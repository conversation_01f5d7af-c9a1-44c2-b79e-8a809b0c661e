import type { Meta, StoryObj } from '@storybook/react';

import { faFileCode } from '@fortawesome/pro-regular-svg-icons/faFileCode';

import { Button } from './Button';

// More on default export: https://storybook.js.org/docs/react/writing-stories/introduction#default-export
export default {
    title: 'Web/Button',
    component: Button,
    // More on argTypes: https://storybook.js.org/docs/react/api/argtypes
    argTypes: {
        backgroundColor: { control: 'color' },
    },
    // enables snapshotting for the component
    parameters: {
        chromatic: { disableSnapshot: false },
    },
} as Meta<typeof Button>;

type Story = StoryObj<typeof Button>;

// For rendering a button next to a disabled version of itself
const DualButtonRender = (args: object) => (
    <div>
        <Button {...args} style={{ marginRight: '8px' }} />
        <Button {...args} disabled />
    </div>
);

export const Primary: Story = {
    // More on args: https://storybook.js.org/docs/react/writing-stories/args
    args: {
        children: 'Primary button',
    },
    render: DualButtonRender,
};

export const Secondary: Story = {
    args: {
        children: 'Secondary button',
        variant: 'secondary',
    },
    render: DualButtonRender,
};

export const Outline: Story = {
    args: {
        children: 'Outline button',
        variant: 'outline',
    },
    render: DualButtonRender,
};

export const Destructive: Story = {
    args: {
        children: 'Destructive button',
        variant: 'destructive',
    },
    render: DualButtonRender,
};

export const DestructiveSecondary: Story = {
    args: {
        children: 'Destructive secondary button',
        variant: 'destructive-secondary',
    },
    render: DualButtonRender,
};

export const DestructiveOutline: Story = {
    args: {
        children: 'Destructive outline button',
        variant: 'destructive-outline',
    },
    render: DualButtonRender,
};

export const AsLink: Story = {
    args: {
        as: 'link',
        children: 'I look like a link',
    },
};

export const SecondaryWithIcon: Story = {
    args: {
        variant: 'secondary',
        children: <span>gulpfile.js:8-9 in a6d199e</span>,
        icon: faFileCode,
    },
};

export const SecondaryWithIconCode: Story = {
    args: {
        variant: 'secondary',
        children: (
            <span>
                <span className="font-source">gulpfile.js:8-9 in a6d199e</span> in <b>a6d199e</b>
            </span>
        ),
        icon: faFileCode,
    },
};
