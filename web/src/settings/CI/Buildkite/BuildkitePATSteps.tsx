import { useMemo } from 'react';
import { Link } from 'react-router-dom';

import { API } from '@shared/api';

import { useAsyncOperation } from '@shared/hooks/UseAsyncOperation';
import { OrderedStep, OrderedStepsList } from '@shared/webComponents/OrderedStepsList/OrderedStepsList';
import { CopyInput } from '@shared/webComponents/TextInput/CopyInput';
import { Loading, TextInput } from '@web/components';
import { Chip } from '@web/components/Chip/Chip';

import './BuildkitePATSteps.scss';

interface Props {
    token: string;
    setToken: (token: string) => void;
    error?: string;
}

export const BuildkitePATSteps = ({ token, setToken, error }: Props) => {
    const [result] = useAsyncOperation(() => API.config.getMetaConfig(), []);
    const metaResponse = useMemo(() => {
        if (!result || !result.ok) {
            return undefined;
        }
        return result.value;
    }, [result]);

    const serviceAddresses = metaResponse?.serviceAddresses;
    if (!serviceAddresses) {
        return <Loading centerAlign />;
    }
    return (
        <OrderedStepsList className="build_kite_pat_steps">
            <OrderedStep index={1} title="Create a Personal Access Token">
                <ul>
                    <li>
                        Go to Buildkite to&nbsp;
                        <Link to="https://buildkite.com/user/api-access-tokens" target="_blank" rel="noreferrer">
                            create a personal access token
                        </Link>
                        .
                    </li>
                    <li>
                        Select the following API Scopes:
                        <ul>
                            <li>
                                <Chip variant="code">read_build_logs</Chip>
                            </li>
                            <li>
                                <Chip variant="code">read_builds</Chip>
                            </li>
                            <li>
                                <Chip variant="code">read_organizations</Chip>
                            </li>
                            <li>
                                <Chip variant="code">read_pipelines</Chip>
                            </li>
                            <li>
                                <Chip variant="code">read_user</Chip>
                            </li>
                        </ul>
                    </li>
                    <li>
                        Copy and paste the IP ranges below into the&nbsp;<b>Allowed IP Addresses</b>&nbsp; field.
                    </li>
                    <div className="address_section">
                        <CopyInput fullWidth value={serviceAddresses.join(' ')} />
                    </div>
                    <li>
                        Click&nbsp;<b>Create New API Access Token</b>.
                    </li>
                </ul>
            </OrderedStep>
            <OrderedStep index={2} title="Add your Personal Access Token">
                <p>Copy your new token and paste it below:</p>
                <TextInput fullWidth value={token} onValueChange={setToken} placeholder="Your Token" />
                {error ? <div className="build_kite_pat_steps__error">{error}</div> : null}
            </OrderedStep>
        </OrderedStepsList>
    );
};
