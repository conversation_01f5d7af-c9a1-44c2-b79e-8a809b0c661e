import classNames from 'classnames';
import { useCallback, useState } from 'react';

import { getApiError } from '@shared/api';

import { CIProjectStore } from '@shared/stores/CIProjectStore';
import { useDialogContext } from '@shared/webComponents/Modal/ModalContext';
import { TwoEqualButtons } from '@shared/webComponents/Modal/ModalDialogButtons/DialogButtons';
import { CIProvider, CIProviderTraitsUtil } from '@shared/webUtils/CIProviderTraits/CIProviderTraitsUtil';
import { Dialog, TextInput } from '@web/components';

import './CISettingsAddProjectSlugDialog.scss';

interface Props {
    store: CIProjectStore;
    provider: CIProvider;
}

export const CISettingsProjectSlugDialog = ({ store, provider }: Props) => {
    const ciTraits = CIProviderTraitsUtil.get(provider);
    const { closeModal } = useDialogContext();
    const label = ciTraits.projectsLabelCounts?.singular ?? 'Project';

    const [slug, setSlug] = useState<string>('');
    const [slugError, setSlugError] = useState<string | undefined>(undefined);

    const addSlug = useCallback(
        async (slug: string) => {
            if (!slug.length) {
                return;
            }
            try {
                setSlugError(undefined);
                await store.addProjectBySlug(slug);
                closeModal();
            } catch (e) {
                const apiError = await getApiError(e);
                setSlugError(apiError?.error.detail ?? 'There was an issue adding your project slug.');
            }
        },
        [store, closeModal]
    );

    const classes = classNames({
        ci_settings_add_project_slug_dialog: true,
    });

    return (
        <Dialog
            className={classes}
            title={`Add a ${label}`}
            description={`Unblocked can triage CI for the repositories associated with each ${label} you add.`}
            size="x-wide"
            padding="separate"
            buttons={
                <TwoEqualButtons
                    primary={{
                        children: 'Add Project',
                        onClickPromise: async () => {
                            addSlug(slug);
                        },
                        overrideClose: true,
                        disabled: slug.length === 0,
                    }}
                />
            }
        >
            <AddSlugSection
                slug={slug}
                setSlug={setSlug}
                addSlug={async () => {
                    if (slug.length > 0) {
                        setSlug(slug);
                    }
                }}
                error={slugError}
            />
        </Dialog>
    );
};

const AddSlugSection = ({
    slug,
    setSlug,
    addSlug,
    error,
    placeholder,
}: {
    slug: string;
    setSlug: (slug: string) => void;
    addSlug?: () => Promise<void>;
    error?: string;
    placeholder?: string;
}) => {
    return (
        <div className="ci_settings_project_slug_dialog__add">
            <h3>Paste your project slug to add it to Unblocked</h3>
            <div className="ci_settings_project_slug_dialog__add_input">
                <TextInput
                    fullWidth
                    value={slug}
                    onValueChange={setSlug}
                    placeholder={placeholder ?? 'Paste your slug'}
                    onEnter={addSlug}
                />
            </div>
            {error ? <div className="ci_settings_project_slug_dialog__error">{error}</div> : null}
        </div>
    );
};
