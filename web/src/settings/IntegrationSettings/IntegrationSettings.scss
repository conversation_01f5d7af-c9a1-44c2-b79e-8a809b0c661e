@use 'layout' as *;
@use 'layout-mixin' as *;
@use 'flex' as *;
@use 'fonts' as *;
@use 'fonts-mixin' as *;
@use 'misc' as *;
@use 'theme' as *;

.integration_settings__header_container {
    @include flex-center-between;
    @include fill-available;

    .integration_settings__dropdown {
        .integration_settings__dropdown_header {
            color: themed($link);
        }

        .dropdown__item {
            color: themed($text);
        }
    }
}

.integration_settings__header {
    div {
        color: themed($text-secondary);
    }
}

.integration_settings {
    .integration_settings__subheader {
        margin-bottom: $spacer-8;
    }

    .integration_settings__connected {
        padding: $spacer-24;
        container-type: inline-size;
    }

    .integration_settings__unconnected {
        padding: $spacer-24;
    }

    .unconnected_section {
        margin: $spacer-32 0;
        scroll-margin-top: 120px;
        container-type: inline-size;
        scroll-behavior: smooth;

        &:first-of-type {
            margin-top: 0;
        }

        .unconnected_section__header {
            text-transform: uppercase;
            color: themed($text-secondary);
            margin-bottom: $spacer-12;
        }
    }
}

.integration_settings__filters_section {
    @include flex-center-between;

    gap: $spacer-12;
    padding: $spacer-12 $spacer-24;
}

.integration_settings__integration_filters {
    display: flex;
    gap: $spacer-8 $spacer-10;
    flex-wrap: wrap;

    .integration_settings__integration_filter {
        color: themed($link);
        transition: all 0.2s ease-in-out;
    }
}

.integrations_settings_navigator__add_preset {
    color: themed($link);
}
