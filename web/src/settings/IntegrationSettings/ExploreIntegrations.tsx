import { useEffect, useMemo, useState } from 'react';

import { Provider } from '@shared/api/generatedApi';

import { useStream } from '@shared/stores/DataCacheStream';
import { FeatureSettingsStoreTraits } from '@shared/stores/FeatureSettingsStoreType';
import { IntegrationsStore } from '@shared/stores/IntegrationsStore';
import { UpsellStore } from '@shared/stores/UpsellStore';
import { useStore } from '@shared/stores/useStore';
import { useModalContext } from '@shared/webComponents/Modal/ModalContext';
import { Sorter } from '@shared/webUtils';
import { ArrayUtils } from '@shared/webUtils/collection/ArrayUtils';
import { IntegrationClassSetting, translateProvidersToClassSettings } from '@shared/webUtils/IntegrationProviderUtils';
import {
    ClientIntegrationType,
    getClientIntegrationTypeLabel,
    getProviderDisplayName,
    supportsMultiProvider,
} from '@shared/webUtils/ProviderUtils';
import { Loading, Pill } from '@web/components';
import { Banner } from '@web/components/Banner/Banner';
import { Button } from '@web/components/Button/Button';
import { IntegrationTileGrid } from '@web/components/IntegrationTile/IntegrationTile';
import { TeamAdminsDialog } from '@web/components/Team/TeamAdminsDialog';
import { useTeamContext } from '@web/components/Team/TeamContext';
import { useDocumentTitle } from '@web/hooks/useDocumentTitle';

import { useSettingsContext } from '../SettingsContext';
import { useSettingsOutletContext } from '../SettingsOutletContext';

import './IntegrationSettings.scss';

const integrationsClassSorter = Sorter.makeSortFn<IntegrationClassSetting>([
    Sorter.byValue((integration) => getIntegrationClassSortValue(integration.$case)),
    Sorter.byValue((integration) => getIntegrationSortValue(integration.provider)),
    Sorter.byValue((integration) => getProviderDisplayName(integration.provider)),
]);

const getIntegrationClassSortValue = (type: ClientIntegrationType): number => {
    switch (type) {
        case 'source':
            return 0;
        case 'continuousIntegration':
            return 1;
        case 'documentation':
            return 2;
        case 'issueTracker':
            return 3;
        case 'messaging':
            return 4;
        case 'invalid':
            return 5;
    }
};

const getIntegrationSortValue = (provider: Provider): number => {
    switch (provider) {
        // Custom Data Source is displayed after every other Documentation tile
        case Provider.CustomIntegration:
            return 1;

        // Other tiles are sorted alphabetically
        default:
            return 0;
    }
};

export const ExploreIntegrations = () => {
    const { currentTeamId, currentTeam } = useTeamContext();
    const { teamIntegrations } = useSettingsContext();
    const { openModal } = useModalContext();
    const { setSettingsPinnedContent, setSettingsHeader } = useSettingsOutletContext();

    const [filter, setFilter] = useState<ClientIntegrationType>();

    const featureSettingsStore = useStore(FeatureSettingsStoreTraits, { teamId: currentTeamId });
    const featureSettings = useStream(() => featureSettingsStore.stream, [featureSettingsStore], { $case: 'loading' });
    const upsellState = useStream(() => UpsellStore.get(currentTeamId).stream, [currentTeamId], { $case: 'loading' });

    const integrationsState = useStream(() => IntegrationsStore.get(currentTeamId).integrations, [currentTeamId], {
        $case: 'loading',
    });

    const allIntegrations: IntegrationClassSetting[] = useMemo(() => {
        const integrationSettings: IntegrationClassSetting[] = ArrayUtils.compact([
            ...translateProvidersToClassSettings(
                [...teamIntegrations.integrations.values()],
                (provider) => !!teamIntegrations.installations.get(provider),
                upsellState
            ),
        ]);

        return integrationSettings.filter(({ provider, isConnected }) => {
            if (supportsMultiProvider(provider)) {
                return true;
            }
            return !isConnected;
        });
    }, [teamIntegrations, upsellState]);

    useDocumentTitle(() => 'Add a Data Source', []);

    useEffect(() => {
        setSettingsHeader(
            <div className="integration_settings__header">
                <h2>Add a Data Source</h2>
                <div>Get better answers with every source you connect.</div>
            </div>
        );

        return () => {
            setSettingsHeader(null);
        };
    }, [setSettingsHeader]);

    useEffect(() => {
        const displayedFilters = ArrayUtils.distinct(
            allIntegrations.sort(integrationsClassSorter).map(({ $case }) => $case)
        );

        // if there's only ever one type, don't render filter section
        if (displayedFilters.length === 1 && !filter) {
            return;
        }

        setSettingsPinnedContent(
            <div className="integration_settings__filters_section">
                <div className="integration_settings__integration_filters">
                    <Pill
                        className="integration_settings__integration_filter"
                        title={`Show all data sources`}
                        key={-1}
                        selected={!filter}
                        onClick={() => setFilter(undefined)}
                    >
                        All
                    </Pill>

                    {displayedFilters.map((integrationType, idx) => {
                        const isTypeSelected = filter === integrationType;
                        const label = getClientIntegrationTypeLabel(integrationType);

                        return (
                            <Pill
                                className="integration_settings__integration_filter"
                                title={`Filter by ${label}`}
                                key={idx}
                                selected={isTypeSelected}
                                onClick={() => setFilter(integrationType)}
                            >
                                {label}
                            </Pill>
                        );
                    })}
                </div>
            </div>
        );
        return () => setSettingsPinnedContent(null);
    }, [currentTeamId, allIntegrations, setSettingsPinnedContent, filter]);

    const displayedIntegrations = useMemo(() => {
        if (!filter) {
            return allIntegrations;
        }

        return allIntegrations.filter(({ $case }) => filter === $case);
    }, [allIntegrations, filter]);

    const unconnectedIntegrationsByType = useMemo(() => {
        const sorted = displayedIntegrations.sort(integrationsClassSorter);
        return ArrayUtils.groupByMap(sorted, (integrationSettingType) => integrationSettingType?.$case);
    }, [displayedIntegrations]);

    const connectionRestrictedBanner = useMemo(() => {
        const showBanner = featureSettings?.$case === 'ready' && !featureSettings.isAllowedToUpdate;

        if (!showBanner) {
            return null;
        }

        return (
            <Banner
                floating
                variant="unblocked"
                actions={
                    <Button
                        variant="secondary"
                        size="tight"
                        onClick={() => openModal(<TeamAdminsDialog team={currentTeam} />)}
                    >
                        View Admins
                    </Button>
                }
            >
                Only admins can connect new data sources.
            </Banner>
        );
    }, [featureSettings, openModal, currentTeam]);

    if (
        featureSettings.$case === 'loading' ||
        upsellState.$case === 'loading' ||
        integrationsState.$case === 'loading'
    ) {
        return <Loading centerAlign />;
    }

    const integrationsMap = new Map(integrationsState.value.map((integration) => [integration.provider, integration]));

    return (
        <div className="integration_settings__unconnected">
            {connectionRestrictedBanner}
            {[...unconnectedIntegrationsByType].map(([type, unconnected], idx) => {
                return (
                    <div className="unconnected_section" key={idx} id={type}>
                        <div className="unconnected_section__header">{getClientIntegrationTypeLabel(type)}</div>

                        <IntegrationTileGrid integrations={unconnected} integrationsMap={integrationsMap} />
                    </div>
                );
            })}
        </div>
    );
};
