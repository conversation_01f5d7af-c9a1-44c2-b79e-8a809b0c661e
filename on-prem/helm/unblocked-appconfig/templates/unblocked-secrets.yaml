apiVersion: v1
kind: Secret
metadata:
  name: unblocked-secrets-all
  annotations:
    checksum/secrets: {{ .Values.global.unblocked.secrets | toJson | sha256sum }}
type: Opaque
stringData:
  # Infra secrets
  AWS_ACCESS_KEY_ID: {{ required "global.unblocked.secrets.AWS_ACCESS_KEY_ID is required" .Values.global.unblocked.secrets.AWS_ACCESS_KEY_ID | quote }}
  AWS_SECRET_ACCESS_KEY: {{ required "global.unblocked.secrets.AWS_SECRET_ACCESS_KEY is required" .Values.global.unblocked.secrets.AWS_SECRET_ACCESS_KEY | quote }}
  DATABASE_PASSWORD: {{ required "global.unblocked.secrets.DATABASE_PASSWORD is required" .Values.global.unblocked.secrets.DATABASE_PASSWORD | quote }}
  {{- if not .Values.global.redis.deployInCluster }}
  REDIS_PASSWORD: {{ required "global.unblocked.secrets.REDIS_PASSWORD is required" .Values.global.unblocked.secrets.REDIS_PASSWORD | quote }}
  {{- end }}
  {{- if not .Values.global.activemq.deployInCluster }}
  ACTIVEMQ_PASSWORD: {{ required "global.unblocked.secrets.ACTIVEMQ_PASSWORD is required" .Values.global.unblocked.secrets.ACTIVEMQ_PASSWORD | quote }}
  {{- end }}

  # App secrets
  ENCRYPTION_USER_SECRETS_4096_PRIVATE_KEY: {{ .Values.global.unblocked.secrets.ENCRYPTION_USER_SECRETS_4096_PRIVATE_KEY | default "" | quote }}
  ENCRYPTION_USER_SECRETS_AES_KEY: {{ .Values.global.unblocked.secrets.ENCRYPTION_USER_SECRETS_AES_KEY | default "" | quote }}
  ADMIN_TOKEN_PRIVATE_KEY: {{ .Values.global.unblocked.secrets.ADMIN_TOKEN_PRIVATE_KEY | default "" | quote }}
  ADMIN_TOKEN_PUBLIC_KEY: {{ .Values.global.unblocked.secrets.ADMIN_TOKEN_PUBLIC_KEY | default "" | quote }}
  EMBEDDING_CONTENT_ENCRYPTION_KEY: {{ .Values.global.unblocked.secrets.EMBEDDING_CONTENT_ENCRYPTION_KEY | default "" | quote }}
  DOCUMENT_CONTENT_ENCRYPTION_KEY: {{ .Values.global.unblocked.secrets.DOCUMENT_CONTENT_ENCRYPTION_KEY | default "" | quote }}
  GITHUB_APP_KEY: {{ .Values.global.unblocked.secrets.GITHUB_APP_KEY | default "" | quote }}
  GITHUB_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.GITHUB_CLIENT_SECRET | default "" | quote }}
  GITHUB_WEBHOOK_SECRET: {{ .Values.global.unblocked.secrets.GITHUB_WEBHOOK_SECRET | default "" | quote }}
  GITHUB_ENTERPRISE_AES_KEY: {{ .Values.global.unblocked.secrets.GITHUB_ENTERPRISE_AES_KEY | default "" | quote }}
  AUTH_TOKEN_PRIVATE_KEY: {{ .Values.global.unblocked.secrets.AUTH_TOKEN_PRIVATE_KEY | default "" | quote }}
  AUTH_TOKEN_PUBLIC_KEY: {{ .Values.global.unblocked.secrets.AUTH_TOKEN_PUBLIC_KEY | default "" | quote }}
  ENCRYPTION_USER_SECRETS_4096_PUBLIC_KEY: {{ .Values.global.unblocked.secrets.ENCRYPTION_USER_SECRETS_4096_PUBLIC_KEY | default "" | quote }}
  GOOGLE_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.GOOGLE_CLIENT_SECRET | default "" | quote }}
  SLACK_BOT_AUTH_TOKEN: {{ .Values.global.unblocked.secrets.SLACK_BOT_AUTH_TOKEN | default "" | quote }}
  SLACK_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.SLACK_CLIENT_SECRET | default "" | quote }}
  SLACK_SIGNING_SECRET: {{ .Values.global.unblocked.secrets.SLACK_SIGNING_SECRET | default "" | quote }}
  MERMAID_HMAC_SECRET: {{ .Values.global.unblocked.secrets.MERMAID_HMAC_SECRET | default "" | quote }}
  SHARED_APP_SECRET_PUBLIC_KEY: {{ .Values.global.unblocked.secrets.SHARED_APP_SECRET_PUBLIC_KEY | default "" | quote }}
  OPENSEARCH_PASSWORD: {{ .Values.global.unblocked.secrets.OPENSEARCH_PASSWORD | default "" | quote }}

  # Optional secrets
  OPENROUTER_API_KEY: {{ .Values.global.unblocked.secrets.OPENROUTER_API_KEY | default "" | quote }}
  GEMINI_API_KEY: {{ .Values.global.unblocked.secrets.GEMINI_API_KEY | default "" | quote }}
  AZURE_DEVOPS_APP_SECRET: {{ .Values.global.unblocked.secrets.AZURE_DEVOPS_APP_SECRET | default "" | quote }}
  AZURE_DEVOPS_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.AZURE_DEVOPS_CLIENT_SECRET | default "" | quote }}
  BITBUCKET_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.BITBUCKET_CLIENT_SECRET | default "" | quote }}
  GITLAB_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.GITLAB_CLIENT_SECRET | default "" | quote }}
  GITLAB_SELF_HOSTED_APP_KEY: {{ .Values.global.unblocked.secrets.GITLAB_SELF_HOSTED_APP_KEY | default "" | quote }}
  GITHUB_ACTIONS_SECRET: {{ .Values.global.unblocked.secrets.GITHUB_ACTIONS_SECRET | default "" | quote }}
  ANTHROPIC_API_KEY: {{ .Values.global.unblocked.secrets.ANTHROPIC_API_KEY | default "" | quote }}
  AZURE_CNE_OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.AZURE_CNE_OPENAI_API_KEY | default "" | quote }}
  AZURE_EUS2_OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.AZURE_EUS2_OPENAI_API_KEY | default "" | quote }}
  AZURE_EUS_OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.AZURE_EUS_OPENAI_API_KEY | default "" | quote }}
  AZURE_NCUS_OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.AZURE_NCUS_OPENAI_API_KEY | default "" | quote }}
  AZURE_USW_OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.AZURE_USW_OPENAI_API_KEY | default "" | quote }}
  COHERE_API_KEY: {{ .Values.global.unblocked.secrets.COHERE_API_KEY | default "" | quote }}
  CONFLUENCE_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.CONFLUENCE_CLIENT_SECRET | default "" | quote }}
  HONEYCOMB_API_KEY: {{ .Values.global.unblocked.secrets.HONEYCOMB_API_KEY | default "" | quote }}
  INTERCOM_API_KEY: {{ .Values.global.unblocked.secrets.INTERCOM_API_KEY | default "" | quote }}
  INTERCOM_IDENTITY_VERIFICATION_SECRET: {{ .Values.global.unblocked.secrets.INTERCOM_IDENTITY_VERIFICATION_SECRET | default "" | quote }}
  JIRA_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.JIRA_CLIENT_SECRET | default "" | quote }}
  LINEAR_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.LINEAR_CLIENT_SECRET | default "" | quote }}
  LINEAR_SIGNING_SECRET: {{ .Values.global.unblocked.secrets.LINEAR_SIGNING_SECRET | default "" | quote }}
  LOGZIO_TOKEN: {{ .Values.global.unblocked.secrets.LOGZIO_TOKEN | default "" | quote }}
  NOTION_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.NOTION_CLIENT_SECRET | default "" | quote }}
  NOTION_SIGNING_SECRET: {{ .Values.global.unblocked.secrets.NOTION_SIGNING_SECRET | default "" | quote }}
  OPENAI_API_KEY: {{ .Values.global.unblocked.secrets.OPENAI_API_KEY | default "" | quote }}
  PINECONE_API_ENV_KEY: {{ .Values.global.unblocked.secrets.PINECONE_API_ENV_KEY | default "" | quote }}
  SENDGRID_API_KEY: {{ .Values.global.unblocked.secrets.SENDGRID_API_KEY | default "" | quote }}
  SLACK_UAT_CLIENT_SECRET: {{ .Values.global.unblocked.secrets.SLACK_UAT_CLIENT_SECRET | default "" | quote }}
  SLACK_UAT_SIGNING_SECRET: {{ .Values.global.unblocked.secrets.SLACK_UAT_SIGNING_SECRET | default "" | quote }}
  STATSIG_API_KEY: {{ .Values.global.unblocked.secrets.STATSIG_API_KEY | default "" | quote }}
  SEGMENT_API_KEY: {{ .Values.global.unblocked.secrets.SEGMENT_API_KEY | default "" | quote }}
  STRIPE_API_KEY: {{ .Values.global.unblocked.secrets.STRIPE_API_KEY | default "" | quote }}
  STRIPE_SIGNING_SECRET: {{ .Values.global.unblocked.secrets.STRIPE_SIGNING_SECRET | default "" | quote }}
  PREFECT_SLACK_CODE_INGESTION_FLOW_WEBHOOK_URL: {{ .Values.global.unblocked.secrets.PREFECT_SLACK_CODE_INGESTION_FLOW_WEBHOOK_URL | default "" | quote }}
  CI_SECRETS_PRIVATE_KEY: {{ .Values.global.unblocked.secrets.CI_SECRETS_PRIVATE_KEY | default "" | quote }}
  CI_AUTH_TOKEN_PRIVATE_KEY: {{ .Values.global.unblocked.secrets.CI_AUTH_TOKEN_PRIVATE_KEY | default "" | quote }}
  CI_AUTH_TOKEN_PUB_KEY: {{ .Values.global.unblocked.secrets.CI_AUTH_TOKEN_PUB_KEY | default "" | quote }}





