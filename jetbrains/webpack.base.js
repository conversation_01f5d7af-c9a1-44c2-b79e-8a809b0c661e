const path = require('path');
const { WebpackManifestPlugin } = require('webpack-manifest-plugin');
const { merge } = require('webpack-merge');
const webpack = require('webpack');
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const ForkTsCheckerWebpackPlugin = require('fork-ts-checker-webpack-plugin');

const gitRevisionPlugin = new GitRevisionPlugin();

const baseConfig = (env) => {
    return {
        context: path.resolve(__dirname, '.'),

        output: {
            path: path.resolve(__dirname, 'dist'),
            filename: '[name].js',
        },

        resolve: {
            alias: {
                '@jetbrain': path.resolve(__dirname, 'src'),
                '@shared': path.resolve(__dirname, '../shared'),
                '@ide': path.resolve(__dirname, '../shared/ide'),
                '@node': path.resolve(__dirname, '../shared/node'),
                '@clientAssets': path.resolve(__dirname, '../shared/clientAssets'),
                '@api': path.resolve(__dirname, 'src/api'),
                '@agent': path.resolve(__dirname, 'src/agent'),
                '@auth': path.resolve(__dirname, 'src/auth'),
                '@components': path.resolve(__dirname, 'src/components'),
                '@models': path.resolve(__dirname, 'src/api/models'),
                '@shared-api': path.resolve(__dirname, '../shared/api'),
                '@shared-metrics': path.resolve(__dirname, '../shared/metrics'),
                '@shared-mocks': path.resolve(__dirname, '../shared/mocks'),
                '@shared-node-git': path.resolve(__dirname, '../shared/node/git'),
                '@shared-node-process': path.resolve(__dirname, '../shared/node/process'),
                '@shared-node-sourcemark': path.resolve(__dirname, '../shared/node/sourcemark'),
                '@shared-proxy': path.resolve(__dirname, '../shared/proxy'),
                '@shared-styles': path.resolve(__dirname, '../shared/webComponents/styles'),
                '@shared-video': path.resolve(__dirname, '../shared/video'),
                '@shared-web-utils': path.resolve(__dirname, '../shared/webUtils'),
                '@shared-ide-components': path.resolve(__dirname, '../shared/ide/components'),
                '@shared-ide-webview': path.resolve(__dirname, '../shared/ide/webview'),
                '@shared-ide-sidebar': path.resolve(__dirname, '../shared/ide/sidebar'),
                '@shared-ide-utils': path.resolve(__dirname, '../shared/ide/utils'),
            },
            extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
            fallback: {
                os: false,
                zlib: false,
                http: false,
                https: false,
            },
        },

        stats: {
            children: true,
        },

        module: {
            rules: [
                {
                    test: /\.s?css$/,
                    use: [
                        'style-loader',
                        'css-loader',
                        'postcss-loader',
                        {
                            loader: 'sass-loader',
                            options: {
                                sassOptions: {
                                    includePaths: ['src/styles'],
                                },
                            },
                        },
                    ],
                },

                {
                    test: /\.tsx?$/,
                    loader: 'esbuild-loader',
                    options: {
                        jsx: 'automatic',
                    },
                },

                {
                    test: /\.(png|jpg|jpeg|gif|webm|mp4|mov|woff|woff2)$/i,
                    type: 'asset/resource',
                },

                {
                    test: /\.(svg)$/i,
                    type: 'asset/resource',
                },
            ],
        },

        plugins: [
            new webpack.DefinePlugin({
                COMMIT_SHA: JSON.stringify(gitRevisionPlugin.commithash()),
                APP_TYPE: JSON.stringify('intellij'),
                PRODUCT_NUMBER: JSON.stringify(env.PRODUCT_NUMBER),
                PRODUCT_VERSION: JSON.stringify(env.PRODUCT_VERSION),
                // @prezly/slate/sdk issue with webviews
                'nodeUrl.URL': 'nodeUrl.Url',
            }),
            new ForkTsCheckerWebpackPlugin(),
        ],
    };
};

const extensionConfig = {
    target: ['node'],
    entry: {
        extension: './src/agent/agent.ts',
        getenv: './src/agent/getenv.ts',
    },
};

const webviewConfig = {
    context: path.resolve(__dirname, '.'),
    entry: {
        LeftSidebarWebview: './src/webview/LeftSidebarWebview.tsx',
        RightSidebarWebview: './src/webview/RightSidebarWebview.tsx',
        EditorWebview: './src/webview/EditorWebview.tsx',

        // Temporary: Build specialized webviews for PR and Docs left sidebar
        LeftExplorerPRSidebarWebview: './src/webview/LeftExplorerPRSidebarWebview.tsx',
        LeftExplorerDocsSidebarWebview: './src/webview/LeftExplorerDocsSidebarWebview.tsx',
    },
    resolve: {
        fallback: {
            os: false,
            zlib: false,
            http: false,
            https: false,
            path: false,
            fs: false,
            process: false,
            child_process: false,
        },
    },
    plugins: [
        new webpack.ProvidePlugin({
            process: 'process/browser',
            Buffer: ['buffer', 'Buffer'],
            setImmediate: ['timers-browserify', 'setImmediate'],
        }),
    ],
};

module.exports = (env, args) => {
    return [merge(baseConfig(env), extensionConfig), merge(baseConfig(env), webviewConfig)];
};
