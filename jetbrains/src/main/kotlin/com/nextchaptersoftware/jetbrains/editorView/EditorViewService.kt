package com.nextchaptersoftware.jetbrains.editorView

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.Service
import com.intellij.openapi.project.DumbAware
import com.intellij.openapi.project.Project
import com.nextchaptersoftware.jetbrains.services.OpenFileService
import com.nextchaptersoftware.jetbrains.util.ProjectCoroutineScope
import com.nextchaptersoftware.jetbrains.util.serviceOrThrow
import com.nextchaptersoftware.jetbrains.webview.WebviewController
import com.nextchaptersoftware.jetbrains.webview.WebviewVirtualFile
import java.util.UUID

@Service(Service.Level.PROJECT)
const val EDITOR_BUNDLE_NAME = "EditorWebview"

class EditorViewService(
    override val project: Project,
) : DumbAware, ProjectCoroutineScope {
    private val constantId = UUID.randomUUID()

    object EditorViewId {
        const val ID = "EditorViewRouter"
    }

    fun launchView() {
        ApplicationManager.getApplication().invokeAndWait {
            val openFileService = project.serviceOrThrow<OpenFileService>()
            val controller = WebviewController(project, EditorViewId.ID)
            val webviewFile = WebviewVirtualFile(controller, "Installation", constantId)
            if (!openFileService.isFileOpen(webviewFile)) {
                openFileService.openFile(webviewFile, null)
            }
        }
    }
}
