syntax = "proto3";

option java_multiple_files = true;
option java_package = "com.nextchaptersoftware.common.ideagent";
option java_outer_classname = "IDEAgent";

import "google/protobuf/wrappers.proto";

import "ClientType.proto";

package ideAgent;

enum ThemeMode {
  Light = 0;
  Dark = 1;
}

message InitializeRequest {
  ClientType clientType = 1;
  ThemeMode themeMode = 2;
  optional string name = 3;
}

message InitializeResponse {}

enum HeartbeatState {
  Running = 0;
  Shutdown = 1;
}

message HeartbeatRequest {
  HeartbeatState state = 1;
}

message HeartbeatResponse {}

message RepoRemote {
  string name = 1;
  string url = 2;
}

message Repo {
  string root = 1;
  repeated RepoRemote remotes = 2;
}

message UpdateReposRequest {
  repeated Repo repos = 1;
}

message UpdateReposResponse {}

message EditorVisibleRange {
  int32 startLine = 1;
  int32 endLine = 2;
}

message EditorSelectedRange {
  int32 startLine = 1;
  int32 endLine = 2;
  int32 startColumn = 3;
  int32 endColumn = 4;
}

message Editor {
  string filePath = 1;
  bool isActive = 2;
  bool isVisible = 3;
  optional EditorVisibleRange visibleRange = 4;
  optional EditorSelectedRange selectedRange = 5;
}

message UpdateEditorsRequest {
  repeated Editor editors = 1;
}

message UpdateEditorsResponse {}

enum SidebarState {
  // This sidebar should not be displayed
  empty = 0;

  // The sidebar is ready to be displayed, using the view router
  ready = 1;

  // Temporary: specialized state for explorer sidebar,
  // so we render two copies as separate tabs
  explorer = 2;
}

message SidebarRequest {}

message SidebarResponse {
  SidebarState state = 1;
}

message AuthRequest {}
message AuthResponse {
  bool isAuthenticated = 1;
}

message FileSourceMarksRequest {
  string filePath = 1;
}

message FileSourceMarksResponse {
  string payload = 1; // SourceMarkInfos
}

message SearchInsightsRequest {
  string query = 1;
}

message SearchInsightsResponse {
  string payload = 1; // SearchInsightsResponse
}

message AgentCommandOpenThread {
  string teamId = 1;
  optional string repoId = 2;
  string threadId = 3;
  bool shouldScroll = 4;
  bool isGutter = 5;
}

message AgentCommandOpenPr {
  string teamId = 1;
  string repoId = 2;
  string prId = 3;
}

message AgentCommandLogout {}

message AgentCommandSetWindowFocus {
  bool focused = 1;
}

message AgentCommandAskQuestion {}

message AgentCommandMyQuestions {}

message AgentCommandRequest {
  oneof command {
    AgentCommandOpenThread openThread = 1;
    AgentCommandOpenPr openPr = 2;
    AgentCommandLogout logout = 3;
    AgentCommandSetWindowFocus setWindowFocus = 5;
    AgentCommandAskQuestion askQuestion = 6;
    AgentCommandMyQuestions myQuestions = 7;
  }
}

message AgentCommandResponse {}

message PluginCommandOpenBrowser {
  string url = 1;
}

message PluginCommandNotifyPluginUpdate {}

message PluginCommandRestartIDE {}

message PluginShowMarketplaceUI {}

message PluginDownloadSvg {
  string svg = 1;
  string fileName = 2;
}

message PluginOpenRightSidebar {}

message PluginOpenLeftSidebar {}

message PluginOpenEditor {}

message PluginCommandRequest {
  oneof command {
    PluginCommandOpenBrowser openBrowser = 1;
    PluginCommandNotifyPluginUpdate notifyPluginUpdate = 2;
    PluginCommandRestartIDE restartIDE = 3;
    PluginShowMarketplaceUI showMarketplaceUI = 4;
    PluginDownloadSvg downloadSvg = 5;
    PluginOpenRightSidebar openRightSidebar = 6;
    PluginOpenLeftSidebar openLeftSidebar = 7;
    PluginOpenEditor openEditor = 8;
  }
}

message PluginCommandResponse {}

message FocusRange {
  int32 startLine = 1;
  int32 endLine = 2;
  bool shouldHighlight = 3;
  bool shouldScroll = 4;
}

message OpenFileRequest {
  string filePath = 1;
  optional FocusRange range = 2;
}

message OpenFileResponse {}

message ProxyRequest {
  string payload = 1;
}

message ProxyResponse {
  string payload = 1;
}

message UnsavedChangesRequest {
  string filePath = 1;
  bool isDirty = 2;
  optional string fileContent = 3;
}

message UnsavedChangesResponse {}

message WebviewRequestPayload {
  string payload = 1;
}

message WebviewRequestVisibility {
  bool isVisible = 1;
}

message WebviewRequest {
  oneof request {
    WebviewRequestPayload payload = 1;
    WebviewRequestVisibility visibility = 2;
  }
}

message WebviewResponsePayload {
  string payload = 1;
}

message WebviewResponse {
  oneof response {
    WebviewResponsePayload payload = 1;
  }
}

service IDEAgentService {
  rpc initialize(InitializeRequest) returns (InitializeResponse) {}
  rpc heartbeat(stream HeartbeatRequest) returns (stream HeartbeatResponse) {}
  rpc updateRepos(stream UpdateReposRequest) returns (UpdateReposResponse) {}
  rpc updateEditors(stream UpdateEditorsRequest) returns (UpdateEditorsResponse) {}

  rpc webview(stream WebviewRequest) returns (stream WebviewResponse) {}

  rpc authStream(AuthRequest) returns (stream AuthResponse) {}

  rpc runPluginCommand(PluginCommandResponse) returns (stream PluginCommandRequest) {}
  rpc runAgentCommand(AgentCommandRequest) returns (AgentCommandResponse) {}

  rpc notifyUnsavedChanges(UnsavedChangesRequest) returns (UnsavedChangesResponse) {}
  rpc fileSourceMarksStream(FileSourceMarksRequest) returns (stream FileSourceMarksResponse) {}

  rpc searchInsights(SearchInsightsRequest) returns (stream SearchInsightsResponse) {}

  rpc openFile(OpenFileResponse) returns (stream OpenFileRequest) {}

  rpc promiseProxy(stream ProxyResponse) returns (stream ProxyRequest) {}

  rpc leftSidebarStream(SidebarRequest) returns (stream SidebarResponse) {}
  rpc rightSidebarStream(SidebarRequest) returns (stream SidebarResponse) {}
  rpc editorStream(SidebarRequest) returns (stream SidebarResponse) {}
}
