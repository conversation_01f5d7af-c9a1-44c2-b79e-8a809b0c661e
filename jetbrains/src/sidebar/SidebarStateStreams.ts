import { IDEViewRouterStores } from '@shared/ide/ViewRouter/IDEViewRouterStore';
import { ViewRouterViewState } from '@shared/ide/ViewRouter/ViewRouterTypes';
import { LazyValue } from '@shared/webUtils';

import { SidebarResponse, SidebarState } from '../../build/generated/source/proto/main/ts_proto/IDEAgent';

function convertIdeSidebarState(state: ViewRouterViewState): SidebarResponse {
    switch (state.$case) {
        case 'empty':
            return { state: SidebarState.empty };

        case 'explorerInsights':
            return { state: SidebarState.explorer };

        default:
            return { state: SidebarState.ready };
    }
}

export const LeftSidebarStateStream = LazyValue(() =>
    IDEViewRouterStores.get('left').stream.map(convertIdeSidebarState)
);
export const RightSidebarStateStream = LazyValue(() =>
    IDEViewRouterStores.get('right').stream.map(convertIdeSidebarState)
);
export const EditorStateStream = LazyValue(() => IDEViewRouterStores.get('editor').stream.map(convertIdeSidebarState));
