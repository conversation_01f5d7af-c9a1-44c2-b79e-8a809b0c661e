import { Stream } from 'xstream';

import { InsightAggregate as ExtrasInsightAggregate, SourceMarkInfos } from '@shared/api/generatedExtraApi/models';

import {
    sendUnaryData,
    ServerDuplexStream,
    ServerReadableStream,
    ServerUnaryCall,
    ServerWritableStream,
    UntypedHandleCall,
} from '@grpc/grpc-js';
import {
    EditorStateStream,
    LeftSidebarStateStream,
    RightSidebarStateStream,
} from '@jetbrain/sidebar/SidebarStateStreams';
import { WebviewContentController } from '@jetbrain/webview/WebviewContentController';
import { FileSourceMarkStreams } from '@shared/ide/sourcemark';
import { CrossRepoSearchStream } from '@shared/ide/stores/CrossRepoSearchStream';
import { ConnectServerWriteableStream } from '@shared/ide/utils/GrpcUtils';
import { InsightSearchQueryType } from '@shared/stores/InsightSearchStream';
import { ValueStream } from '@shared/stores/NewValueStream';
import { filterReady } from '@shared/stores/StreamOperators';
import { logger } from '@shared/webUtils/log';

import {
    AgentCommandRequest,
    AgentCommandResponse,
    AuthRequest,
    AuthResponse,
    FileSourceMarksRequest,
    FileSourceMarksResponse,
    HeartbeatRequest,
    HeartbeatResponse,
    HeartbeatState,
    IDEAgentServiceServer,
    InitializeRequest,
    InitializeResponse,
    OpenFileRequest,
    OpenFileResponse,
    PluginCommandRequest,
    PluginCommandResponse,
    ProxyRequest,
    ProxyResponse,
    SearchInsightsRequest,
    SearchInsightsResponse,
    SidebarRequest,
    SidebarResponse,
    ThemeMode,
    UnsavedChangesRequest,
    UnsavedChangesResponse,
    UpdateEditorsRequest,
    UpdateEditorsResponse,
    UpdateReposRequest,
    UpdateReposResponse,
    WebviewRequest,
    WebviewResponse,
} from '../../build/generated/source/proto/main/ts_proto/IDEAgent';

export enum HeartbeatEvent {
    // Client has connected
    connect,

    // Client has disconnected, but may re-connect
    disconnect,

    // Client sent a heartbeat
    heartbeat,

    // Client is shutting down, and will not reconect
    shutdown,
}

const log = logger('IDEAgentService');

const generateIDEAgentService = () => {
    const initializationStream = new ValueStream<InitializeRequest>();
    const heartbeatStream = new ValueStream<HeartbeatEvent>();

    let themeMode = ThemeMode.Light;

    const authResponseStream = new ValueStream<AuthResponse | undefined>(undefined);

    const pluginCommandRequestStream = new ValueStream<PluginCommandRequest | undefined>(undefined);
    const openFileRequestStream = new ValueStream<OpenFileRequest>();

    const updateRepoRootsStream = new ValueStream<UpdateReposRequest>();
    const updateEditorsStream = new ValueStream<UpdateEditorsRequest>();

    const unsavedChangesStream = new ValueStream<UnsavedChangesRequest>();

    const agentCommandStream = new ValueStream<AgentCommandRequest>();

    const proxyResponseStream = new ValueStream<ProxyResponse | undefined>();
    const proxyRequestStream = new ValueStream<ProxyRequest | undefined>();

    class IDEAgentService implements IDEAgentServiceServer {
        initialize(
            call: ServerUnaryCall<InitializeRequest, InitializeResponse>,
            callback: sendUnaryData<InitializeResponse>
        ) {
            themeMode = call.request.themeMode;
            initializationStream.value = call.request;
            callback(null, {}, undefined, undefined);
        }

        heartbeat(call: ServerDuplexStream<HeartbeatRequest, HeartbeatResponse>) {
            heartbeatStream.value = HeartbeatEvent.connect;

            call.on('data', (request: HeartbeatRequest) => {
                if (request.state === HeartbeatState.Running) {
                    heartbeatStream.value = HeartbeatEvent.heartbeat;
                } else if (request.state === HeartbeatState.Shutdown) {
                    heartbeatStream.value = HeartbeatEvent.shutdown;
                }
            });

            call.on('end', () => {
                heartbeatStream.value = HeartbeatEvent.disconnect;
            });
        }

        updateRepos(call: ServerReadableStream<UpdateReposRequest, UpdateReposResponse>) {
            call.on('data', (request: UpdateReposRequest) => {
                updateRepoRootsStream.value = request;
            });
        }

        updateEditors(call: ServerReadableStream<UpdateEditorsRequest, UpdateEditorsResponse>) {
            call.on('data', (request: UpdateEditorsRequest) => {
                updateEditorsStream.value = request;
            });
        }

        authStream(call: ServerWritableStream<AuthRequest, AuthResponse>) {
            authResponseStream.subscribe({
                next: (authResponse) => {
                    if (authResponse) {
                        call.write(authResponse);
                    }
                },
            });
        }

        runPluginCommand(call: ServerWritableStream<PluginCommandResponse, PluginCommandRequest>) {
            pluginCommandRequestStream.subscribe({
                next: (request) => {
                    if (request) {
                        call.write(request);
                    }
                },
            });
        }

        fileSourceMarksStream(call: ServerWritableStream<FileSourceMarksRequest, FileSourceMarksResponse>) {
            const subscription = FileSourceMarkStreams.instance
                .getStream(call.request.filePath)
                .compose(filterReady)
                .map((infos): SourceMarkInfos => ({ infos: infos.value }))
                .subscribe({
                    next: (value) => call.write({ payload: JSON.stringify(value) }),
                });

            // Unsubscribe when requested stream is closed or errors out
            call.on('cancelled', () => subscription.unsubscribe());
            call.on('error', () => subscription.unsubscribe());
            call.on('end', () => subscription.unsubscribe());
        }

        searchInsights(call: ServerWritableStream<SearchInsightsRequest, SearchInsightsResponse>) {
            const query: InsightSearchQueryType = { query: call.request.query, sortType: 'relevance' };

            const subscription = CrossRepoSearchStream(Stream.of<InsightSearchQueryType | undefined>(query)).subscribe({
                next: (state) => {
                    if (state.$case === 'loaded') {
                        const values = state.result.map<ExtrasInsightAggregate>((value) => {
                            switch (value.$case) {
                                case 'pr':
                                    return { pr: value.pr };
                                case 'thread':
                                    return { thread: value.threadInfo };
                            }
                        });

                        const payload = JSON.stringify(values);
                        call.write({ payload });

                        // Search only returns a single item for now -- close the stream once we've finished
                        // If we want to return more search items we can continue streaming here
                        call.end();
                        subscription.unsubscribe();
                    }
                },
            });

            // Unsubscribe when requested stream is closed or errors out
            call.on('cancelled', () => subscription.unsubscribe());
            call.on('error', () => subscription.unsubscribe());
            call.on('end', () => subscription.unsubscribe());
        }

        runAgentCommand(
            call: ServerUnaryCall<AgentCommandRequest, AgentCommandResponse>,
            callback: sendUnaryData<AgentCommandResponse>
        ) {
            agentCommandStream.value = call.request;
            callback(null, {}, undefined, undefined);
        }

        openFile(call: ServerWritableStream<OpenFileResponse, OpenFileRequest>) {
            openFileRequestStream.subscribe({
                next: (request) => {
                    call.write(request);
                },
            });
        }

        promiseProxy(call: ServerDuplexStream<ProxyResponse, ProxyRequest>): void {
            call.on('data', (request: ProxyResponse) => {
                proxyResponseStream.value = request;
            });

            proxyRequestStream.subscribe({
                next: (payload) => {
                    if (payload) {
                        call.write(payload);
                    }
                },
            });
        }

        notifyUnsavedChanges(
            call: ServerUnaryCall<UnsavedChangesRequest, UnsavedChangesResponse>,
            callback: sendUnaryData<UnsavedChangesResponse>
        ) {
            unsavedChangesStream.value = call.request;
            callback(null, {}, undefined, undefined);
        }

        leftSidebarStream(call: ServerWritableStream<SidebarRequest, SidebarResponse>) {
            ConnectServerWriteableStream(call, LeftSidebarStateStream());
        }

        rightSidebarStream(call: ServerWritableStream<SidebarRequest, SidebarResponse>) {
            ConnectServerWriteableStream(call, RightSidebarStateStream());
        }

        editorStream(call: ServerWritableStream<SidebarRequest, SidebarResponse>) {
            ConnectServerWriteableStream(call, EditorStateStream());
        }

        webview(call: ServerDuplexStream<WebviewRequest, WebviewResponse>): void {
            const controller = new WebviewContentController((payload) => {
                call.write({
                    response: { $case: 'payload', payload: { payload } },
                });
            });

            call.on('data', (request: WebviewRequest) => {
                switch (request.request?.$case) {
                    case 'payload':
                        controller.processPayloadFromWebview(request.request.payload.payload);
                        break;

                    case 'visibility':
                        controller.processesUpdateVisibilityFromWebview(request.request.visibility.isVisible);
                        break;
                }
            });

            call.on('cancelled', () => controller.dispose());
            call.on('error', () => controller.dispose());
            call.on('end', () => controller.dispose());
        }

        [name: string]: UntypedHandleCall; // ?
    }

    const updateIsAuthenticated = (isAuthenticated: boolean) => {
        authResponseStream.value = { isAuthenticated };
    };

    const openBrowser = (url: string) => {
        pluginCommandRequestStream.value = { command: { $case: 'openBrowser', openBrowser: { url } } };
    };

    const notifyPluginUpdate = () => {
        pluginCommandRequestStream.value = { command: { $case: 'notifyPluginUpdate', notifyPluginUpdate: {} } };
    };

    const restartIDE = () => {
        pluginCommandRequestStream.value = { command: { $case: 'restartIDE', restartIDE: {} } };
    };

    const showMarketplaceUI = () => {
        pluginCommandRequestStream.value = { command: { $case: 'showMarketplaceUI', showMarketplaceUI: {} } };
    };

    const downloadSvg = (svg: string, fileName: string) => {
        pluginCommandRequestStream.value = {
            command: {
                $case: 'downloadSvg',
                downloadSvg: { svg, fileName },
            },
        };
    };

    const openRightSidebar = () => {
        pluginCommandRequestStream.value = {
            command: { $case: 'openRightSidebar', openRightSidebar: {} },
        };
    };

    const openLeftSidebar = () =>
        (pluginCommandRequestStream.value = { command: { $case: 'openLeftSidebar', openLeftSidebar: {} } });

    const openEditor = () => {
        log.error('EDITOR LAUNCHED');
        pluginCommandRequestStream.value = { command: { $case: 'openEditor', openEditor: {} } };
    };

    const openFile = (request: OpenFileRequest) => {
        openFileRequestStream.value = request;
    };

    const sendProxyRequest = (request: ProxyRequest) => {
        proxyRequestStream.value = request;
    };

    return {
        service: new IDEAgentService(),
        initializationStream: initializationStream,
        heartbeatStream: heartbeatStream,
        updateIsAuthenticated,
        openBrowser,
        notifyPluginUpdate,
        restartIDE,
        showMarketplaceUI,
        updateRepoRootsStream: updateRepoRootsStream,
        updateEditorsStream: updateEditorsStream,
        agentCommandStream: agentCommandStream,
        openFile,
        sendProxyRequest: sendProxyRequest,
        proxyResponseStream: proxyResponseStream,
        unsavedChangesStream: unsavedChangesStream,
        getThemeMode: () => themeMode,
        downloadSvg,
        openRightSidebar,
        openLeftSidebar,
        openEditor,
    };
};

export const IDEAgent = generateIDEAgentService();
