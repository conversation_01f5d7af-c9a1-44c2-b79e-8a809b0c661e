export type NotificationSetupState =
    | { $case: 'loading' }
    | {
          $case: 'loaded';

          // True if notifications are enabled
          isEnabled: boolean;

          // True if we think the user has gone through notification configuration
          userHasConfigured: boolean;
      };

export type NotificationSetupCommands = { $case: 'openNotificationPreferences' } | { $case: 'skipNotificationSetup' };
