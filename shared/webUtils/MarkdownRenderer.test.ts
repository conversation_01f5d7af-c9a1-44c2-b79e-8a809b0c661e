import { Block, ListBlock_Style } from '@shared/api/models';

import { RenderMessageMarkdown } from './MarkdownRenderer';
import {
    MsgCode,
    MsgHardLineBreak,
    MsgHorizontalLineBlock,
    MsgImage,
    MsgInlineImage,
    MsgLink,
    MsgList,
    MsgMention,
    MsgParagraph,
    MsgQuote,
    MsgText,
    MsgVideo,
} from './MessageConstructors';

describe('MarkdownRenderer', () => {
    it('should render a simple text paragraph', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'Hello, world!' })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('Hello, world!');
    });

    it('should render a paragraph with multiple inline elements', () => {
        const blocks: Block[] = [
            MsgParagraph({ elements: [MsgText({ text: 'Hello, ' }), MsgText({ text: 'world!' })] }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('Hello, world!');
    });

    it('should render a paragraph with hard line break', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [MsgText({ text: 'First line' }), MsgHardLineBreak(), MsgText({ text: 'Second line' })],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('First line\nSecond line');
    });

    it('should render a paragraph with multiple elements and hard line breaks', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgText({ text: 'First ' }),
                    MsgText({ text: 'part' }),
                    MsgHardLineBreak(),
                    MsgText({ text: 'Second ' }),
                    MsgText({ text: 'part' }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('First part\nSecond part');
    });

    it('should render a paragraph with links and hard line breaks', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgLink({ textItems: [{ text: 'First' }], url: 'https://example.com/1' }),
                    MsgHardLineBreak(),
                    MsgLink({ textItems: [{ text: 'Second' }], url: 'https://example.com/2' }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('[First](https://example.com/1)\n[Second](https://example.com/2)');
    });

    it('should render a paragraph with mentions and hard line breaks', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgMention({ displayName: 'John', teamMemberId: '123', username: 'john' }),
                    MsgHardLineBreak(),
                    MsgMention({ displayName: 'Jane', teamMemberId: '456', username: 'jane' }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('@John\n@Jane');
    });

    it('should render a link', () => {
        const blocks: Block[] = [
            MsgParagraph({ elements: [MsgLink({ textItems: [{ text: 'Click here' }], url: 'https://example.com' })] }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('[Click here](https://example.com)');
    });

    it('should render an inline image', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [MsgInlineImage({ url: 'https://example.com/image.jpg', altText: 'Example image' })],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('![Example image](https://example.com/image.jpg)');
    });

    it('should render a hard line break', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [MsgText({ text: 'First line' }), MsgHardLineBreak(), MsgText({ text: 'Second line' })],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('First line\nSecond line');
    });

    it('should render a mention', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [MsgMention({ displayName: 'John Doe', teamMemberId: '123', username: 'johndoe' })],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('@John Doe');
    });

    it('should render a code block', () => {
        const blocks: Block[] = [
            MsgCode('const x = 1;\nconsole.log(x);', { text: 'const x = 1;\nconsole.log(x);', language: 'typescript' }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('```typescript\nconst x = 1;\nconsole.log(x);\n```');
    });

    it('should render a horizontal line', () => {
        const blocks: Block[] = [MsgHorizontalLineBlock()];
        expect(RenderMessageMarkdown(blocks)).toBe('---');
    });

    it('should render an ordered list', () => {
        const blocks: Block[] = [
            MsgList({
                style: ListBlock_Style.ORDERED,
                items: [
                    {
                        blocks: [MsgParagraph({ elements: [MsgText({ text: 'First item' })] })],
                    },
                    {
                        blocks: [MsgParagraph({ elements: [MsgText({ text: 'Second item' })] })],
                    },
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('1. First item\n2. Second item');
    });

    it('should render an unordered list', () => {
        const blocks: Block[] = [
            MsgList({
                style: ListBlock_Style.UNORDERED,
                items: [
                    {
                        blocks: [MsgParagraph({ elements: [MsgText({ text: 'First item' })] })],
                    },
                    {
                        blocks: [MsgParagraph({ elements: [MsgText({ text: 'Second item' })] })],
                    },
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('- First item\n- Second item');
    });

    it('should render a quote block', () => {
        const blocks: Block[] = [
            MsgQuote({
                blocks: [
                    MsgParagraph({ elements: [MsgText({ text: 'First line' })] }),
                    MsgParagraph({ elements: [MsgText({ text: 'Second line' })] }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('> First line\n> Second line');
    });

    it('should render a block image', () => {
        const blocks: Block[] = [MsgImage({ url: 'https://example.com/image.jpg', altText: 'Example image' })];
        expect(RenderMessageMarkdown(blocks)).toBe('![Example image](https://example.com/image.jpg)');
    });

    it('should render a video block', () => {
        const blocks: Block[] = [MsgVideo({ url: 'https://example.com/video.mp4', altText: 'Example video' })];
        expect(RenderMessageMarkdown(blocks)).toBe('[video](https://example.com/video.mp4)');
    });

    it('should render multiple blocks with proper spacing', () => {
        const blocks: Block[] = [
            MsgParagraph({ elements: [MsgText({ text: 'First paragraph' })] }),
            MsgParagraph({ elements: [MsgText({ text: 'Second paragraph' })] }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('First paragraph\nSecond paragraph');
    });

    it('should render bold text', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'Bold text', isBold: true })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('**Bold text**');
    });

    it('should render italic text', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'Italic text', isItalic: true })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('_Italic text_');
    });

    it('should render code text', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'code', isCode: true })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('`code`');
    });

    it('should render strikethrough text', () => {
        const blocks: Block[] = [
            MsgParagraph({ elements: [MsgText({ text: 'strikethrough', isStrikethrough: true })] }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('~strikethrough~');
    });

    it('should render subscript text', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'sub', isSubscript: true })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('<sub>sub</sub>');
    });

    it('should render superscript text', () => {
        const blocks: Block[] = [MsgParagraph({ elements: [MsgText({ text: 'super', isSuperscript: true })] })];
        expect(RenderMessageMarkdown(blocks)).toBe('<sup>super</sup>');
    });

    it('should render text with multiple formats', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgText({
                        text: 'Bold and italic',
                        isBold: true,
                        isItalic: true,
                    }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('**_Bold and italic_**');
    });

    it('should render formatted text in links', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgLink({
                        textItems: [
                            { text: 'Bold', isBold: true },
                            { text: ' and ' },
                            { text: 'Italic', isItalic: true },
                        ],
                        url: 'https://example.com',
                    }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('[**Bold** and _Italic_](https://example.com)');
    });

    it('should handle nested formatting', () => {
        const blocks: Block[] = [
            MsgParagraph({
                elements: [
                    MsgText({
                        text: 'Complex formatting',
                        isBold: true,
                        isItalic: true,
                        isCode: true,
                    }),
                ],
            }),
        ];
        expect(RenderMessageMarkdown(blocks)).toBe('**_`Complex formatting`_**');
    });

    it('should render a table block', () => {
        const tableBlock = {
            content: {
                $case: 'table',
                table: {
                    head: {
                        row: {
                            cells: [
                                {
                                    contents: [{ content: { $case: 'text', text: { text: 'Header 1' } } }],
                                    width: 0,
                                    alignment: 0,
                                },
                                {
                                    contents: [{ content: { $case: 'text', text: { text: 'Header 2' } } }],
                                    width: 0,
                                    alignment: 0,
                                },
                            ],
                        },
                    },
                    body: {
                        rows: [
                            {
                                cells: [
                                    {
                                        contents: [{ content: { $case: 'text', text: { text: 'Row 1 Col 1' } } }],
                                        width: 0,
                                        alignment: 0,
                                    },
                                    {
                                        contents: [{ content: { $case: 'text', text: { text: 'Row 1 Col 2' } } }],
                                        width: 0,
                                        alignment: 0,
                                    },
                                ],
                            },
                            {
                                cells: [
                                    {
                                        contents: [{ content: { $case: 'text', text: { text: 'Row 2 Col 1' } } }],
                                        width: 0,
                                        alignment: 0,
                                    },
                                    {
                                        contents: [{ content: { $case: 'text', text: { text: 'Row 2 Col 2' } } }],
                                        width: 0,
                                        alignment: 0,
                                    },
                                ],
                            },
                        ],
                    },
                },
            },
        };
        const blocks = [tableBlock];
        expect(RenderMessageMarkdown(blocks as never[])).toBe(
            '| Header 1 | Header 2 |\n| --- | --- |\n| Row 1 Col 1 | Row 1 Col 2 |\n| Row 2 Col 1 | Row 2 Col 2 |'
        );
    });
});
