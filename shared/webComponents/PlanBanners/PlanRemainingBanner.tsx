import dayjs from 'dayjs';
import { useCallback } from 'react';

import { Team } from '@shared/api/generatedApi';

import { Banner, BannerVariant } from '@shared/webComponents/Banner/Banner';
import { Button, ButtonVariant } from '@shared/webComponents/Button/Button';
import { DashboardUrls } from '@shared/webUtils/DashboardUrls';

import pricingPlanIcon from '@clientAssets/pricing-plans.png';

import { ClientWorkspace } from '../ClientWorkspace/ClientWorkspace';
import { TimeRemaining } from './PlanExpiryBanner';

interface Props {
    expiresAt: Date;
    planName: string;
    team: Team;
    showAction?: boolean;
    minimumDaysOut?: number;
    floating?: boolean;
    variant?: BannerVariant;
    compressed?: boolean;
    buttonVariant?: ButtonVariant;
}

export const PlanRemainingBanner = ({
    expiresAt,
    planName,
    team,
    showAction,
    minimumDaysOut = 0,
    floating,
    variant = 'warning',
    compressed,
    buttonVariant = 'tertiary',
}: Props) => {
    const viewPlans = useCallback(
        () =>
            ClientWorkspace.instance().handleAction({
                $case: 'openUrl',
                url: DashboardUrls.orgPlans(team.id),
                navigate: true,
            }),
        [team.id]
    );

    const now = dayjs();
    const shouldShowBanner = now.isBefore(expiresAt) && now.add(minimumDaysOut, 'day').isSameOrAfter(expiresAt);

    if (!shouldShowBanner) {
        return null;
    }

    const title = (
        <>
            <TimeRemaining expiryDate={expiresAt} /> remaining in your {planName} plan
        </>
    );

    const content = 'Keep your current subscription to continue to use Unblocked uninterrupted.';

    return (
        <Banner
            className="plan_banner"
            variant={variant}
            header={compressed ? undefined : title}
            icon={pricingPlanIcon}
            iconSize={32}
            actions={
                showAction ? (
                    <Button variant={buttonVariant} size="tight" onClick={viewPlans}>
                        Keep subscription
                    </Button>
                ) : null
            }
            floating={floating}
        >
            {compressed ? title : content}
        </Banner>
    );
};
