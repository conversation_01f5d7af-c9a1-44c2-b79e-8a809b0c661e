flyway-info-local: developer-setup
	flyway -configFiles=flyway.toml \
	  -environment=local \
	  info
.PHONY: flyway-info-local

flyway-info-dev: developer-setup
	@if [ -z "$$DB_PASS" ]; then \
	  echo "❌  DB_PASS is not set" >&2; exit 1; \
	fi
	DB_PASS=$${DB_PASS}; \
	flyway -configFiles=flyway.toml \
	  -password=$$DB_PASS \
	  -environment=dev \
	  info
.PHONY: flyway-info-dev

flyway-repair-local: developer-setup
	flyway -configFiles=flyway.toml \
	  -environment=local \
	  repair
.PHONY: flyway-repair-local

flyway-repair-dev: developer-setup
	@if [ -z "$$DB_PASS" ]; then \
	  echo "❌  DB_PASS is not set" >&2; exit 1; \
	fi
	DB_PASS=$${DB_PASS}; \
	flyway -configFiles=flyway.toml \
	  -password=$$DB_PASS \
	  -environment=dev \
	  repair
.PHONY: flyway-repair-dev

# Do not attempt to use flyway cli as it does not handle Java classpaths
flyway-migrate-local:
	ENV=$${ENV:-local}; \
	./gradlew :projects:apps:dbmigrator:applyDatabaseMigration \
	  -Pdbmigrator.env=local \
	  -Pdbmigrator.dbUser=demo \
	  -Pdbmigrator.dbPass=demo
.PHONY: flyway-migrate-local

# Do not attempt to use flyway cli as it does not handle Java classpaths
flyway-migrate-dev:
	@if [ -z "$$DB_PASS" ]; then \
	  echo "❌  DB_PASS is not set" >&2; exit 1; \
	fi
	DB_PASS=$${DB_PASS}; \
	./gradlew :projects:apps:dbmigrator:applyDatabaseMigration \
	  -Pdbmigrator.env=dev \
	  -Pdbmigrator.dbUser=unblocked \
	  -Pdbmigrator.dbPass=$$DB_PASS
.PHONY: flyway-migrate-dev

flyway-drop-schema-history-table-local:
	ENV=$${ENV:-local}; \
	./gradlew :projects:apps:dbmigrator:dropSchemaHistoryTable \
	  -Pdbmigrator.env=local \
	  -Pdbmigrator.dbUser=demo \
	  -Pdbmigrator.dbPass=demo
.PHONY: flyway-drop-schema-history-table-local

flyway-drop-schema-history-table-dev:
	@if [ -z "$$DB_PASS" ]; then \
	  echo "❌  DB_PASS is not set" >&2; exit 1; \
	fi
	DB_PASS=$${DB_PASS}; \
	./gradlew :projects:apps:dbmigrator:dropSchemaHistoryTable \
	  -Pdbmigrator.env=dev \
	  -Pdbmigrator.dbUser=unblocked \
	  -Pdbmigrator.dbPass=$$DB_PASS
.PHONY: flyway-drop-schema-history-table-dev

flyway-rollback-schema-history-local:
	@if [ -z "$$VERSION" ]; then \
	  echo "❌  VERSION is not set (example: VERSION=1.5)"; exit 1; \
	fi
	ENV=$${ENV:-local}; VERSION=$${VERSION}; \
	./gradlew :projects:apps:dbmigrator:rollbackSchemaHistory \
	  -Pdbmigrator.env=local \
	  -Pdbmigrator.dbUser=demo \
	  -Pdbmigrator.dbPass=demo \
	  -Pdbmigrator.version=$$VERSION
.PHONY: flyway-rollback-schema-history-local

flyway-rollback-schema-history-dev:
	@if [ -z "$$DB_PASS" ]; then \
	  echo "❌  DB_PASS is not set" >&2; exit 1; \
	fi
	@if [ -z "$$VERSION" ]; then \
	  echo "❌  VERSION is not set (example: VERSION=1.5)" >&2; exit 1; \
	fi
	DB_PASS=$${DB_PASS}; VERSION=$${VERSION}; \
	./gradlew :projects:apps:dbmigrator:rollbackSchemaHistory \
	  -Pdbmigrator.env=dev \
	  -Pdbmigrator.dbUser=unblocked \
	  -Pdbmigrator.dbPass=$$DB_PASS \
	  -Pdbmigrator.version=$$VERSION
.PHONY: flyway-rollback-schema-history-dev

generate-database-migration:
	./gradlew :projects:apps:dbmigrator:generateDatabaseMigration
.PHONY: generate-database-migration

generate-collapsed-migration:
	@echo "Checking for clean git working directory..."
	@if [ -n "$$(git status --porcelain)" ]; then \
		echo "❌ Error: Working directory is not clean. Please commit or stash your changes first."; \
		exit 1; \
	fi
	@echo "Removing migration files added in this branch..."
	@git --no-pager diff --name-only --diff-filter=A main...HEAD -- projects/models/src/main/resources/db/migration/ | xargs -r rm -rf || true
	@echo "Generating new consolidated migration..."
	@$(MAKE) generate-database-migration
	@echo "✅ Migration files collapsed successfully."
.PHONY: generate-collapsed-migration

developer-setup: service-setup
	bash ./developer/setup/developer-setup.sh
.PHONY: developer-setup

service-setup::
	bash ./docker/service-setup.sh >/dev/null
.PHONY: service-setup

create-service:
	$(MAKE) -C ./kotlin create-service
.PHONY: create-service

create-service-helm-charts:
	$(MAKE) -C ./helm create-service-helm-charts
.PHONY: create-service-helm-charts

update-service-helm-charts:
	$(MAKE) -C ./helm update-service-helm-charts
.PHONY: update-service-helm-charts

start-ci-services:
	docker compose -f docker-compose-ci.yml up --force-recreate -d
.PHONY: start-ci-services

stop-ci-services:
	docker compose -f docker-compose-ci.yml down
.PHONY: stop-ci-services

encrypt-local-secrets:
	$(MAKE) -C ./secrets encrypt-local-secrets
.PHONY: encrypt-local-secrets

decrypt-local-secrets:
	$(MAKE) -C ./secrets decrypt-local-secrets
.PHONY: decrypt-local-secrets

encrypt-k8s-secrets-dev:
	$(MAKE) -C ./secrets encrypt-k8s-secrets-dev
.PHONY: encrypt-k8s-secrets-dev

decrypt-k8s-secrets-dev:
	$(MAKE) -C ./secrets decrypt-k8s-secrets-dev
.PHONY: decrypt-k8s-secrets-dev

deploy-k8s-secrets-dev:
	$(MAKE) -C ./secrets deploy-k8s-secrets-dev
.PHONY: deploy-k8s-secrets-dev

encrypt-k8s-secrets-prod:
	$(MAKE) -C ./secrets encrypt-k8s-secrets-prod
.PHONY: encrypt-k8s-secrets-dev

decrypt-k8s-secrets-prod:
	$(MAKE) -C ./secrets decrypt-k8s-secrets-prod
.PHONY: decrypt-k8s-secrets-prod

deploy-k8s-secrets-prod:
	$(MAKE) -C ./secrets deploy-k8s-secrets-prod
.PHONY: deploy-k8s-secrets-prod

encrypt-secrets:
	$(MAKE) -C ./secrets encrypt-secrets
.PHONY: encrypt-secrets

decrypt-secrets:
	$(MAKE) -C ./secrets decrypt-secrets
.PHONY: decrypt-secrets

decrypt-cdk-secrets:
	$(MAKE) -C ./secrets decrypt-cdk-secrets
.PHONY: decrypt-cdk-secrets

encrypt-cdk-secrets:
	$(MAKE) -C ./secrets encrypt-cdk-secrets
.PHONY: encrypt-cdk-secrets

decrypt-prefect-secrets:
	$(MAKE) -C ./secrets decrypt-prefect-secrets
.PHONY: decrypt-prefect-secrets

encrypt-prefect-secrets:
	$(MAKE) -C ./secrets encrypt-prefect-secrets
.PHONY: encrypt-prefect-secrets

clean-build-cache:
	rm -rf .build-cache
.PHONY: clean-build-cache

clean: clean-build-cache
	./gradlew --stop && ./gradlew clean
.PHONY: clean

lint: developer-setup
	./gradlew api:openAPIStyleValidator detekt formatKotlin lintKotlin
.PHONY: lint

check: lint start-ci-services
	./gradlew check
.PHONY: check

build-custom-ktlint-rules:
	./gradlew :custom-ktlint-rules:build :custom-ktlint-rules:copyToLibs
.PHONY: build-custom-ktlint-rules

build: lint
	./gradlew build -x test testClasses
.PHONY: build

build-jetbrains: lint
	./gradlew -PincludeJetbrains=true :jetbrains:build
.PHONY: build-jetbrains

build-and-test: lint start-ci-services
	./gradlew build
.PHONY: build-and-test

# Cleans and restarts database for prefect
drop-local-prefect-database:
	docker ps -a --filter name=postgres-prefect --format '{{.ID}}' | xargs docker rm
	docker volume rm unblocked_postgres-prefect-data || true
.PHONY: restart-local-prefect-database


# Cleans and restarts ephemeral database for ci
restart-local-ci-database:
	$(DOCKER_COMPOSE_LOCAL) stop postgres-ci
	$(DOCKER_COMPOSE_LOCAL) rm -f postgres-ci
	$(DOCKER_COMPOSE_LOCAL) up -d postgres-ci
.PHONY: restart-local-ci-database

# cleans all persistent data for local-stack (Postgres and Redis)
drop-local-stack-database:
	docker ps --filter name=postgres --format '{{.ID}}' | xargs -r docker stop
	docker ps -a --filter name=postgres --format '{{.ID}}' | xargs docker rm
	docker volume rm unblocked_postgres-local-stack-data || true
	docker ps -a --filter name=redis --format '{{.ID}}' | xargs docker rm
	docker volume rm unblocked_redis-local-stack-data || true
.PHONY: drop-local-stack-database

DOCKER_COMPOSE_LOCAL = docker compose --file docker-compose-local.yml
DOCKER_LOCAL = docker

#
# Starts local docker compose stack running a subset of kotlin application services behind load balancer.
# SERVICE environment variable can be set to a subset of services to run, default is web-facing services.
#
run-local-docker-stack:
	COMPOSE_PROFILES='local,intellij' \
	$(DOCKER_COMPOSE_LOCAL) --all-resources down --remove-orphans
	$(DOCKER_COMPOSE_LOCAL) --profile 'local' up --build --force-recreate --remove-orphans --detach
	$(DOCKER_COMPOSE_LOCAL) --profile 'local' logs --follow --tail all
.PHONY: run-local-docker-stack

#
# starts local docker compose stack proxying behind load balancer to all kotlin services started through intellij
#
run-local-intellij-stack:
	COMPOSE_PROFILES='local,intellij' \
	$(DOCKER_COMPOSE_LOCAL) --all-resources down --remove-orphans
	$(DOCKER_COMPOSE_LOCAL) --profile 'intellij' up --build --force-recreate --remove-orphans --detach
	$(DOCKER_COMPOSE_LOCAL) --profile 'intellij' logs --follow --tail all
.PHONY: run-local-intellij-stack

clean-local-intellij-stack:
	COMPOSE_PROFILES='local,intellij' $(DOCKER_COMPOSE_LOCAL) --all-resources down --remove-orphans; \
	$(DOCKER_LOCAL) volume ls --filter name=unblocked --format '{{.Name}}' | xargs docker volume rm; \
	$(DOCKER_LOCAL) container prune -f && docker image prune -f && docker volume prune -f; \
	$(DOCKER_LOCAL) volume ls; \
.PHONY: clean-local-intellij-stack



#
# Cleanup docker disk usage, removing stopped containers, dangling images, and builder cache.
#
clean-docker:
	docker system prune --force
.PHONY: clean-docker

use-local:
	echo local > ~/Library/Application\ Support/Unblocked/env
.PHONY: use-local

use-dev:
	echo dev > ~/Library/Application\ Support/Unblocked/env
.PHONY: use-dev

use-prod:
	echo prod > ~/Library/Application\ Support/Unblocked/env
.PHONY: use-prod

use-onprem:
	echo onprem > ~/Library/Application\ Support/Unblocked/env
.PHONY: use-onprem

use-default:
	rm -f ~/Library/Application\ Support/Unblocked/env
.PHONY: use-default
