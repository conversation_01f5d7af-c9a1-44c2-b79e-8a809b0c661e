[versions]
activemqVersion = "6.1.6"
apacheDbcp2Version = "2.12.0"
apacheMath3Version = "3.6.1"
apacheTikaVersion = "2.9.3"
arrowKtVersion = "1.2.4"
atlassianJwtVersion = "3.3.3"
awsSdkVersion = "2.30.16"
bouncyCastleVersion = "1.80"
cache4kVersion = "0.14.0"
commonMarkVersion = "0.22.0"
crawlerCommonsVersion = "1.4"
eclipseOpenAPIMicroProfileVersion = "3.1.2"
empoaSwaggerVersion = "2.1.0"
exposedVersion = "0.61.0"
failsafeVersion = "3.3.2"
flexmarkVersion = "0.64.8"
flywayVersion = "11.8.1"
googleApiClientVersion = "2.4.1"
googleDriveApiClientVersion = "v3-rev20240327-2.0.0"
googleAdminApiClientVersion = "directory_v1-rev20240304-2.0.0"
graphqlKtorVersion = "7.0.2"
grpcKotlinVersion = "1.4.1"
grpcVersion = "1.63.0"
hikariVersion = "6.3.0"
honeycombOpenTelemetryVersion = "1.7.0"
hopliteVersion = "2.9.0"
intellijGradlePluginVersion = "2.0.1"
intellijStructurePluginVersion = "3.250"
jacksonVersion = "2.18.3"
jacksonDatabindNullableVersion = "0.2.6"
javaDiffUtilsVersion = "4.15"
jcolorVersion = "5.5.1"
jsoupVersion = "1.17.2"
jose4jVersion = "0.9.6"
kotlinCsvVersion = "1.9.3"
kotlinDateTimeJvmVersion = "0.6.2"
kotlinGradlePluginVersion = "2.1.0"
kotlinLoggingVersion = "3.0.5"
kotlinSlfj4CoroutinesVersion = "1.8.0"
kotlinxCliVersion = "0.3.6"
kotlinxCoroutinesVersion = "1.10.2"
kotlinxSerializationVersion = "1.8.1"
kotlinxRpcCoreVersion = "0.7.0"
ktlintVersion = "1.4.1"
ktorVersion = "3.1.3"
lettuceVersion = "6.6.0.RELEASE"
logbackVersion = "1.5.17"
logstashLogbackEncoder = "7.4"
logzioLogbackAppenderVersion = "2.1.0"
oneLoginJavaSamlVersion = "2.9.0"
openAIKotlinVersion = "4.0.1"
openAPIStyleValidatorVersion = "1.11"
openSearchVersion = "2.23.0"
openSearchClientRestClientVersion = "3.0.0-alpha1"
openSearchPluginNeuralSearchVersion = "*******-alpha1"
openTelemetryInstrumentationVersion = "2.14.0-alpha"
openTelemetrySemconvVersion = "1.30.0"
openTelemetryVersion = "1.48.0"
pdfboxVersion = "3.0.4"
pineconeClientVersion = "4.0.1"
postgresqlVersion = "42.6.0"
protobufVersion = "4.31.1"
reflectionsVersion = "0.10.2"
sendgridVersion = "4.10.2"
semver4jVersion = "3.1.0"
segment = "1.16.3"
slackVersion = "1.45.3"
stripeVersion = "26.9.0"
swaggerCoreVersion = "1.6.14"
swaggerParserVersion = "2.1.22"
zallyVersion = "2.1.0"

detektPluginVersion = "1.23.6"
gradleNodePluginVersion = "3.6.0"
graphqlPluginVersion = "6.5.5"
kotlinJpaPluginVersion = "2.1.0"
openapiGeneratorPluginVersion = "6.6.0"
protobufPluginVersion = "0.9.4"
shadowJarPluginVersion = "8.1.1"

[libraries]
activemq-client = { module = "org.apache.activemq:activemq-client", version.ref = "activemqVersion" }
activemq-pool = { module = "org.apache.activemq:activemq-pool", version.ref = "activemqVersion" }
apache-commons-dbcp2 = { module = "org.apache.commons:commons-dbcp2", version.ref = "apacheDbcp2Version" }
apache-commons-math3 = { module = "org.apache.commons:commons-math3", version.ref = "apacheMath3Version" }
apache-tika = { module = "org.apache.tika:tika-core", version.ref = "apacheTikaVersion" }
apache-pdfbox = { module = "org.apache.pdfbox:pdfbox", version.ref = "pdfboxVersion" }
arrow-kt-core = { module = "io.arrow-kt:arrow-core", version.ref = "arrowKtVersion" }
arrow-kt-coroutines = { module = "io.arrow-kt:arrow-fx-coroutines", version.ref = "arrowKtVersion" }
atlassian-jwt-api = { module = "com.atlassian.jwt:jwt-api", version.ref = "atlassianJwtVersion" }
atlassian-jwt-core = { module = "com.atlassian.jwt:jwt-core", version.ref = "atlassianJwtVersion" }
aws-sdk-bom = { module = "software.amazon.awssdk:bom", version.ref = "awsSdkVersion" }
aws-sdk-bedrockruntime = { module = "software.amazon.awssdk:bedrockruntime", version.ref = "awsSdkVersion" }
aws-sdk-bedrockagentruntime = { module = "software.amazon.awssdk:bedrockagentruntime", version.ref = "awsSdkVersion" }
aws-sdk-dynamo = { module = "software.amazon.awssdk:dynamodb", version.ref = "awsSdkVersion" }
aws-sdk-lambda = { module = "software.amazon.awssdk:lambda", version.ref = "awsSdkVersion" }
aws-sdk-rds = { module = "software.amazon.awssdk:rds", version.ref = "awsSdkVersion" }
aws-sdk-s3 = { module = "software.amazon.awssdk:s3", version.ref = "awsSdkVersion" }
aws-sdk-sagemakerruntime = { module = "software.amazon.awssdk:sagemakerruntime", version.ref = "awsSdkVersion" }
aws-sdk-sqs = { module = "software.amazon.awssdk:sqs", version.ref = "awsSdkVersion" }
aws-sdk-sfn = { module = "software.amazon.awssdk:sfn", version.ref = "awsSdkVersion" }
aws-sdk-sts = { module = "software.amazon.awssdk:sts", version.ref = "awsSdkVersion" }
bouncycastle-bcpkix = { module = "org.bouncycastle:bcpkix-jdk18on", version.ref = "bouncyCastleVersion" }
commonmark = { module = "org.commonmark:commonmark", version.ref = "commonMarkVersion" }
commonmark-ext-autolink = { module = "org.commonmark:commonmark-ext-autolink", version.ref = "commonMarkVersion" }
commonmark-ext-gfm-strikethrough = { module = "org.commonmark:commonmark-ext-gfm-strikethrough", version.ref = "commonMarkVersion" }
commonmark-ext-gfm-tables = { module = "org.commonmark:commonmark-ext-gfm-tables", version.ref = "commonMarkVersion" }
crawler-commons = { module = "com.github.crawler-commons:crawler-commons", version.ref = "crawlerCommonsVersion" }
eclipse-microprofile-openapi = { module = "org.eclipse.microprofile.openapi:microprofile-openapi-api", version.ref = "eclipseOpenAPIMicroProfileVersion" }
empoa-swagger-core = { module = "org.openapitools.empoa:empoa-swagger-core", version.ref = "empoaSwaggerVersion" }
expediagroup-graphql-ktor-client = { module = "com.expediagroup:graphql-kotlin-ktor-client", version.ref = "graphqlKtorVersion" }
expediagroup-graphql-ktor-client-serialization = { module = "com.expediagroup:graphql-kotlin-client-serialization", version.ref = "graphqlKtorVersion" }
exposed-core = { module = "org.jetbrains.exposed:exposed-core", version.ref = "exposedVersion" }
exposed-dao = { module = "org.jetbrains.exposed:exposed-dao", version.ref = "exposedVersion" }
exposed-jdbc = { module = "org.jetbrains.exposed:exposed-jdbc", version.ref = "exposedVersion" }
exposed-json = { module = "org.jetbrains.exposed:exposed-json", version.ref = "exposedVersion" }
exposed-kotlin-datetime = { module = "org.jetbrains.exposed:exposed-kotlin-datetime", version.ref = "exposedVersion" }
exposed-migration = { module = "org.jetbrains.exposed:exposed-migration", version.ref = "exposedVersion" }
failsafe = { module = "dev.failsafe:failsafe", version.ref = "failsafeVersion" }
fasterxml-jackson-core = { module = "com.fasterxml.jackson.core:jackson-core", version.ref = "jacksonVersion" }
fasterxml-jackson-databind = { module = "com.fasterxml.jackson.core:jackson-databind", version.ref = "jacksonVersion" }
fasterxml-jackson-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jacksonVersion" }
fasterxml-jackson-datatype-jsr310 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jsr310", version.ref = "jacksonVersion" }
fasterxml-jackson-datatype-jdk8 = { module = "com.fasterxml.jackson.datatype:jackson-datatype-jdk8", version.ref = "jacksonVersion" }
flexmark = { module = "com.vladsch.flexmark:flexmark-all", version.ref = "flexmarkVersion" }
flyway-core = { module="org.flywaydb:flyway-core", version.ref = "flywayVersion" }
flyway-database-postgresql = { module="org.flywaydb:flyway-database-postgresql", version.ref = "flywayVersion" }
google-api-client = { module = "com.google.api-client:google-api-client", version.ref = "googleApiClientVersion" }
google-drive-api-client = { module = "com.google.apis:google-api-services-drive", version.ref = "googleDriveApiClientVersion" }
google-drive-admin-client = { module = "com.google.apis:google-api-services-admin-directory", version.ref = "googleAdminApiClientVersion" }
grpc-kotlin-stub = { module = "io.grpc:grpc-kotlin-stub", version.ref = "grpcKotlinVersion" }
grpc-netty = { module = "io.grpc:grpc-netty", version.ref = "grpcVersion" }
grpc-netty-shaded = { module = "io.grpc:grpc-netty-shaded", version.ref = "grpcVersion" }
grpc-protobuf = { module = "io.grpc:grpc-protobuf", version.ref = "grpcVersion" }
grpc-stub = { module = "io.grpc:grpc-stub", version.ref = "grpcVersion" }
grpc-services = { module = "io.grpc:grpc-services", version.ref = "grpcVersion" }
hikari = { module = "com.zaxxer:HikariCP", version.ref = "hikariVersion" }
honeycomb-opentelemetry-sdk = { module = "io.honeycomb:honeycomb-opentelemetry-sdk", version.ref = "honeycombOpenTelemetryVersion" }
hoplite-core = { module = "com.sksamuel.hoplite:hoplite-core", version.ref = "hopliteVersion" }
hoplite-hocon = { module = "com.sksamuel.hoplite:hoplite-hocon", version.ref = "hopliteVersion" }
intellij-gradle-plugin = { module = "org.jetbrains.intellij.plugins:gradle-intellij-plugin", version.ref = "intellijGradlePluginVersion" }
intellij-structure-plugin = { module = "org.jetbrains.intellij.plugins:structure-intellij", version.ref = "intellijStructurePluginVersion" }
jackson-databind-nullable = { module = "org.openapitools:jackson-databind-nullable", version.ref = "jacksonDatabindNullableVersion" }
java-diff-utils = { module = "io.github.java-diff-utils:java-diff-utils", version.ref = "javaDiffUtilsVersion" }
jcolor = { module = "com.diogonunes:JColor", version.ref = "jcolorVersion" }
jsoup = { module = "org.jsoup:jsoup", version.ref = "jsoupVersion" }
jose4j = { module = "org.bitbucket.b_c:jose4j", version.ref = "jose4jVersion" }
kotlin-csv = { module = "com.github.doyaaaaaken:kotlin-csv-jvm", version.ref = "kotlinCsvVersion" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinGradlePluginVersion" }
kotlin-logging = { module = "io.github.microutils:kotlin-logging", version.ref = "kotlinLoggingVersion" }
kotlinx-cli = { module = "org.jetbrains.kotlinx:kotlinx-cli", version.ref = "kotlinxCliVersion" }
kotlinx-coroutines-debug = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-debug", version.ref = "kotlinxCoroutinesVersion" }
kotlinx-coroutines-guava = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-guava", version.ref = "kotlinxCoroutinesVersion" }
kotlinx-coroutines-reactive = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-reactive", version.ref = "kotlinxCoroutinesVersion" }
kotlinx-coroutines-slf4j = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j", version.ref = "kotlinSlfj4CoroutinesVersion" }
kotlinx-datetime-jvm = { module = "org.jetbrains.kotlinx:kotlinx-datetime-jvm", version.ref = "kotlinDateTimeJvmVersion" }
kotlinx-serialization-hocon = { module = "org.jetbrains.kotlinx:kotlinx-serialization-hocon", version.ref = "kotlinxSerializationVersion" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationVersion" }
kotlinx-rpc-krpc-client = { module = "org.jetbrains.kotlinx:kotlinx-rpc-krpc-client", version.ref = "kotlinxRpcCoreVersion" }
kotlinx-rpc-krpc-server = { module = "org.jetbrains.kotlinx:kotlinx-rpc-krpc-server", version.ref = "kotlinxRpcCoreVersion" }
kotlinx-rpc-krpc-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-rpc-krpc-serialization-json", version.ref = "kotlinxRpcCoreVersion" }
kotlinx-rpc-krpc-ktor-client = { module = "org.jetbrains.kotlinx:kotlinx-rpc-krpc-ktor-client", version.ref = "kotlinxRpcCoreVersion" }
kotlinx-rpc-krpc-ktor-server = { module = "org.jetbrains.kotlinx:kotlinx-rpc-krpc-ktor-server", version.ref = "kotlinxRpcCoreVersion" }
ktlint-cli-ruleset-core = { module = "com.pinterest.ktlint:ktlint-cli-ruleset-core", version.ref = "ktlintVersion" }
ktlint-rule-engine = { module = "com.pinterest.ktlint:ktlint-rule-engine", version.ref = "ktlintVersion" }
ktlint-rule-engine-core = { module = "com.pinterest.ktlint:ktlint-rule-engine-core", version.ref = "ktlintVersion" }
ktlint-test = { module = "com.pinterest.ktlint:ktlint-test", version.ref = "ktlintVersion" }
ktor-client-auth = { module = "io.ktor:ktor-client-auth", version.ref = "ktorVersion" }
ktor-client-okhttp = { module = "io.ktor:ktor-client-okhttp", version.ref = "ktorVersion" }
ktor-client-cio = { module = "io.ktor:ktor-client-cio", version.ref = "ktorVersion" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktorVersion" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktorVersion" }
ktor-client-logging = { module = "io.ktor:ktor-client-logging", version.ref = "ktorVersion" }
ktor-network-tls = { module = "io.ktor:ktor-network-tls", version.ref = "ktorVersion" }
ktor-network-tls-certificates = { module = "io:ktor-network-tls-certificates", version.ref = "ktorVersion" }
ktor-resources = { module = "io.ktor:ktor-resources", version.ref = "ktorVersion" }
ktor-serialization = { module = "io.ktor:ktor-serialization", version.ref = "ktorVersion" }
ktor-serialization-kotlinx-json = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktorVersion" }
ktor-serialization-kotlinx-xml = { module = "io.ktor:ktor-serialization-kotlinx-xml", version.ref = "ktorVersion" }
ktor-server-auth = { module = "io.ktor:ktor-server-auth", version.ref = "ktorVersion" }
ktor-server-auth-jwt = { module = "io.ktor:ktor-server-auth-jwt", version.ref = "ktorVersion" }
ktor-server-auto-head-response = { module = "io.ktor:ktor-server-auto-head-response", version.ref = "ktorVersion" }
ktor-server-body-limit = { module = "io.ktor:ktor-server-body-limit", version.ref = "ktorVersion" }
ktor-server-caching-headers = { module = "io.ktor:ktor-server-caching-headers", version.ref = "ktorVersion" }
ktor-server-call-id = { module = "io.ktor:ktor-server-call-id", version.ref = "ktorVersion" }
ktor-server-call-logging = { module = "io.ktor:ktor-server-call-logging", version.ref = "ktorVersion" }
ktor-server-compression = { module = "io.ktor:ktor-server-compression", version.ref = "ktorVersion" }
ktor-server-content-negotiation = { module = "io.ktor:ktor-server-content-negotiation", version.ref = "ktorVersion" }
ktor-server-core = { module = "io.ktor:ktor-server-core", version.ref = "ktorVersion" }
ktor-server-cors = { module = "io.ktor:ktor-server-cors", version.ref = "ktorVersion" }
ktor-server-default-headers = { module = "io.ktor:ktor-server-default-headers", version.ref = "ktorVersion" }
ktor-server-forwarded-header = { module = "io.ktor:ktor-server-forwarded-header", version.ref = "ktorVersion" }
ktor-server-hsts = { module = "io.ktor:ktor-server-hsts", version.ref = "ktorVersion" }
ktor-server-html-builder = { module = "io.ktor:ktor-server-html-builder", version.ref = "ktorVersion" }
ktor-server-jetty = { module = "io.ktor:ktor-server-jetty", version.ref = "ktorVersion" }
ktor-server-metrics = { module = "io.ktor:ktor-server-metrics", version.ref = "ktorVersion" }
ktor-server-netty = { module = "io.ktor:ktor-server-netty", version.ref = "ktorVersion" }
ktor-server-resources = { module = "io.ktor:ktor-server-resources", version.ref = "ktorVersion" }
ktor-server-sessions = { module = "io.ktor:ktor-server-sessions", version.ref = "ktorVersion" }
ktor-server-status-pages = { module = "io.ktor:ktor-server-status-pages", version.ref = "ktorVersion" }
lettuce-core = { module = "io.lettuce:lettuce-core", version.ref = "lettuceVersion" }
logback-classic = { module = "ch.qos.logback:logback-classic", version.ref = "logbackVersion" }
logback-core = { module = "ch.qos.logback:logback-core", version.ref = "logbackVersion" }
logstash-logback-encoder = { module = "net.logstash.logback:logstash-logback-encoder", version.ref = "logstashLogbackEncoder" }
logzio-logback-appender = { module = "io.logz.logback:logzio-logback-appender", version.ref = "logzioLogbackAppenderVersion" }
onelogin-java-saml = { module = "com.onelogin:java-saml", version.ref = "oneLoginJavaSamlVersion" }
openai-kotlin = { module = "com.aallam.openai:openai-client", version.ref = "openAIKotlinVersion" }
openapi-style-validator = { module = "org.openapitools.openapistylevalidator:openapi-style-validator-lib", version.ref = "openAPIStyleValidatorVersion" }
opensearch-client-java = { module = "org.opensearch.client:opensearch-java", version.ref = "openSearchVersion" }
opensearch-client-rest-client = { module = "org.opensearch.client:opensearch-rest-client", version.ref = "openSearchClientRestClientVersion" }
opensearch-plugin-neural-search = { module = "org.opensearch.plugin:neural-search", version.ref = "openSearchPluginNeuralSearchVersion" }
opentelemetry-exporter-logging = { module = "io.opentelemetry:opentelemetry-exporter-logging", version.ref = "openTelemetryVersion" }
opentelemetry-exporter-otlp = { module = "io.opentelemetry:opentelemetry-exporter-otlp", version.ref = "openTelemetryVersion" }
opentelemetry-extension-kotlin = { module = "io.opentelemetry:opentelemetry-extension-kotlin", version.ref = "openTelemetryVersion" }
opentelemetry-javaagent-extension-api = { module = "io.opentelemetry.javaagent:opentelemetry-javaagent-extension-api", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-javaagent-instrumentation-jms = { module = "io.opentelemetry.javaagent.instrumentation:opentelemetry-javaagent-jms-3.0", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-javaagent-instrumentation-jms-common = { module = "io.opentelemetry.javaagent.instrumentation:opentelemetry-javaagent-jms-common", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-aws = { module = "io.opentelemetry.instrumentation:opentelemetry-aws-sdk-2.2", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-grpc = { module = "io.opentelemetry.instrumentation:opentelemetry-grpc-1.6", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-runtimemetrics-java17 = { module = "io.opentelemetry.instrumentation:opentelemetry-runtime-telemetry-java17", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-jdbc = { module = "io.opentelemetry.instrumentation:opentelemetry-jdbc", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-redis = { module = "io.opentelemetry.instrumentation:opentelemetry-lettuce-5.1", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-resources = { module = "io.opentelemetry.instrumentation:opentelemetry-resources", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-instrumentation-logback-mdc = { module = "io.opentelemetry.instrumentation:opentelemetry-logback-mdc-1.0", version.ref = "openTelemetryInstrumentationVersion" }
opentelemetry-sdk = { module = "io.opentelemetry:opentelemetry-sdk", version.ref = "openTelemetryVersion" }
opentelemetry-semconv = { module = "io.opentelemetry.semconv:opentelemetry-semconv", version.ref = "openTelemetrySemconvVersion" }
pinecone-client = { module = "io.pinecone:pinecone-client", version.ref = "pineconeClientVersion" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresqlVersion" }
protobuf-java = { module = "com.google.protobuf:protobuf-java", version.ref = "protobufVersion" }
protobuf-java-util = { module = "com.google.protobuf:protobuf-java-util", version.ref = "protobufVersion" }
protobuf-kotlin = { module = "com.google.protobuf:protobuf-kotlin", version.ref = "protobufVersion" }
protobuf-protoc = { module = "com.google.protobuf:protoc", version.ref = "protobufVersion" }
protobuf-protoc-java = { module = "io.grpc:protoc-gen-grpc-java", version.ref = "grpcVersion" }
protobuf-protoc-kotlin = { module = "io.grpc:protoc-gen-grpc-kotlin", version.ref = "grpcKotlinVersion" }
reactivecircus-cache4k = { module = "io.github.reactivecircus.cache4k:cache4k-jvm", version.ref = "cache4kVersion" }
reflections = { module = "org.reflections:reflections", version.ref = "reflectionsVersion" }
sendgrid-api = { module = "com.sendgrid:sendgrid-java", version.ref = "sendgridVersion" }
segment-core = { module = "com.segment.analytics.kotlin:core", version.ref = "segment" }
semver4j = { module = "com.vdurmont:semver4j", version.ref = "semver4jVersion" }
slack-api-bolt = { module = "com.slack.api:bolt", version.ref = "slackVersion" }
slack-api-bolt-ktor = { module = "com.slack.api:bolt-ktor", version.ref = "slackVersion" }
slack-api-client = { module = "com.slack.api:slack-api-client", version.ref = "slackVersion" }
slack-api-client-kotlin-extension = { module = "com.slack.api:slack-api-client-kotlin-extension", version.ref = "slackVersion" }
slack-api-model-kotlin-extension = { module = "com.slack.api:slack-api-model-kotlin-extension", version.ref = "slackVersion" }
slack-app-backend = { module = "com.slack.api:slack-app-backend", version.ref = "slackVersion" }
stripe = { module = "com.stripe:stripe-java", version.ref = "stripeVersion" }
swagger-core = { module = "io.swagger:swagger-core", version.ref = "swaggerCoreVersion" }
swagger-parser = { module = "io.swagger.parser.v3:swagger-parser", version.ref = "swaggerParserVersion" }
zally-core = { module = "org.zalando:zally-core", version.ref = "zallyVersion" }
zally-ruleset-zalando = { module = "org.zalando:zally-ruleset-zalando", version.ref = "zallyVersion" }
zally-ruleset-zally = { module = "org.zalando:zally-ruleset-zally", version.ref = "zallyVersion" }

[bundles]
activemq = ["activemq-client", "activemq-pool"]
arrow-kt = ["arrow-kt-core", "arrow-kt-coroutines"]
atlassian-jwt = ["atlassian-jwt-api", "atlassian-jwt-core"]
aws-sdk = ["aws-sdk-bedrockruntime", "aws-sdk-bedrockagentruntime", "aws-sdk-dynamo", "aws-sdk-lambda", "aws-sdk-sagemakerruntime", "aws-sdk-rds", "aws-sdk-s3", "aws-sdk-sfn", "aws-sdk-sqs", "aws-sdk-sts"]
commonmark = ["commonmark", "commonmark-ext-gfm-strikethrough", "commonmark-ext-autolink", "commonmark-ext-gfm-tables"]
exposed = ["exposed-core", "exposed-dao", "exposed-jdbc", "exposed-kotlin-datetime", "exposed-json", "exposed-migration"]
flyway = ["flyway-core", "flyway-database-postgresql"]
google-api = ["google-api-client", "google-drive-api-client", "google-drive-admin-client"]
grpc = ["grpc-stub", "grpc-protobuf", "grpc-kotlin-stub", "grpc-services"]
grpc-netty = ["grpc-netty", "grpc-netty-shaded"]
honeycomb = ["honeycomb-opentelemetry-sdk", "opentelemetry-sdk", "opentelemetry-exporter-logging", "opentelemetry-exporter-otlp", "opentelemetry-instrumentation-resources", "opentelemetry-semconv", "grpc-netty-shaded"]
hoplite = ["hoplite-core", "hoplite-hocon"]
fasterxml-jackson = [
    "fasterxml-jackson-core",
    "fasterxml-jackson-databind",
    "fasterxml-jackson-kotlin",
    "fasterxml-jackson-datatype-jsr310",
    "fasterxml-jackson-datatype-jdk8",
    "jackson-databind-nullable",
]
kotlinx-rpc-client = [
    "kotlinx-rpc-krpc-client",
    "kotlinx-rpc-krpc-serialization-json",
    "kotlinx-rpc-krpc-ktor-client",
]
kotlinx-rpc-server = [
    "kotlinx-rpc-krpc-server",
    "kotlinx-rpc-krpc-serialization-json",
    "kotlinx-rpc-krpc-ktor-server",
]
ktlint = ["ktlint-cli-ruleset-core", "ktlint-rule-engine", "ktlint-rule-engine-core", "ktlint-test"]
ktor-client = ["ktor-client-core", "ktor-client-cio", "ktor-client-okhttp", "ktor-client-logging", "ktor-client-content-negotiation", "ktor-client-auth", "ktor-serialization-kotlinx-json"]
ktor-server = ["ktor-resources", "ktor-server-core", "ktor-server-auto-head-response", "ktor-server-caching-headers", "ktor-server-content-negotiation", "ktor-server-compression", "ktor-server-default-headers", "ktor-server-hsts", "ktor-server-auth", "ktor-server-auth-jwt", "ktor-server-call-id", "ktor-server-cors", "ktor-server-forwarded-header", "ktor-server-call-logging", "ktor-server-netty", "ktor-server-metrics", "ktor-server-resources", "ktor-server-status-pages", "ktor-serialization", "ktor-serialization-kotlinx-json", "ktor-server-jetty", "ktor-server-html-builder", "ktor-server-sessions", "ktor-server-body-limit"]
logback = ["logback-classic", "logback-core"]
logzio = ["logzio-logback-appender", "logstash-logback-encoder", "logback-core", "logback-classic"]
openapiStyleValidator = ["openapi-style-validator", "eclipse-microprofile-openapi", "empoa-swagger-core"]
opensearch = ["opensearch-client-java", "opensearch-plugin-neural-search", "opensearch-client-rest-client"]
swagger = ["swagger-parser", "swagger-core"]
protobuf = ["protobuf-java-util", "protobuf-java", "protobuf-kotlin"]
slack = ["slack-api-client", "slack-api-model-kotlin-extension", "slack-api-client-kotlin-extension", "slack-app-backend"]
slack-bolt = ["slack-api-client", "slack-api-model-kotlin-extension", "slack-api-client-kotlin-extension", "slack-app-backend", "slack-api-bolt", "slack-api-bolt-ktor"]
zally = ["zally-core", "zally-ruleset-zally", "zally-ruleset-zalando"]

[plugins]
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detektPluginVersion" }
expediagroup-graphql = { id = "com.expediagroup.graphql", version.ref = "graphqlPluginVersion" }
gradle-node = { id = "com.github.node-gradle.node", version.ref = "gradleNodePluginVersion" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlinJpaPluginVersion" }
kotlinx-rpc-platform = { id = "org.jetbrains.kotlinx.rpc.platform", version.ref = "kotlinxRpcCoreVersion" }
openapi-generator = { id = "org.openapi.generator", version.ref = "openapiGeneratorPluginVersion" }
protobuf = { id = "com.google.protobuf", version.ref = "protobufPluginVersion" }
shadow = { id = "com.github.johnrengelman.shadow", version.ref = "shadowJarPluginVersion" }
