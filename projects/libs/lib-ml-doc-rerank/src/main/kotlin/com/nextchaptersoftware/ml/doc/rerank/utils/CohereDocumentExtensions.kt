package com.nextchaptersoftware.ml.doc.rerank.utils

import com.nextchaptersoftware.cohere.api.models.CohereDocument
import com.nextchaptersoftware.ml.doc.rerank.models.RerankDocument

object CohereDocumentExtensions {
    fun CohereDocument.asRerankDocument(): RerankDocument {
        return RerankDocument(
            id = id,
            text = text,
            title = title,
            date = date,
            score = score,
            rank = rank,
        )
    }
}
