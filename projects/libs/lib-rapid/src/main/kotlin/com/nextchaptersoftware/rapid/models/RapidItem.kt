package com.nextchaptersoftware.rapid.models

import com.nextchaptersoftware.rapid.types.RapidCollection
import com.nextchaptersoftware.rapid.types.RapidContent
import com.nextchaptersoftware.rapid.types.RapidGroup
import com.nextchaptersoftware.rapid.types.RapidId
import com.nextchaptersoftware.rapid.types.RapidTenant

/**
 * Hierarchical data structure for Rapid items.
 * ```
 *  Tenant              (Org)
 *   └─ Collection      (Installation, etc.)
 *       └─ Group       (Repo, Channel, etc.)
 *           └─ ID      (Primary Object Key)
 * ```
 */
data class RapidItem(
    val tenant: RapidTenant,
    val collection: RapidCollection,
    val group: RapidGroup,
    val id: RapidId,
    val content: RapidContent,
)
