package com.nextchaptersoftware.scm.delegates

import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.scm.models.ScmInstallationAccount

interface ScmUserDelegate {
    suspend fun getScmInstallationAccount(
        orgId: OrgId?,
        identity: Identity,
        externalInstallationId: String,
    ): ScmInstallationAccount?
}
