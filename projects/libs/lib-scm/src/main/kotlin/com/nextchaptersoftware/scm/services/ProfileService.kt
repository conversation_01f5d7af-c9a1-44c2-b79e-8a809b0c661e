package com.nextchaptersoftware.scm.services

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toRepo
import com.nextchaptersoftware.db.models.toScmTeam
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.types.EmailAddress
import kotlinx.coroutines.flow.firstOrNull
import mu.KotlinLogging
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

private val LOGGER = KotlinLogging.logger { }

class ProfileService(
    private val identityStore: IdentityStore = Stores.identityStore,
    private val personStore: PersonStore = Stores.personStore,
    private val scmRepoApiFactory: ScmRepoApiFactory,
    private val scmUserApiFactory: ScmUserApiFactory,
) {

    suspend fun refreshProfileForPerson(personId: PersonId) {
        personStore.getIdentitiesForPerson(personId = personId).forEach { identity ->
            refreshProfileForIdentity(identity)
        }
    }

    suspend fun refreshProfileForIdentityId(identityId: IdentityId) {
        identityStore.findById(identityId = identityId)?.let {
            refreshProfileForIdentity(identity = it)
        }
    }

    private suspend fun refreshProfileForIdentity(identity: Identity) {
        val scm = Scm.fromIdentity(identity = identity)

        scmUserApiFactory.getApiFromIdentity(
            identityId = identity.id,
            scm = scm,
            orgId = null,
        ).also {
            val updatedProfile = it.user()
            identityStore.upsert(
                provider = identity.provider,
                externalId = updatedProfile.externalId,
                displayName = updatedProfile.displayName,
                emails = updatedProfile.emails,
                primaryEmail = updatedProfile.primaryEmail,
                avatarUrl = updatedProfile.avatarUrl,
                htmlUrl = updatedProfile.htmlUrl,
                username = updatedProfile.username,
                externalTeamId = scm.uniqueSignature,
                isBot = updatedProfile.isBot,
            )
        }
    }

    suspend fun refreshProfilesMissingPrimaryEmail() {
        suspendedTransaction {
            identityStore.findSignInIdentitiesMissingPrimaryEmail().forEach { identityId ->
                withLoggingContextAsync("identityId" to identityId) {
                    runSuspendCatching {
                        refreshProfileForIdentityId(identityId)
                        LOGGER.infoAsync { "Refreshed profile for identity" }
                    }.onFailure {
                        LOGGER.errorAsync(it) { "Failed to refresh profile for identity" }
                    }
                }
            }
        }
    }

    suspend fun refreshEmailForMemberIfNeeded(member: Member) {
        val identity = identityStore.findById(identityId = member.identityId)
            ?: run {
                LOGGER.warnAsync { "Identity not found for member" }
                return
            }

        if (identity.primaryEmail != null) {
            LOGGER.debugAsync { "Email already set for identity" }
            return
        }

        val email = discoverEmailForIdentityIfPossible(member, identity)
            ?: run {
                LOGGER.warnAsync { "Email not discovered for identity" }
                return
            }

        identityStore.updateById(identityId = identity.id, primaryEmail = email, emails = listOf(email))
    }

    private suspend fun discoverEmailForIdentityIfPossible(member: Member, identity: Identity): EmailAddress? {
        if (!identity.provider.isScmProvider) {
            LOGGER.warnAsync { "Provider is not an SCM provider" }
            return null
        }

        // Probably possible to do this for other providers as well,
        // just need to confirm the API supports it.
        if (identity.provider !in setOf(Provider.GitHub, Provider.GitHubEnterprise)) {
            LOGGER.warnAsync { "Provider not supported for email discovery" }
            return null
        }

        return discoverEmailForScmIdentity(member, identity)
    }

    private suspend fun discoverEmailForScmIdentity(member: Member, identity: Identity): EmailAddress? {
        val (scmTeam, repo) = findRecentActiveRepo(memberId = member.id)
            ?: run {
                LOGGER.warnAsync { "No recent active repo found for member" }
                return null
            }

        val scmRepoApi = scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = Scm.fromTeam(scmTeam),
        )

        val commit = scmRepoApi.commits(
            author = identity.username,
            includeDiffs = false,
            maxItems = 1,
        ).firstOrNull() ?: run {
            LOGGER.warnAsync { "No recent commit found for member" }
            return null
        }

        return commit.author?.email
    }

    private suspend fun findRecentActiveRepo(memberId: MemberId): Pair<ScmTeam, Repo>? {
        return suspendedTransaction {
            PullRequestModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = RepoModel,
                    otherColumn = RepoModel.id,
                    onColumn = PullRequestModel.repo,
                )
                .join(
                    joinType = JoinType.INNER,
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.id,
                    onColumn = RepoModel.scmTeam,
                ) {
                    AllOp(
                        ScmTeamStore.SCM_TEAM_EXISTS_CLAUSE,
                    )
                }
                .select(ScmTeamModel.columns + RepoModel.columns)
                .whereAll(
                    PullRequestModel.creator eq memberId,
                )
                .orderBy(PullRequestModel.state to SortOrder.DESC, PullRequestModel.createdAt to SortOrder.DESC)
                .limit(1)
                .map { Pair(it.toScmTeam(), it.toRepo()) }
                .firstOrNull()
        }
    }
}
