package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.utils.TestUtils.testMockEngine
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class PullRequestReviewThreadServiceTest {

    private val scmAppApiFactory: ScmAppApiFactory = ScmAppApiFactory(
        scmConfig = ScmConfig.INSTANCE,
    )

    @Test
    fun pullRequestsWithReviewComments() = runTest {
        val mockEngine = testMockEngine {
            val body = getResource(this, "/scm/github/PullRequestReviewThreads.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val results = PullRequestReviewThreadService(
            gitHubClient = scmAppApiFactory.getApi(orgId = null, scm = Scm.GitHub, clientEngine = mockEngine).v4Org("123"),
            owner = "NextChapterSoftware",
            repoName = "unblocked",
        ).pullRequestsWithReviewComments(
            prPageSize = 100,
            prAfterCursor = null,
        )

        assertThat(results.pullRequests).hasSize(6)
        assertThat(results.lastUpdatedAt).isNotNull
    }

    @Disabled("Used for debugging")
    @Test
    fun getToken() = runTest {
        val token = scmAppApiFactory.getApi(orgId = null, scm = Scm.GitHub).v3App().installationAccessToken(
            installationId = "27556539", // GitHub DEV App installed on the NextChapterSoftware org
        )
        println(token)
    }
}
