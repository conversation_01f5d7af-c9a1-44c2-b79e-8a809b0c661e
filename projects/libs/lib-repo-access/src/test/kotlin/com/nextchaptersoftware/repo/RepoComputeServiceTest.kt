package com.nextchaptersoftware.repo

import arrow.fx.coroutines.parMap
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.stores.OrgMemberAndIdentity
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import kotlinx.coroutines.flow.flowOf
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.eq
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoInteractions

class RepoComputeServiceTest : DatabaseTestsBase() {

    private val scmTeamApiFactory = mock<ScmTeamApiFactory>()
    private val scmUserApiFactory = mock<ScmUserApiFactory>()
    private val scmTeamApi = mock<ScmTeamApi>()
    private val scmUserApi = mock<ScmUserApi>()
    private val repoComputeService = LocalRepoComputeService(
        scmTeamApiFactory = scmTeamApiFactory,
        scmUserApiFactory = scmUserApiFactory,
    )

    private lateinit var org: OrgDAO
    private lateinit var team: ScmTeamDAO
    private lateinit var identity: IdentityDAO
    private lateinit var member: MemberDAO
    private lateinit var repo1: Repo
    private lateinit var repo2: Repo

    private suspend fun setup(scm: Scm) {
        org = makeOrg()
        team = makeScmTeam(provider = scm.provider, org = org)
        identity = makeIdentity(provider = scm.provider)
        member = makeMember(scmTeam = team, identity = identity, isCurrentMember = true, isPrimaryMember = true)
        repo1 = makeRepo(scmTeam = team, externalId = "repo1").asDataModel()
        repo2 = makeRepo(scmTeam = team, externalId = "repo2").asDataModel()

        `when`(
            scmTeamApiFactory.getApiFromTeam(
                scmTeam = eq(team.asDataModel()),
                scm = eq(scm),
                clientEngine = anyOrNull(),
            ),
        ).thenReturn(scmTeamApi)

        `when`(
            scmUserApiFactory.getApiFromIdentity(
                orgId = eq(team.asDataModel().orgId),
                identityId = eq(identity.id.value),
                scm = eq(scm),
                clientEngine = anyOrNull(),
            ),
        ).thenReturn(scmUserApi)
    }

    @Test
    fun `repeated sequential calls incur one cache miss -- Bitbucket`() = suspendingDatabaseTest {
        setup(scm = Scm.Bitbucket)

        `when`(
            scmUserApi.repoExternalIds(
                externalInstallationId = eq(checkNotNull(team.providerExternalInstallationId)),
                owner = eq(team.providerLogin),
                isPersonalAccount = eq(false),
            ),
        )
            .thenReturn(flowOf("repo2", "repo3"))

        repeat(4) {
            repoComputeService.getOrComputeRepoAccess(
                scmTeam = team.asDataModel(),
                scmOrgMemberAndIdentity = OrgMemberAndIdentity(
                    member = member.asDataModel(),
                    identity = identity.asDataModel(),
                    org = org.asDataModel(),
                    orgMember = member.orgMember.asDataModel(),
                ),
                forceCompute = false,
            ).also { access ->
                assertThat(access).isEqualTo(RepoAccessResult.Some(setOf(repo2.id)))
            }
        }

        // verify exactly one cache miss
        verify(scmUserApi, times(1)).repoExternalIds(
            externalInstallationId = eq(checkNotNull(team.providerExternalInstallationId)),
            owner = eq(team.providerLogin),
            isPersonalAccount = eq(false),
        )
        verifyNoInteractions(scmTeamApi)
    }

    @Test
    fun `repeated concurrent calls incur one cache miss -- Bitbucket`() = suspendingDatabaseTest {
        setup(scm = Scm.Bitbucket)

        `when`(
            scmUserApi.repoExternalIds(
                externalInstallationId = eq(checkNotNull(team.providerExternalInstallationId)),
                owner = eq(team.providerLogin),
                isPersonalAccount = eq(false),
            ),
        )
            .thenReturn(flowOf("repo2", "repo3"))

        (1..4).parMap {
            repoComputeService.getOrComputeRepoAccess(
                scmTeam = team.asDataModel(),
                scmOrgMemberAndIdentity = OrgMemberAndIdentity(
                    member = member.asDataModel(),
                    identity = identity.asDataModel(),
                    org = org.asDataModel(),
                    orgMember = member.orgMember.asDataModel(),
                ),
                forceCompute = false,
            )
        }.onEach { access ->
            assertThat(access).isEqualTo(RepoAccessResult.Some(setOf(repo2.id)))
        }

        // verify exactly one cache miss
        verify(scmUserApi, times(1)).repoExternalIds(
            externalInstallationId = eq(checkNotNull(team.providerExternalInstallationId)),
            owner = eq(team.providerLogin),
            isPersonalAccount = eq(false),
        )
        verifyNoInteractions(scmTeamApi)
    }

    @Test
    fun `repeated sequential calls incur one cache miss -- GitHub`() = suspendingDatabaseTest {
        setup(scm = Scm.GitHub)

        `when`(scmTeamApi.repoExternalIds(eq(identity.username)))
            .thenReturn(flowOf("repo2", "repo3"))

        repeat(4) {
            repoComputeService.getOrComputeRepoAccess(
                scmTeam = team.asDataModel(),
                scmOrgMemberAndIdentity = OrgMemberAndIdentity(
                    member = member.asDataModel(),
                    identity = identity.asDataModel(),
                    org = org.asDataModel(),
                    orgMember = member.orgMember.asDataModel(),
                ),
                forceCompute = false,
            ).also { access ->
                assertThat(access).isEqualTo(RepoAccessResult.Some(setOf(repo2.id)))
            }
        }

        // verify exactly one cache miss
        verify(scmTeamApi, times(1)).repoExternalIds(eq(identity.username))
        verifyNoInteractions(scmUserApi)
    }

    @Test
    fun `repeated concurrent calls incur one cache miss -- GitHub`() = suspendingDatabaseTest {
        setup(scm = Scm.GitHub)

        `when`(scmTeamApi.repoExternalIds(eq(identity.username)))
            .thenReturn(flowOf("repo2", "repo3"))

        (1..4).parMap {
            repoComputeService.getOrComputeRepoAccess(
                scmTeam = team.asDataModel(),
                scmOrgMemberAndIdentity = OrgMemberAndIdentity(
                    member = member.asDataModel(),
                    identity = identity.asDataModel(),
                    org = org.asDataModel(),
                    orgMember = member.orgMember.asDataModel(),
                ),
                forceCompute = false,
            )
        }.onEach { access ->
            assertThat(access).isEqualTo(RepoAccessResult.Some(setOf(repo2.id)))
        }

        // verify exactly one cache miss
        verify(scmTeamApi, times(1)).repoExternalIds(eq(identity.username))
        verifyNoInteractions(scmUserApi)
    }
}
