package com.nextchaptersoftware.repo

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.cache.RedisRefreshAheadCache
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.OrgMemberAndIdentity
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.failsafe.dsl.retry
import com.nextchaptersoftware.failsafe.execAsync
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import dev.failsafe.Failsafe
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

/**
 * Service for computing the repo access for a team member.
 * Repo access is expensive to compute, so this class is responsible for caching appropriately.
 */
class LocalRepoComputeService(
    private val repoStore: RepoStore = Stores.repoStore,
    private val scmTeamApiFactory: ScmTeamApiFactory,
    private val scmUserApiFactory: ScmUserApiFactory,
) : RepoComputeService {
    companion object {
        private const val NAMESPACE = "RepoComputeService"

        /** The duration for which the computation lock is held */
        private val COMPUTATION_LOCK_EXPIRY = 3.minutes
    }

    private fun cacheKey(memberId: MemberId) = "CACHE:$memberId"

    private fun computationLock(memberId: MemberId): RedisLock {
        val computationLockKey = "$NAMESPACE:LOCK:$memberId"
        return RedisLock(key = computationLockKey, expiration = COMPUTATION_LOCK_EXPIRY)
    }

    /** When the repo access computation is already in progress, wait a reasonable amount of time before giving up. */
    override suspend fun getOrComputeRepoAccess(
        scmTeam: ScmTeam,
        scmOrgMemberAndIdentity: OrgMemberAndIdentity,
        forceCompute: Boolean,
    ): RepoAccessResult {
        @Suppress("MagicNumber")
        val contentionRetryPolicy = retry<RepoAccessResult> {
            delay = 500.milliseconds
            jitterFactor = 0.2
            maxDuration = 10.seconds
            maxRetries = -1 // Retry indefinitely up to maxDuration
            handle = listOf(RepoAccessLockFailed::class.java)
            onRetriesExceeded {
                LOGGER.errorSync(it.exception) { "getRepoAccess retries failed" }
            }
        }

        return Failsafe.with(contentionRetryPolicy).execAsync {
            val key = cacheKey(scmOrgMemberAndIdentity.member.id)
            cache.getOrCompute(key = key, forceCompute = forceCompute) {
                computeRepoAccessExclusively(scmTeam, scmOrgMemberAndIdentity).encode()
            }.decode()
        }
    }

    /**
     * Compute the repo access for a team member.
     *
     * This method is called with a lock held, to ensure that only one computation is in progress at a time.
     * This is necessary because the computation is expensive, and we want to avoid duplicate work.
     *
     * @throws RepoAccessLockFailed if the lock could not be acquired
     */
    private suspend fun computeRepoAccessExclusively(scmTeam: ScmTeam, orgMemberAndIdentity: OrgMemberAndIdentity): RepoAccessResult {
        LOGGER.debugAsync { "computeRepoAccessExclusively" }
        val lock = computationLock(orgMemberAndIdentity.member.id)

        if (!lock.acquire()) {
            LOGGER.debugAsync { "failed to acquire lock" }
            throw RepoAccessLockFailed()
        }

        return try {
            withTimeout(COMPUTATION_LOCK_EXPIRY) {
                computeRepoAccess(scmTeam, orgMemberAndIdentity)
            }
        } finally {
            LOGGER.debugAsync { "release lock" }
            lock.release()
        }
    }

    private suspend fun computeRepoAccess(scmTeam: ScmTeam, orgMemberAndIdentity: OrgMemberAndIdentity): RepoAccessResult {
        val providerExternalInstallationId = scmTeam.providerExternalInstallationId
            ?: return RepoAccessResult.None

        val scm = Scm.fromIdentity(orgMemberAndIdentity.identity)

        val externalIds = when (scm) {
            Scm.GitHub, is Scm.GitHubEnterprise -> scmTeamApiFactory.getApiFromTeam(scmTeam, scm).use { api ->
                api.repoExternalIds(username = orgMemberAndIdentity.identity.username).toList()
            }

            else -> scmUserApiFactory.getApiFromIdentity(
                identityId = orgMemberAndIdentity.identity.id,
                scm = scm,
                orgId = scmTeam.orgId,
            ).use { api ->
                api.repoExternalIds(
                    externalInstallationId = providerExternalInstallationId,
                    owner = scmTeam.providerLogin,
                    isPersonalAccount = scmTeam.providerIsPersonalAccount,
                ).toList()
            }
        }

        val repoIds = repoStore.findByExternalIds(scmTeam.id, externalIds)

        LOGGER.debugAsync(
            "scmTeamId" to scmTeam.id,
            "identityId" to orgMemberAndIdentity.identity.id,
            "externalIdsSize" to externalIds.size,
            "repoIdsSize" to repoIds.size,
        ) { "computeRepoAccess" }

        return RepoAccessResult.Some(repoIds)
    }

    private val cache by lazy {
        RedisRefreshAheadCache(
            namespace = NAMESPACE,
            expireAfter = 30.days,
            refreshAfter = 2.hours,
        )
    }
}
