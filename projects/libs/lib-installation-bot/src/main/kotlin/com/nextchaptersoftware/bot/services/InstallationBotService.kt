package com.nextchaptersoftware.bot.services

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationBotModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.toIdentity
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.InstallationBotStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.nullIfEmpty
import org.jetbrains.exposed.sql.Coalesce
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.like
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.or

class InstallationBotService(
    private val installationBotStore: InstallationBotStore = Stores.installationBotStore,
) {

    suspend fun findBotCandidates(
        installationId: InstallationId,
        match: String?,
        limit: Int,
    ): List<Identity> {
        val tokens = tokenize(match)

        return suspendedTransaction {
            MemberModel
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                    joinType = JoinType.INNER,
                )
                .select(IdentityModel.columns)
                .whereAll(
                    InstallationModel.id eq installationId,
                    InstallationStore.INSTALLATION_EXISTS,
                    IdentityModel.provider eq InstallationModel.provider,
                    MemberStore.IS_CURRENT_MEMBER_CLAUSE,
                    tokens.nullIfEmpty()?.let {
                        tokens.map {
                            (IdentityModel.displayName.lowerCase() like "%$it%") or (IdentityModel.username.lowerCase() like "%$it%")
                        }.reduce { acc, op -> acc and op }
                    },
                )
                .orderBy(Coalesce(IdentityModel.displayName, IdentityModel.username))
                .limit(limit)
                .map { it.toIdentity() }
        }
    }

    private fun tokenize(text: String?): Set<String> {
        return (text ?: "")
            .lowercase()
            .split(Regex("[^a-z0-9]"))
            .mapNotNull { it.trim().nullIfEmpty() }
            .toSet()
    }

    suspend fun selectInstallationBot(
        installationId: InstallationId,
        botIdentityId: IdentityId,
    ) {
        assertBotIsEligible(installationId, botIdentityId)
        installationBotStore.setInstallationBot(
            installationId = installationId,
            botIdentityId = botIdentityId,
        )
    }

    suspend fun assertBotIsEligible(
        installationId: InstallationId,
        identityId: IdentityId,
    ) {
        suspendedTransaction {
            MemberModel
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = MemberModel.identity,
                    joinType = JoinType.INNER,
                )
                .select(MemberModel.id)
                .whereAll(
                    InstallationModel.id eq installationId,
                    IdentityModel.id eq identityId,
                    // Installation must exist
                    InstallationStore.INSTALLATION_EXISTS,
                    // Identity provider must match installation provider
                    IdentityModel.provider eq InstallationModel.provider,
                    // Must be a current member of the installation
                    MemberStore.IS_CURRENT_MEMBER_CLAUSE,
                )
                .firstOrNull()
                ?: throw IllegalArgumentException("Selected identity is not eligible as installation bot")
        }
    }

    suspend fun deselectInstallationBot(
        installationId: InstallationId,
    ) {
        installationBotStore.removeInstallationBot(installationId)
    }

    suspend fun getInstallationBot(
        installationId: InstallationId,
    ): Identity? {
        return suspendedTransaction {
            InstallationBotModel
                .join(
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = InstallationBotModel.installation,
                    joinType = JoinType.INNER,
                ) {
                    InstallationStore.INSTALLATION_EXISTS
                }
                .join(
                    otherTable = IdentityModel,
                    otherColumn = IdentityModel.id,
                    onColumn = InstallationBotModel.identity,
                    joinType = JoinType.INNER,
                )
                .join(
                    otherTable = MemberModel,
                    otherColumn = MemberModel.identity,
                    onColumn = InstallationBotModel.identity,
                    joinType = JoinType.INNER,
                ) {
                    AllOp(
                        MemberModel.installation eq installationId,
                        MemberStore.IS_CURRENT_MEMBER_CLAUSE,
                    )
                }
                .select(InstallationBotModel.columns + IdentityModel.columns)
                .whereAll(InstallationBotModel.installation eq installationId)
                .firstOrNull()?.toIdentity()
        }
    }
}
