package com.nextchaptersoftware.access

import com.nextchaptersoftware.db.models.Provider
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

internal class RestrictAccessConfigTest {

    @Test
    fun `PROD config must allow all`() {
        val config = RestrictedAccessConfig.getTestInstance(overrideEnvironment = "prod", overrideUser = "nobody")
        assertThat(config.allowedAll).isTrue
        assertThat(config.allowedAllHosts).isTrue
        assertThat(config.allowedAllOrgs).isTrue
        assertThat(config.allowedAllUsers).isTrue
    }

    @Test
    fun `DEV config must not allow all`() {
        val config = RestrictedAccessConfig.getTestInstance(overrideEnvironment = "dev", overrideUser = "nobody")
        assertThat(config.allowedAll).isFalse
        assertThat(config.allowedAllHosts).isFalse
        assertThat(config.allowedAllOrgs).isFalse
        assertThat(config.allowedAllUsers).isFalse
    }

    @Test
    fun `DEV config restricts hosts`() {
        val config = RestrictedAccessConfig.getTestInstance(overrideEnvironment = "dev", overrideUser = "nobody")
        assertThat(config.allowedAllHosts).isFalse
        assertThat(config.allowedProviderHosts).containsExactlyInAnyOrder(
            "atlassian-test.secops.getunblocked.com",
            "ghe.secops.getunblocked.com",
            "gitlab.secops.getunblocked.com",
        )
    }

    @Test
    fun `DEV config restricts orgs`() {
        val config = RestrictedAccessConfig.getTestInstance(overrideEnvironment = "dev", overrideUser = "nobody")
        assertThat(config.allowedAllOrgs).isFalse
        assertThat(config.allowedProviderOrgs.keys).containsExactlyInAnyOrder(
            Provider.AzureDevOps,
            Provider.Bitbucket,
            Provider.GitHub,
            Provider.GitLab,
        )
        assertThat(config.allowedProviderOrgs[Provider.AzureDevOps]).containsExactlyInAnyOrder(
            "115684ec-4f74-4446-ba4f-fed91bce9a09",
            "20eb838e-2d1e-4735-a621-1c76db71ae58",
            "2881b307-b045-461f-af6e-700f9bf3b734",
            "55f79e4f-4790-44e3-b851-f4a9b95933ff",
            "64d893b5-0cd4-4bc8-97e2-ca8dac4105f0",
            "8907cb0d-21fd-405a-a5a0-7c66166965d4",
            "8e426568-ffd1-4674-8178-901716790864",
            "be0e5863-803a-4ded-acad-d79d4c722beb",
            "c0ad5cbb-3614-4738-a314-1a0021d11ed4",
            "d8f3ca20-4031-41c7-9c2c-40f2e28564d1",
            "e28638f7-9630-464d-8ee4-2603e7527d96",
        )
        assertThat(config.allowedProviderOrgs[Provider.Bitbucket]).containsExactlyInAnyOrder(
            "{0788b6e4-43c2-4490-8001-3a9034ca387b}",
            "{09f4095f-d0a0-4472-8123-293773db3a26}",
            "{0b5fd65d-658b-4174-a6e1-74f524466eea}",
            "{0ccacbd7-35ce-4343-8757-5cfbfd1e95bd}",
            "{2ecda499-6913-45c7-b8a4-4d7dd27beb27}",
            "{64fbc8b1-f3a1-4297-9ca4-ac19ee4af6c8}",
            "{79f8f678-37d1-4d28-8ce1-e33d0f063c81}",
            "{9261c8df-baad-466b-b384-d546e87bd895}",
            "{e5d4a9d0-3bc7-4860-a530-3e3509ba3431}",
            "{e729481b-a207-4fe8-9512-5be661b9d082}",
            "{c3bdbd36-1abe-44b5-8548-096385777599}",
        )
        assertThat(config.allowedProviderOrgs[Provider.GitHub]).containsExactlyInAnyOrder(
            "19292465",
            "27134756",
            "73629892",
            "91906527",
            "98424745",
            "107499042",
            "107571419",
            "108355141",
            "123202210",
            "125993084",
            "130709988",
            "147771720",
            "152916612",
            "159066470",
            "159475012",
            "177649968",
        )
        assertThat(config.allowedProviderOrgs[Provider.GitLab]).containsExactlyInAnyOrder(
            "14744107",
            "63439188",
            "80738308",
            "80974989",
        )
    }

    @Test
    fun `DEV config restricts users`() {
        val config = RestrictedAccessConfig.getTestInstance(overrideEnvironment = "dev", overrideUser = "nobody")
        assertThat(config.allowedAllUsers).isFalse
        assertThat(config.allowedProviderUsers.keys).containsExactlyInAnyOrder(
            Provider.AzureDevOps,
            Provider.Bitbucket,
            Provider.GitHub,
            Provider.GitLab,
        )
        assertThat(config.allowedProviderUsers[Provider.AzureDevOps]).containsExactlyInAnyOrder(
            "aad.OGU1ODc5NjQtODMxNi03MjVjLWIwOWQtYjI0ZjA0ZmJjYWE3",
            "aad.OGVjYTI4N2QtMThjZS03NjFhLTk3ZmEtMWU0NzUzYzk2YjVh",
            "aad.OTA1M2NkZmYtZjc4Mi03OTU0LThjNTgtYWNjMDFhODgzMDg2",
            "aad.ZmRhODNmZTQtMWI1NS03YzU4LThjNmQtNjUwMGFjODBjZGY5",
            "msa.MDU2YThjY2UtODcwNC03MmM4LWI5OTYtMjNjYTljNzFjNDQz",
            "msa.MGNiNDQxNWYtNjc0My03YjdhLTk2YTItNDM0NDdhMjc0NThi",
            "msa.NWIxNDI1MzMtNjg3Yy03OTM0LTg4YTQtZTNmYWU3ZjgwMTc5",
            "msa.YTE2YzU2OTQtZDEzNy03ZTFjLWE1MGQtNzQ5ZjJhN2QzNTE2",
            "msa.ZDNiZjdmMjAtNDAxZC03NGJjLThiNjAtZGZhZmFhNTg5YTQ2",
            "msa.ZmY2Y2YwYWItY2Q4Ny03ZTI2LWEwOTctOWE4YWY5N2RkOGNl",
        )
        assertThat(config.allowedProviderUsers[Provider.Bitbucket]).containsExactlyInAnyOrder(
            "557058:512a45a9-29ef-44af-9d7f-61f45928ec30",
            "557058:75aef400-dac6-44e3-846c-53c7ff639027",
            "557058:7d95ed56-d71e-449e-bd5a-a6256326105c",
            "557058:81459e29-04ca-496b-9d6f-52df9e1684cb",
            "557058:ad6ab1b8-ade7-4dd7-8e67-504e792a9171",
            "63ea8d19c5061c632c0c8c31",
            "64068aa828478663100069b7",
            "64134b421273131f2ae11471",
            "6418dc3c5534b0bf74414b54",
            "712020:2886b6e9-a8af-467c-a9e4-dc9234c4e944",
            "712020:2dd18615-f7c0-4d6c-8d69-435c149d116c",
            "712020:6bc4a489-1eab-45d9-b2a0-657d3e930841",
            "712020:bc9e2186-aa88-48cc-9ca7-051f65ffa165",
            "712020:c2330d23-69ff-45e7-8015-f6044164224e",
            "712020:d0e00096-871c-49c2-8b06-d1d8bd98ff20",
            "{0b5fd65d-658b-4174-a6e1-74f524466eea}",
        )
        assertThat(config.allowedProviderUsers[Provider.GitHub]).containsExactlyInAnyOrder(
            "279389",
            "306293",
            "332509",
            "387044",
            "858772",
            "1553313",
            "1798345",
            "1924615",
            "2133518",
            "3806658",
            "13353189",
            "13431372",
            "21088373",
            "26414618",
            "94733135",
            "94769088",
            "95773133",
            "107140760",
            "107571031",
            "107573188",
            "125993182",
            "127981783",
            "134032938",
            "135359238",
            "146791528",
            "148585981",
            "152910464",
            "165066257",
        )
        assertThat(config.allowedProviderUsers[Provider.GitLab]).containsExactlyInAnyOrder(
            "290258",
            "311239",
            "847809",
            "882857",
            "13651997",
            "14270127",
            "14744107",
            "19837046",
        )
    }
}
