## Environment Access Control
##
## This file contains the set of public provider orgs, public provider users, and on-premise hosts
## that are allowed to access the environment.
allowedAll: false

## Allowed public orgs
allowedAllOrgs: false
allowedProviderOrgs: {
    "AzureDevOps": [
        "115684ec-4f74-4446-ba4f-fed91bce9a09" # https://dev.azure.com/codeswell-software/Codeswell%20Software
        "20eb838e-2d1e-4735-a621-1c76db71ae58" # https://dev.azure.com/CleanAutomotion/Backend
        "2881b307-b045-461f-af6e-700f9bf3b734" # https://dev.azure.com/CleanAutomotion/Car%20App
        "55f79e4f-4790-44e3-b851-f4a9b95933ff" # https://dev.azure.com/NextChapterSoftware/ReallyStuck
        "64d893b5-0cd4-4bc8-97e2-ca8dac4105f0" # https://dev.azure.com/always-warm-software/heater-dashboard
        "8907cb0d-21fd-405a-a5a0-7c66166965d4" # https://dev.azure.com/codeswell-software/Surf%20Quest
        "8e426568-ffd1-4674-8178-901716790864" # https://dev.azure.com/always-warm-software/heated-gloves
        "be0e5863-803a-4ded-acad-d79d4c722beb" # https://dev.azure.com/OldFashionSoftware/RetroEmulator
        "c0ad5cbb-3614-4738-a314-1a0021d11ed4" # https://dev.azure.com/NextChapterSoftware/Unblocked
        "d8f3ca20-4031-41c7-9c2c-40f2e28564d1" # https://dev.azure.com/CleanAutomotion/Truck%20App
        "e28638f7-9630-464d-8ee4-2603e7527d96" # https://dev.azure.com/SouthStarSoftware/Compass
    ]
    "Bitbucket": [
        "{0788b6e4-43c2-4490-8001-3a9034ca387b}"    # https://bitbucket.org/anotheronefordennispi/
        "{09f4095f-d0a0-4472-8123-293773db3a26}"    # https://bitbucket.org/chapter2/
        "{0b5fd65d-658b-4174-a6e1-74f524466eea}"    # https://bitbucket.org/richiebres/
        "{0ccacbd7-35ce-4343-8757-5cfbfd1e95bd}"    # https://bitbucket.org/un-test-onboarding/
        "{2ecda499-6913-45c7-b8a4-4d7dd27beb27}"    # https://bitbucket.org/peter_werry/
        "{64fbc8b1-f3a1-4297-9ca4-ac19ee4af6c8}"    # https://bitbucket.org/matt-test-bb/
        "{79f8f678-37d1-4d28-8ce1-e33d0f063c81}"    # https://bitbucket.org/getunblocked/
        "{9261c8df-baad-466b-b384-d546e87bd895}"    # https://bitbucket.org/codeswell-software/
        "{e5d4a9d0-3bc7-4860-a530-3e3509ba3431}"    # https://bitbucket.org/oldfashionsoftware/
        "{e729481b-a207-4fe8-9512-5be661b9d082}"    # https://bitbucket.org/always-warm-software/
        "{c3bdbd36-1abe-44b5-8548-096385777599}"    # https://bitbucket.org/testunblocked/
    ]
    "GitHub": [
        "19292465"      # https://github.com/Bens-Test-Organization
        "27134756"      # https://github.com/woshishidashu
        "73629892"      # https://github.com/dust-engine
        "91906527"      # https://github.com/NextChapterSoftware
        "98424745"      # https://github.com/GetUnblocked
        "107499042"     # https://github.com/OldFashionSoftware
        "107571419"     # https://github.com/TheUnblockedDemo
        "108355141"     # https://github.com/unblocked-tests
        "123202210"     # https://github.com/nrsh-society
        "125993084"     # https://github.com/dennispi-testOrg
        "130709988"     # https://github.com/get-unblocked
        "147771720"     # https://github.com/try-unblocked
        "152916612"     # https://github.com/Slack-Review-Organization
        "159066470"     # https://github.com/Always-warm-software
        "159475012"     # https://github.com/SlackDemoForReview
        "177649968"     # https://github.com/NewAbstractSoftware
    ]
    "GitLab": [
        "14744107"      # https://gitlab.com/matt.test.9827348
        "63439188"      # https://gitlab.com/groups/getunblocked
        "80738308"      # https://gitlab.com/mattsgroup2400902
        "80974989"      # https://gitlab.com/HarmonyQuest
    ]
}

## Allowed public users
allowedAllUsers: false
allowedProviderUsers: {
    "AzureDevOps": [
        "aad.OGU1ODc5NjQtODMxNi03MjVjLWIwOWQtYjI0ZjA0ZmJjYWE3" # Ben (<EMAIL>)
        "aad.OGVjYTI4N2QtMThjZS03NjFhLTk3ZmEtMWU0NzUzYzk2YjVh" # Jeff (<EMAIL>)
        "aad.OTA1M2NkZmYtZjc4Mi03OTU0LThjNTgtYWNjMDFhODgzMDg2" # Martin (<EMAIL>)
        "aad.ZmRhODNmZTQtMWI1NS03YzU4LThjNmQtNjUwMGFjODBjZGY5" # Martin Test (<EMAIL>)
        "msa.MDU2YThjY2UtODcwNC03MmM4LWI5OTYtMjNjYTljNzFjNDQz" # Matt Adam (<EMAIL>)
        "msa.MGNiNDQxNWYtNjc0My03YjdhLTk2YTItNDM0NDdhMjc0NThi" # ben ng45 (<EMAIL>)
        "msa.NWIxNDI1MzMtNjg3Yy03OTM0LTg4YTQtZTNmYWU3ZjgwMTc5" # Richie ??
        "msa.YTE2YzU2OTQtZDEzNy03ZTFjLWE1MGQtNzQ5ZjJhN2QzNTE2" # jeff (<EMAIL>)
        "msa.ZDNiZjdmMjAtNDAxZC03NGJjLThiNjAtZGZhZmFhNTg5YTQ2" # Richie (<EMAIL>)
        "msa.ZmY2Y2YwYWItY2Q4Ny03ZTI2LWEwOTctOWE4YWY5N2RkOGNl" # Martin (<EMAIL>)
    ]
    "Bitbucket": [
        "557058:512a45a9-29ef-44af-9d7f-61f45928ec30"   # Ben (gmail)
        "557058:75aef400-dac6-44e3-846c-53c7ff639027"   # Peter
        "557058:7d95ed56-d71e-449e-bd5a-a6256326105c"   # Richie
        "557058:81459e29-04ca-496b-9d6f-52df9e1684cb"   # Jeff
        "557058:ad6ab1b8-ade7-4dd7-8e67-504e792a9171"   # Richie
        "63ea8d19c5061c632c0c8c31"                      # Richie
        "64068aa828478663100069b7"                      # Jeff
        "64134b421273131f2ae11471"                      # Doug
        "6418dc3c5534b0bf74414b54"                      # Dave
        "712020:2886b6e9-a8af-467c-a9e4-dc9234c4e944"   # Matt (matt.test.9827348)
        "712020:2dd18615-f7c0-4d6c-8d69-435c149d116c"   # Kay
        "712020:6bc4a489-1eab-45d9-b2a0-657d3e930841"   # Matt (<EMAIL>)
        "712020:bc9e2186-aa88-48cc-9ca7-051f65ffa165"   # Mrtn
        "712020:c2330d23-69ff-45e7-8015-f6044164224e"   # Ben (iCloud)
        "712020:d0e00096-871c-49c2-8b06-d1d8bd98ff20"   # Dennis
        "{0b5fd65d-658b-4174-a6e1-74f524466eea}"        # Richie
    ]
    "GitHub": [
        "279389"        # https://github.com/pmn
        "306293"        # https://github.com/narayans
        "332509"        # https://github.com/cancelself
        "387044"        # https://github.com/dennispi
        "858772"        # https://github.com/pwerry
        "1553313"       # https://github.com/jeffrey-ng
        "1798345"       # https://github.com/richiebres
        "1924615"       # https://github.com/davidkwlam
        "2133518"       # https://github.com/matthewjamesadam
        "3806658"       # https://github.com/rasharab
        "13353189"      # https://github.com/benedict-jw
        "13431372"      # https://github.com/kaych
        "21088373"      # https://github.com/adamsmythe
        "26414618"      # https://github.com/benedict-bb
        "94733135"      # https://github.com/mahdi-torabi
        "94769088"      # https://github.com/UnblockedDemo
        "95773133"      # https://github.com/dkwlam
        "107140760"     # https://github.com/jeffUBTest
        "107571031"     # https://github.com/dennispiUB
        "107573188"     # https://github.com/AppleUB
        "125993182"     # https://github.com/dennispigithub
        "127981783"     # https://github.com/v-cancelself
        "134032938"     # https://github.com/kayubtest
        "135359238"     # https://github.com/matt-test-9827348
        "146791528"     # https://github.com/matt-test-7727
        "148585981"     # https://github.com/reviewslack
        "152910464"     # https://github.com/claire-gong-18
        "165066257"     # https://github.com/martin-ncs
    ]
    "GitLab": [
        "290258"        # https://gitlab.com/benedict-jw1
        "311239"        # https://gitlab.com/richiebres
        "847809"        # https://gitlab.com/davidkwlam
        "882857"        # https://gitlab.com/jeffrey-ng
        "13651997"      # https://gitlab.com/richie-block
        "14270127"      # https://gitlab.com/doug8204715
        "14744107"      # https://gitlab.com/matt.test.9827348
        "19837046"      # https://gitlab.com/benedict-icloud
    ]
}

## Allowed On-Premise Provider Hosts
## All orgs and users from these on-premise hosts will be allowed.
allowedAllHosts: false
allowedProviderHosts: [
    "atlassian-test.secops.getunblocked.com"
    "ghe.secops.getunblocked.com"
    "gitlab.secops.getunblocked.com"
]
