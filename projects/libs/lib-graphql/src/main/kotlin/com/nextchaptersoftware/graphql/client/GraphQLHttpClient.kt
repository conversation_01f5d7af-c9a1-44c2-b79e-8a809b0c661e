package com.nextchaptersoftware.graphql.client

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.client.org.orgHttpClient
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.UserAgent
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation

object GraphQLHttpClient {
    fun create(
        orgId: OrgId?,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): HttpClient {
        val configure: HttpClientConfig<*>.() -> Unit = {
            install(ContentNegotiation) {
                installJsonSerializer()
            }
            install(UserAgent) {
                agent = "un-blocked"
            }
            install(HttpRequestRetry)
            install(KtorClientTracing)
            expectSuccess = true
        }

        return if (orgId == null) {
            HttpClient(engine = clientEngine, block = configure)
        } else {
            orgHttpClient(orgId = orgId, engine = clientEngine, configure = configure)
        }
    }
}
