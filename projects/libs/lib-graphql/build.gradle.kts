plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
}

dependencies {
    // GraphQL
    implementation(libs.expediagroup.graphql.ktor.client) {
        exclude("com.expediagroup:graphql-kotlin-client-jackson")
    }
    implementation(libs.expediagroup.graphql.ktor.client.serialization)

    // Client
    implementation(libs.bundles.ktor.client)

    implementation(project(":projects:libs:lib-api-generated", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor-client-engine", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor-client-org", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-ktor", configuration = "default"))

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)
}
