package com.nextchaptersoftware.ml.embedding.pinecone.store

import com.google.protobuf.util.JsonFormat
import com.nextchaptersoftware.ml.embedding.core.store.filter.Or
import com.nextchaptersoftware.ml.embedding.core.store.filter.filter
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

internal class PineconeMetadataFilterMapperTest {

    private val printer: JsonFormat.Printer = JsonFormat.printer()
        .alwaysPrintFieldsWithNoPresence()
        .omittingInsignificantWhitespace()

    @Nested
    inner class Atomic {
        @Test
        fun testEqualityPredicate() {
            val expected = "{\"key\":{\"\$eq\":5.0}}"
            val f = filter { eq("key", 5) }
            val actual = PineconeMetadataFilterMapper.map(f)
            assertThat(printer.print(actual)).isEqualTo(expected)
        }

        @Test
        fun testGreaterThanPredicate() {
            val expected = "{\"key\":{\"\$gt\":1.0}}"
            val f = filter { gt("key", 1) }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testInPredicate() {
            val expected = "{\"key\":{\"\$in\":[\"a\",\"b\"]}}"
            val f = filter { isIn("key", listOf("a", "b")) }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }
    }

    @Nested
    inner class BinaryCombine {
        @Test
        fun testAndCombination() {
            val expected = "{\"\$and\":[{\"key\":{\"\$gt\":1.0}},{\"key\":{\"\$lt\":10.0}}]}"
            val f = filter {
                gt("key", 1)
                lt("key", 10)
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testOrCombination() {
            val expected = "{\"\$or\":[{\"key\":{\"\$gt\":1.0}},{\"key\":{\"\$lt\":10.0}}]}"
            val f = filter(operator = ::Or) {
                gt("key", 1)
                lt("key", 10)
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }
    }

    @Nested
    inner class NestedLogical {
        @Suppress("MaxLineLength")
        @Test
        fun testAndOfAndOrCombination() {
            val expected = "{\"\$and\":[{\"\$and\":[{\"key\":{\"\$gt\":1.0}},{\"key\":{\"\$lt\":10.0}}]},{\"\$or\":[{\"key2\":{\"\$eq\":\"foo\"}},{\"key2\":{\"\$ne\":\"bar\"}}]}]}"
            val f = filter {
                and {
                    gt("key", 1)
                    lt("key", 10)
                }
                or {
                    eq("key2", "foo")
                    neq("key2", "bar")
                }
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testAndOfAndIsInCombination() {
            val expected = "{\"\$and\":[{\"\$and\":[{\"key\":{\"\$gt\":1.0}},{\"key\":{\"\$lt\":10.0}}]},{\"key2\":{\"\$in\":[\"foo\",\"bar\"]}}]}"
            val f = filter {
                and {
                    gt("key", 1)
                    lt("key", 10)
                }
                isIn("key2", listOf("foo", "bar"))
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testNotAndToOrCombination() {
            val expected = "{\"\$or\":[{\"key\":{\"\$lte\":1.0}},{\"key\":{\"\$gte\":10.0}}]}"
            val f = filter {
                not {
                    gt("key", 1)
                    lt("key", 10)
                }
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Suppress("MaxLineLength")
        @Test
        fun testOrWithMultipleIsInConditions() {
            val expected = "{\"\$and\":[{\"\$and\":[{\"\$or\":[{\"isPrivate\":{\"\$ne\":true}},{\"label\":{\"\$in\":[\"test\"]}}]},{\"author\":{\"\$in\":[\"peter\"]}}]},{\"tag\":{\"\$in\":[\"featured\",\"breaking\"]}}]}"
            val f = filter {
                or {
                    neq("isPrivate", true)
                    isIn("label", listOf("test"))
                }
                isIn("author", listOf("peter"))
                isIn("tag", listOf("featured", "breaking"))
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }
    }

    @Nested
    inner class NegationAtomic {
        @Test
        fun testNotGreaterThanToLessThanOrEqual() {
            val expected = "{\"key\":{\"\$lte\":1.0}}"
            val f = filter {
                not { gt("key", 1) }
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testNotInToNotIn() {
            val expected = "{\"key\":{\"\$nin\":[\"a\",\"b\"]}}"
            val f = filter {
                not { isIn("key", listOf("a", "b")) }
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }

        @Test
        fun testDoubleNotNegation() {
            val expected = "{\"key\":{\"\$eq\":42.0}}"
            val f = filter {
                not {
                    not { eq("key", 42) }
                }
            }
            assertThat(printer.print(PineconeMetadataFilterMapper.map(f))).isEqualTo(expected)
        }
    }
}
