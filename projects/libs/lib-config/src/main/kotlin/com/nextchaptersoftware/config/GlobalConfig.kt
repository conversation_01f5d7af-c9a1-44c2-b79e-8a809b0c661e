package com.nextchaptersoftware.config

import com.sksamuel.hoplite.Secret
import java.io.File
import kotlin.time.Duration
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
enum class DatabaseSchemaUpdateLock { Standard, Platform }

/**
 * Serial: Prevents deadlocks
 * Batch: Faster but will likely incur deadlock at load
 */
@Serializable
enum class DatabaseSchemaUpdate { Serial, SerialDepth, Batch, Flyway }

@Serializable
data class DiagnosticsConfig(
    val enableCoroutineDump: Boolean,
)

@Serializable
data class OpenRouterConfig(
    @Contextual val apiKey: Secret,
    val baseApiUri: String,
    val defaultTimeout: Duration,
)

@Serializable
data class AnthropicConfig(
    @Contextual val apiKey: Secret,
    val baseApiUri: String,
    val defaultTimeout: Duration,
)

@Serializable
data class AsanaConfig(
    val baseApiUri: String,
    @Contextual val clientSecret: Secret,
    val oauth: OAuthConfig,
)

@Serializable
data class CohereConfig(
    @Contextual val apiKey: Secret,
    val baseApiUri: String,
    val completionTimeout: Duration,
    val rerankTimeout: Duration,
)

@Serializable
data class LinearConfig(
    val baseApiUri: String,
    @Contextual val clientSecret: Secret,
    @Contextual val signingSecret: Secret,
    val oauth: OAuthConfig,
)

@Serializable
data class NotionConfig(
    val baseApiUri: String,
    @Contextual val clientSecret: Secret,
    @Contextual val signingSecret: Secret,
    val oauth: OAuthConfig,
)

@Serializable
data class GoogleDriveConfig(
    @Contextual val clientSecret: Secret,
    val oauth: OAuthConfig,
    val showDataSharingWarning: Boolean,
)

@Serializable
data class StackOverflowTeamsConfig(
    val baseApiUri: String,
)

@Serializable
data class CodaConfig(
    val baseApiUri: String,
    val webhookUrl: String,
)

@Serializable
data class MachineLearningTransformersConfig(
    val mistralChatPath: String,
    val llamaChatPath: String,
)

@Serializable
data class MachineLearningEmbeddingsConfig(
    val e5MistralPath: String,
)

@Serializable
data class MachineLearningModelsConfig(
    val evalPath: String,
    val topicMappingPath: String,
    val graphRagPath: String,
    val docMarkdownPath: String,
)

@Serializable
data class MachineLearningDeploymentConfig(
    val baseApiUri: String,
)

@Serializable
data class MachineLearningConfig(
    val defaultDeployment: MachineLearningDeploymentConfig,
    val gCloudDeployment: MachineLearningDeploymentConfig? = null,
    val transformers: MachineLearningTransformersConfig,
    val embeddings: MachineLearningEmbeddingsConfig,
    val models: MachineLearningModelsConfig,
)

@Serializable
data class OpenAIConfig(
    @Contextual val apiKey: Secret,
    val defaultTimeout: Duration,
)

@Serializable
data class AzureOpenAIDeploymentIdConfig(
    val id: String,
    val supportedRegions: List<String>,
)

@Serializable
data class AzureOpenAIDeploymentConfig(
    @Contextual val apiKey: Secret,
    val resourceName: String,
    val apiVersion: String,
)

@Serializable
data class AzureOpenAIConfig(
    val defaultTimeout: Duration,
    val gpt4OmniDeploymentId: AzureOpenAIDeploymentIdConfig,
    val gpt4OmniMiniDeploymentId: AzureOpenAIDeploymentIdConfig,
    val gpt41DeploymentId: AzureOpenAIDeploymentIdConfig,
    val gpt41MiniDeploymentId: AzureOpenAIDeploymentIdConfig,
    val gpt41NanoDeploymentId: AzureOpenAIDeploymentIdConfig,
    val cneDeployment: AzureOpenAIDeploymentConfig,
    val eusDeployment: AzureOpenAIDeploymentConfig,
    val eus2Deployment: AzureOpenAIDeploymentConfig,
    val ncusDeployment: AzureOpenAIDeploymentConfig,
    val uswDeployment: AzureOpenAIDeploymentConfig,
)

@Serializable
data class PineconeConfig(
    @Contextual val apiKey: Secret,
    val defaultTimeout: Duration,
    val maxRetries: Int,
)

@Serializable
data class ActiveMQEndpoint(
    val hostName: String,
    val port: Int,
)

@Serializable
data class ActiveMQConfig(
    val primary: ActiveMQEndpoint,
    val endpoints: List<ActiveMQEndpoint>?,
    val userName: String,
    @Contextual val password: Secret,
    val passwordFile: String?,
    val useSsl: Boolean,
    val maxConnections: Int,
    val queuePrefetch: Int,
)

@Serializable
data class DatabaseConnectionPoolConfig(
    val minIdle: Int,
    val maxIdle: Int,
    val maxTotal: Int,
    val initialSize: Int,
    val maxConnLifetime: Duration,
)

@Serializable
data class DatabaseFlywayMigrationConfig(
    val baselineOnMigrate: Boolean,
    val baselineVersion: String,
    val lockRetryCount: Int,
    val migrationLocations: List<String>,
    val migrationResourceLocation: String,
    val schemaHistoryTable: String,
)

@Serializable
data class DatabaseConfig(
    val hostName: String,
    val dbName: String,
    val port: Int,
    val userName: String,
    @Contextual val password: Secret,
    val region: String,
    val reWriteBatchedInserts: Boolean,
    val enableStsAuth: Boolean,
    val enableSqlLogging: Boolean,
    val initializeSchemas: Boolean,
    val schemaUpdateLock: DatabaseSchemaUpdateLock,
    val schemaUpdate: DatabaseSchemaUpdate,
    val connectionPool: DatabaseConnectionPoolConfig?,
    val flywayMigration: DatabaseFlywayMigrationConfig,
)

@Serializable
data class HostConfig(val hostName: String, val port: Int?, val schemes: List<String>) {
    val fullyQualifiedName by lazy {
        port?.let {
            "$hostName:$port"
        } ?: hostName
    }
}

@Serializable
data class CORSConfig(
    val hosts: List<HostConfig>,
    val methods: List<String>,
    val headers: List<String>,
    val exposeHeaders: List<String>,
    val allowCredentials: Boolean,
)

@Serializable
data class RpcEndpointConfig(
    val hostname: String,
    val port: Int,
    val path: String?,
)

@Serializable
data class RpcGlobalConfig(
    val proxyProvider: RpcEndpointConfig,
    val serviceMermaid: RpcEndpointConfig,
)

@Serializable
data class SslEngineConfig(
    // certificates
    @Contextual val rootCrt: File,
    @Contextual val serverCrt: File,
    @Contextual val serverKey: File,
    // listener
    val port: Int,
    val enabled: Boolean,
)

@Serializable
data class DeploymentConfig(
    val hostName: String,
    val port: Int,
    val targetPort: Int,
    val enableTLS: Boolean,
    val basePath: String?,
    val sslEngine: SslEngineConfig? = null,
)

@Serializable
data class DocsPageConfig(
    val hostName: String,
    val enableTLS: Boolean,
)

@Serializable
data class BlogPageConfig(
    val hostName: String,
    val enableTLS: Boolean,
    val basePath: String?,
)

@Serializable
data class SurveyPageConfig(
    val hostName: String,
    val enableTLS: Boolean,
)

@Serializable
data class IntercomConfig(
    @Contextual val apiKey: Secret,
    @Contextual val identityVerificationSecret: Secret,
)

@Serializable
data class SlackConfig(
    val appId: String,
    val appHostName: String,
    @Contextual val signingSecret: Secret,
    @Contextual val clientSecret: Secret,
    val apiHostName: String,
    val oauth: OAuthConfig,
    val openId: OAuthConfig,
    val openIdSignIn: OAuthConfig,
    val slackTeamId: String,
    @Contextual val botAuthToken: Secret,
)

@Serializable
data class SensitiveLoggingConfig(
    val dynamoDB: DynamoDBConfig,
    val ttl: Duration,
)

@Serializable
data class JiraConfig(
    val appId: String,
    @Contextual val clientSecret: Secret,
    val apiHostName: String,
    val atlassianConnectAppKey: String,
    val baseUrlOverride: String?,
    val oauth: OAuthConfig,
    val forgeAppId: String,
)

@Serializable
data class ConfluenceConfig(
    val appId: String,
    val apiHostName: String,
    @Contextual val clientSecret: Secret,
    val oauth: OAuthConfig,
)

@Serializable
data class ProvidersConfig(
    val asana: AsanaConfig?,
    val coda: CodaConfig,
    val confluence: ConfluenceConfig?,
    val googleDrive: GoogleDriveConfig?,
    val jira: JiraConfig?,
    val linear: LinearConfig?,
    val notion: NotionConfig?,
    val slack: SlackConfig,
    val slackUAT: SlackConfig?,
    val stackOverflowTeams: StackOverflowTeamsConfig,
    val exchangeTokenExpiry: Duration,
)

@Serializable
data class AuthenticationConfig(
    val authTokenExpiry: Duration,
    val refreshTokenExpiry: Duration,
    val exchangeTokenExpiry: Duration,
    @Contextual val tokenPrivateKey: Secret,
    @Contextual val tokenPublicKey: Secret,
    val authRedirectUrl: String,
    val authRedirectAltUrls: List<String>,
    val useSecretCookie: Boolean,
    val secretCookieMaxAge: Int,
    val secureCookies: Boolean,
    val sameSite: String,
    val tokenIssuer: String,
    val ssoLoginUrl: String,
    val scmEnterpriseLoginUrl: String,
)

@Serializable
data class RedisConfig(
    val hostName: String,
    val userName: String,
    val password: String,
    val passwordFile: String?,
    val port: Int,
    val useSsl: Boolean,
    val useCluster: Boolean,
)

@Serializable
data class AWSBucketConfig(
    val bucket: String,
    val region: String,
)

@Serializable
data class QueueConfig(
    val region: String,

    val asanaEventsQueueName: String,
    val billingEventsQueueName: String,
    val ciBuildEventsQueueName: String,
    val ciTriageEventsQueueName: String,
    val ciProjectEventsQueueName: String,
    val codaEventsQueueName: String,
    val confluenceEventsQueueName: String,
    val dataEventsQueueName: String,
    val documentEventsQueueName: String,
    val embeddingEventsQueueName: String,
    val expertEventsQueueName: String,
    val feedbackAnalysisQueueName: String,
    val googleEventsQueueName: String,
    val hooksCiQueueName: String,
    val hooksScmQueueName: String,
    val hooksSlackQueueName: String,
    val hooksStripeQueueName: String,
    val hooksTranscriptionQueueName: String,
    val jiraEventsQueueName: String,
    val linearEventsQueueName: String,
    val maintenanceEventsQueueName: String,
    val migrationEventsQueueName: String,
    val mlRouterEventsQueueName: String,
    val notificationEventsQueueName: String,
    val notionEventsQueueName: String,
    val pullRequestArchiveQueueName: String,
    val pullRequestIngestionQueueName: String,
    val pullRequestSummariesIngestionQueueName: String,
    val pullRequestSummaryIngestionQueueName: String,
    val regressionTestingEventsQueueName: String,
    val scmEventsQueueName: String,
    val searchEventsQueueName: String,
    val searchIndexingEventsQueueName: String,
    val searchPriorityEventsQueueName: String,
    val slackBotEventsQueueName: String,
    val slackEventsQueueName: String,
    val sourceCodeEventsQueueName: String,
    val sourceMarkQueueName: String,
    val summarizationEventsQueueName: String,
    val topicEventsQueueName: String,
    val topicPriorityEventsQueueName: String,
    val transcriptionEventsQueueName: String,
    val unblockedPRCommentQueueName: String,
    val webEventsQueueName: String,
)

@Serializable
data class EmailConfig(
    val sender: String,
    val senderName: String,
    val signupSender: String,
    val signupSenderName: String,
    val billingSender: String,
    val billingSenderName: String,
    val mailerJobTimeout: Duration,
    val itemProcessingTimeout: Duration,
    val emailLifecycleInterval: Duration,
)

enum class MLRouterInferenceModel { GPT4Omni, GPT4OmniMini, LLAMA3 }

@Serializable
data class MLRouterConfig(
    val webhookJobTimeout: Duration,
    val eventRouteLifecycleInterval: Duration,
    val inferenceModel: MLRouterInferenceModel = MLRouterInferenceModel.LLAMA3,
    val dynamoDB: DynamoDBConfig,
    val defaultTTL: Duration,
)

@Serializable
data class HoneycombConfig(
    val enabled: Boolean,
    @Contextual val apiKey: Secret,
    val sampleRate: Int,
    val hostName: String,
    val port: Int,
    val enableTls: Boolean,
)

@Serializable
data class Encryption(
    val userSecrets4096PublicKey: String,
)

@Serializable
data class VersioningConfig(
    val source: Source,
    val manifestBasePath: String,
    val s3: AWSBucketConfig,
    val buildsPathPrefix: String,
    val releasePathPrefix: String,
    val autoArchiveBuildsAfter: Duration,
    val autoReleaseBuilds: Boolean,
    val platforms: List<String>,
    val shouldManuallyUpdateIdePlugins: Boolean,
) {
    @Serializable
    enum class Source { S3, HTTP }
}

@Serializable
data class SendGridConfig(
    val isEnabled: Boolean,
    val apiKey: String,
    val onboardedListId: String,
    val teamInvitedFollowupListId: String,
    val welcomeEmailListId: String,
    val recommendedInviteeTemplateId: String,
    val teamInviteTemplateId: String,
    val threadInviteTemplateId: String,
    val threadInviteJoinTemplateId: String,
    val processingCompleteTemplateId: String,
    val processingCompleteInactiveFollowupListId: String,
    val onboardCompleteInactiveFollowupListId: String,
    val connectRepoFollowupTemplateId: String,
    val trialStartTemplateId: String,
    val nthTrialStartTemplateId: String,
    val pitchTemplateId: String,
    val trialTwoWeeksTemplateId: String,
    val trialOneWeekTemplateId: String,
    val trialThreeDaysTemplateId: String,
    val trialOneDayTemplateId: String,
    val businessPlanConfirmationTemplateId: String,
    val trialSurveyTemplateId: String,
    val inviteTemplateId: String,
    val paymentInviteTemplateId: String,
    val userAddedTemplateId: String,
    val userRemovedConfirmationTemplateId: String,
    val integrationAddedConfirmationTemplateId: String,
    val connectSourceCodeReminderTemplateId: String,
    val connectIntegrationsReminderTemplateId: String,
    val firstQuestionReminderTemplateId: String,
    val inviteTeamReminderTemplateId: String,
    val updateSlackPrivateScopesTemplateId: String,
    val requestAdminUpdateSlackPrivateScopesTemplateId: String,
    val isIntegrationAddedConfirmationEnabled: Boolean,
)

@Serializable
data class SegmentConfig(
    val apiKey: String,
)

@Serializable
data class NotificationConfig(
    val isSuggestedPeersInviteeEnabled: Boolean,
    val isInactiveFollowupEnabled: Boolean,
    val isOnboardingIncompleteFollowupEnabled: Boolean,
)

@Serializable
data class InternalSlackConfig(
    val fallbackChannel: String,
    val askUnblockedChannel: String?,
    val askUnblockedDownvoteChannel: String?,
    val askUnblockedInternalChannel: String?,
    val askUnblockedMcpChannel: String?,
    val askUnblockedMcpInsidersChannel: String?,
    val askUnblockedRegressionTestChannel: String?,
    val askUnblockedSlackAutoAnswerChannel: String?,
    val askUnblockedSlackAutoAnswerExperimentalChannel: String?,
    val askUnblockedSlackAutoAnswerFilterChannel: String?,
    val askUnblockedSlackAutoAnswerHelpfulChannel: String?,
    val askUnblockedSlackAutoAnswerInternalChannel: String?,
    val askUnblockedSlackAutoAnswerUnhelpfulChannel: String?,
    val askUnblockedSlackUserConnectedAccountChannel: String?,
    val askUnblockedSlackUserConnectedAccountInternalChannel: String?,
    val askUnblockedSlackUserSignInChannel: String?,
    val askUnblockedSlackUserSignInInternalChannel: String?,
    val askUnblockedUpvoteChannel: String?,
    val churnChannel: String?,
    val codeIngestionAlarmChannel: String?,
    val customerIntegrationsChannel: String?,
    val defaultIngestionChannel: String?,
    val demoSignupChannel: String?,
    val growthOrgChannel: String?,
    val growthUserChannel: String?,
    val invalidDemoSignupChannel: String?,
    val invitesChannel: String?,
    val machineLearningChannel: String?,
    val onboardingAlarmChannel: String?,
    val pickedPlanChannel: String?,
    val productFeedbackChannel: String?,
    val revenueChannel: String?,
    val serviceAlarmChannel: String?,
    val slackIngestionChannel: String?,
    val trialAlertsChannel: String?,
    val trialExtensionChannel: String?,
)

@Serializable
data class OAuthConfig(
    val authorizationUrl: String,
    val clientId: String,
    val includeResponseType: Boolean,
    val scopes: String?,
    val userScopes: String? = null,
    val includeOwner: Boolean = false,
    val tokenExchangeUrl: String? = null,
    val oauthCallbackUrl: String? = null,
)

@Serializable
data class FunctionConfig(
    val awsQueryFunctionTimeout: Duration,
    val expertsFunctionTimeout: Duration,
    val globalFunctionTimeout: Duration,
    val graphRagFunctionTimeout: Duration,
    val ciFunctionTimeout: Duration,
    val slackSummaryFunctionTimeout: Duration,
)

@Serializable
data class SearchConfig(
    val chatQueryReducerTimeout: Duration,
    val documentRetrievalTimeout: Duration,
    val enableAutocomplete: Boolean,
    val functions: FunctionConfig,
    val historicalMessageSizeLimit: Int,
    val inlineReferencesResolutionTimeout: Duration,
    val maliciousQueryDetectionTimeout: Duration,
    val maxConversationHistoryToPromptRatio: Double,
    val maxUserInputToPromptRatio: Double,
    val queryCompressionTimeout: Duration,
    val ragRegressionTestingEnabled: Boolean,
    val ragRegressionTestingInterval: Duration,
    val regressionTestingEnabled: Boolean,
    val regressionTestingInterval: Duration,
    val repoExtractionTimeout: Duration,
    val slackQAHistoricalMessageLimit: Int,
    val unblockedQAHistoricalMessageLimit: Int,
    val useCheatCodes: Boolean,
    val useExpertsUsingTopics: Boolean,
)

@Serializable
data class SearchTriageConfig(
    val documentRetrievalTimeout: Duration,
)

@Serializable
data class SlackAutoResponseConfig(
    val documentRelevancyThreshold: Float,
)

@Serializable
data class DynamoDBConfig(
    val region: String,
    val tableName: String,
)

@Serializable
data class EmbeddingConfig(
    val enableInstallationMigration: Boolean,
    val enableOrgMigration: Boolean,
    val maxPartitionSizeInBytes: Int,
)

@Serializable
data class WebIngestionTimeout(
    val requestTimeoutDurationSeconds: Int,
    val socketTimeoutDurationSeconds: Int,
)

@Serializable
data class WebIngestionConfig(
    val enabled: Boolean,
    val proxyEnabled: Boolean,
    val proxyHostname: String,
    val proxyPort: Int,
    val timeout: WebIngestionTimeout? = null,
)

@Serializable
data class CoroutineConfig(
    val dbParallelism: Int,
    var asyncStepParallelism: Int,
    val documentRetrievalParallelism: Int,
)

@Serializable
data class FeatureFlagsConfig(
    val enableAWSLiveQuery: Boolean,
    val enableBuildIngestion: Boolean,
    val enableCiJobSearchByHtmlUrl: Boolean,
    val enableDocumentMarkdownConversion: Boolean,
    val enableGraphRag: Boolean,
    val enableOpenSearch: Boolean,
    val enablePrefect: Boolean,
    val enablePrefectSampling: Boolean,
    val enablePullRequestSummaries: Boolean,
    val enableSlackInstallProgress: Boolean,
    val enableTeamDisablement: Boolean,
    val enableTestBillingActions: Boolean,
    val enableTopicMapping: Boolean,
)

@Serializable
data class MermaidConfig(
    val errorImageUrl: String,
    @Contextual val hmacSecret: Secret,
)

@Serializable
data class RapidConfig(
    val dynamoDB: DynamoDBConfig,
)

@Serializable
data class DiscoveryConfig(
    val originatingServiceAddresses: List<String>,
)

@Serializable
data class OpenSearchConfig(
    val baseApiUri: String,
    val defaultTimeout: Duration,
    val userName: String,
    @Contextual val password: Secret,
)

@Serializable
data class PrefectConfig(
    val baseApiUri: String,
    val defaultTimeout: Duration,
    @Contextual val authString: Secret,
)

@Serializable
data class ClientEncryptionConfig(
    val publicKey: String,
)

@Serializable
data class AtlassianForgeConfig(
    val jwksUrl: String,
)

@Serializable
data class BillingConfig(
    val enabled: Boolean,
)

@Serializable
data class TestConfig(
    val maxTestRetries: Int,
)

@Serializable
data class GlobalConfig(
    val activeMQ: ActiveMQConfig,
    val adminWeb: DeploymentConfig,
    val anthropic: AnthropicConfig,
    val atlassianForgeConfig: AtlassianForgeConfig,
    val authentication: AuthenticationConfig,
    val azureOpenAI: AzureOpenAIConfig,
    val billing: BillingConfig,
    val blog: BlogPageConfig,
    val clientEncryption: ClientEncryptionConfig,
    val cohere: CohereConfig,
    val coroutine: CoroutineConfig,
    val cors: CORSConfig,
    val dashboard: DeploymentConfig,
    val database: DatabaseConfig,
    val diagnostics: DiagnosticsConfig,
    val discovery: DiscoveryConfig,
    val docs: DocsPageConfig,
    val email: EmailConfig,
    val embedding: EmbeddingConfig,
    val encryption: Encryption,
    val featureFlags: FeatureFlagsConfig,
    val honeycomb: HoneycombConfig,
    val intercom: IntercomConfig,
    val internalSlack: InternalSlackConfig,
    val landingPage: DeploymentConfig,
    val machineLearning: MachineLearningConfig,
    val mermaid: MermaidConfig,
    val mlRouter: MLRouterConfig,
    val notification: NotificationConfig,
    val openAI: OpenAIConfig,
    val openRouter: OpenRouterConfig,
    val openSearch: OpenSearchConfig,
    val pinecone: PineconeConfig,
    val prefect: PrefectConfig,
    val providers: ProvidersConfig,
    val queue: QueueConfig,
    val rapid: RapidConfig,
    val redis: RedisConfig,
    val rpc: RpcGlobalConfig,
    val search: SearchConfig,
    val searchTriage: SearchTriageConfig,
    val segment: SegmentConfig,
    val sendGrid: SendGridConfig,
    val sensitiveLogging: SensitiveLoggingConfig,
    val service: DeploymentConfig,
    val slackAutoResponse: SlackAutoResponseConfig,
    val survey: SurveyPageConfig,
    val test: TestConfig,
    val versioning: VersioningConfig,
    val webIngestionConfig: WebIngestionConfig,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<GlobalConfig>()

        fun getTestInstance(
            overrideEnvironment: String? = null,
            overrideUser: String? = null,
        ) = ConfigLoader.loadConfig<GlobalConfig>(
            overrideEnvironment = overrideEnvironment,
            overrideUser = overrideUser,
        )
    }
}
