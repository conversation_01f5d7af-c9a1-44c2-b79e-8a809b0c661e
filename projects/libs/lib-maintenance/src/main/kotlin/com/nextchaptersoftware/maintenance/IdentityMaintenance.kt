package com.nextchaptersoftware.maintenance

import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.user.secret.UserSecretService
import org.jetbrains.exposed.sql.Transaction

class IdentityMaintenance(
    private val identityStore: IdentityStore = Stores.identityStore,
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val userSecretService: UserSecretService,
) {

    /**
     * Creates or updates [Identity]s from [ScmUser]s and returns all the identities.
     */
    suspend fun upsertIdentities(
        trx: Transaction? = null,
        scmTeam: ScmTeam,
        scmUsers: List<ScmUser>,
    ): List<Identity> {
        val scm = Scm.fromTeam(scmTeam)

        return scmUsers.distinctBy { it.externalId }.map { scmUser ->
            identityStore.upsert(
                trx = trx,
                externalId = scmUser.externalId,
                externalTeamId = scm.uniqueSignature,
                username = scmUser.login,
                displayName = scmUser.displayName,
                avatarUrl = scmUser.avatarUrl,
                htmlUrl = scmUser.htmlUrl,
                provider = scm.provider,
                isBot = scmUser.isBot,
            )
        }
    }

    suspend fun revoke(user: ScmUser, scm: Scm) {
        identityStore.findIdByExternalId(
            provider = scm.provider,
            externalId = user.externalId,
            externalTeamId = scm.uniqueSignature,
        )?.also {
            identityStore.revoke(identityId = it)
        }
    }

    suspend fun remotelyRevoke(identityId: IdentityId) {
        val identity = identityStore.findIdentityWithAccount(identityId) ?: return
        identity.provider.isScmProvider || return

        runSuspendCatching {
            val authApi = scmAuthApiFactory.getApi(orgId = null, oAuthApiType = Scm.fromIdentity(identity))

            val oauthRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = authApi,
                tokenPersistence = EncryptedTokenPersistence(userSecretService),
            )
            oauthRefreshService.getRefreshedOAuthTokensIfNeeded(identityId.value)?.also { tokens ->
                authApi.revokeToken(tokens.accessToken)
            }
        }

        identityStore.revoke(identityId)
    }
}
