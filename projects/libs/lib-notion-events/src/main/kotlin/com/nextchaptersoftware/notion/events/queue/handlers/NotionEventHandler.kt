package com.nextchaptersoftware.notion.events.queue.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.integration.queue.handlers.IntegrationReIngestEventHandler
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.notion.events.queue.payloads.NotionEvent
import com.nextchaptersoftware.notion.events.queue.payloads.NotionIngestionEvent
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class NotionEventHandler(
    private val integrationReIngestEventHandler: IntegrationReIngestEventHandler,
    private val notionWebhookEventHandler: TypedEventHandler<NotionEvent.NotionWebhookEvent>,
    private val notionPageIngestionEventHandler: TypedEventHandler<NotionIngestionEvent.NotionPageIngestionEvent>,
) : EventHandler {
    override suspend fun handle(event: String): Boolean {
        val notionEvent: NotionEvent = runSuspendCatching {
            event.decode<NotionEvent>()
        }.getOrElse {
            LOGGER.errorAsync(it) { "Failed to deserialize NotionEvent" }
            return@handle false
        }

        LOGGER.debugAsync { "Handling notion event" }

        return when (notionEvent) {
            is NotionEvent.NotionReIngestEvent -> integrationReIngestEventHandler.handle(notionEvent)
            is NotionEvent.NotionWebhookEvent -> notionWebhookEventHandler.handle(notionEvent)
            is NotionIngestionEvent.NotionPageIngestionEvent -> notionPageIngestionEventHandler.handle(notionEvent)
        }
    }
}
