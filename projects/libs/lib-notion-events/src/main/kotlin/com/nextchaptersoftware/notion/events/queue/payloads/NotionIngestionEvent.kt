package com.nextchaptersoftware.notion.events.queue.payloads

import com.nextchaptersoftware.activemq.models.MessagePriority
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.integration.queue.payloads.IntegrationIngestionEvent
import com.nextchaptersoftware.notion.models.Page
import java.util.UUID
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
sealed class NotionIngestionEvent : NotionEvent(), IntegrationIngestionEvent {
    @SerialName("NotionPageIngestionEvent")
    @Serializable
    data class NotionPageIngestionEvent(
        @SerialName("teamId") // for backwards compatibility
        override val orgId: OrgId,
        override val installationId: InstallationId,
        override val isInitialIngestion: Boolean,
        override val priority: MessagePriority = MessagePriority.DEFAULT,
        val identityId: IdentityId, // the identity to use for fetching content
        val page: Page,
    ) : NotionIngestionEvent() {
        override val documentId: UUID
            get() = page.id
    }
}
