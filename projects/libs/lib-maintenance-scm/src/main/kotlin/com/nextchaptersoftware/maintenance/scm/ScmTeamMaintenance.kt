package com.nextchaptersoftware.maintenance.scm

import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.RedisLock
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebhookService
import com.nextchaptersoftware.scm.models.ScmRole
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration
import kotlin.time.Duration.Companion.milliseconds
import kotlin.time.Duration.Companion.minutes
import kotlinx.coroutines.delay
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ScmTeamMaintenance(
    private val scmMembershipMaintenance: ScmMembershipMaintenance,
    private val lockProvider: LockProvider,
    private val repoMaintenance: RepoMaintenance,
    private val scmTeamApiFactory: ScmTeamApiFactory,
    private val scmUserApiFactory: ScmUserApiFactory,
    private val scmWebhookService: ScmWebhookService,
    private val observableDelay: suspend (Duration) -> Unit = { delay(it) },
) {
    companion object {
        val LEEWAY = 1.minutes
    }

    suspend fun refreshTeamResources(
        scmTeam: ScmTeam,
        scm: Scm,
    ) {
        val now = Instant.nowWithMicrosecondPrecision() - LEEWAY

        if (Stores.scmTeamStore.hasProviderRefreshed(scmTeam.id, since = now)) {
            // when the team was recently refreshed, no work needs to be done
            return
        }

        // the spirit of this section is to safely allow multiple callers to trigger
        // concurrent team refreshes while minimizing the number of external requests

        val lock = lockProvider.create(id = scmTeam.id.value)
        try {
            while (!lock.acquire()) {
                observableDelay.invoke(250.milliseconds)
            }
            if (!Stores.scmTeamStore.hasProviderRefreshed(scmTeam.id, since = now)) {
                // we can now safely refresh the team
                refreshTeamResourcesWithLock(scmTeam, scm, lock)
            }
        } finally {
            lock.release()
        }
    }

    private suspend fun refreshTeamResourcesWithLock(
        scmTeam: ScmTeam,
        scm: Scm,
        lock: RedisLock,
    ) {
        scmTeamApiFactory.getApiFromTeam(scmTeam, scm).use { api ->
            withLoggingContextAsync("teamId" to scmTeam.id) {
                LOGGER.debugAsync { "refreshTeamResourcesWithApi start" }
                refreshTeamResourcesWithApi(scmTeam, api, scm, lock)
                LOGGER.debugAsync { "refreshTeamResourcesWithApi done" }
            }
        }
    }

    private suspend fun refreshTeamResourcesWithApi(scmTeam: ScmTeam, api: ScmTeamApi, scm: Scm, lock: RedisLock) {
        var refreshResourcesFailed = false

        suspend fun lockRenew() {
            if (!lock.renew()) {
                throw ScmTeamRefreshError("Failed to refresh team resources: unable to renew lock")
            }
        }

        runSuspendCatching {
            repoMaintenance.refreshRepositories(scmTeam = scmTeam, allScmRepos = api.repos())
            Stores.scmTeamStore.updateProviderLastRefreshedAt(scmTeam.id)
        }.onFailure {
            LOGGER.errorAsync(it) { "refreshTeamResourcesWithApi: Failed to refresh repos" }
            refreshResourcesFailed = true
        }

        lockRenew()

        runSuspendCatching {
            scmMembershipMaintenance.refreshMembers(
                scmTeam = scmTeam,
                scm = scm,
                allScmMembers = api.membersForOrg(),
                getScmMemberRole = when (scm) {
                    is Scm.BitbucketDataCenter -> { identity -> getMemberOrgAccess(scm, scmTeam, identity) }
                    else -> null
                },
            )
        }.onFailure {
            LOGGER.errorAsync(it) { "refreshTeamResourcesWithApi: Failed to refresh team members" }
            refreshResourcesFailed = true
        }

        lockRenew()

        runSuspendCatching {
            scmWebhookService.ensureWebhookExists(scm, api, scmTeam)
        }.onFailure {
            LOGGER.errorAsync(it) { "refreshTeamResourcesWithApi: Failed to install org webhook" }
            // FIXME richie find another way to install the webhook on repos instead
            //  refreshResourcesFailed = true
        }

        if (refreshResourcesFailed) {
            throw ScmTeamRefreshError("Failed to refresh team resources")
        }
    }

    private suspend fun getMemberOrgAccess(scm: Scm, scmTeam: ScmTeam, identity: Identity): ScmRole? {
        scmUserApiFactory.getApiFromIdentity(identityId = identity.id, scm = scm, orgId = scmTeam.orgId).use { api ->
            return api.orgRole(scmTeam.displayName)
        }
    }
}

data class ScmTeamRefreshError(
    override val message: String,
) : Exception(message)
