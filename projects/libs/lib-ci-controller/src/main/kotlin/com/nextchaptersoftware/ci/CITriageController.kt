package com.nextchaptersoftware.ci

import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.ci.services.CIRepoControlService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.stores.OrgBillingStore
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.orgSettingsStore
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType

private val LOGGER = mu.KotlinLogging.logger {}

class CITriageController(
    private val capabilityValidation: CapabilityValidation,
    private val ciRepoControlService: CIRepoControlService = CIRepoControlService(),
    private val clientConfigService: ClientConfigService,
    private val orgBillingStore: OrgBillingStore = Stores.orgBillingStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val orgStore: OrgStore = Stores.orgStore,
) : CITriageControllerInterface {

    @Suppress("ReturnCount")
    override suspend fun ciEnabled(
        orgId: OrgId,
        repo: Repo,
        orgMemberId: OrgMemberId,
        ciInstallationId: InstallationId,
        scmInstallationId: InstallationId,
    ): Boolean {
        if (repo.isPublic == true) {
            LOGGER.traceAsync { "Repo is public" }
            return false
        }

        // Feature flag check
        if (!clientConfigService.computeMergedCapabilityForType(personId = null, orgId = orgId, type = ClientCapabilityType.FeatureConfigureCI)) {
            LOGGER.traceAsync { "Org is missing FeatureConfigureCI" }
            return false
        }

        // Plan check: Trial expired
        if (orgBillingStore.isTrialExpired(orgId = orgId)) {
            LOGGER.traceAsync { "Org trial expired" }
            return false
        }

        // Plan check: CI enabled
        if (!capabilityValidation.allowsCapability(orgId, PlanCapabilityType.CI)) {
            LOGGER.traceAsync { "Org is missing PlanCapability CI" }
            return false
        }

        // Org exists
        val org = orgStore.findById(orgId = orgId)
            ?: run {
                LOGGER.traceAsync { "Org does not exist" }
                return false
            }

        // Org enabled
        if (org.isPending) {
            LOGGER.traceAsync { "Org is pending" }
            return false
        }

        // Repo enabled
        if (!ciRepoControlService.isRepoEnabled(repo = repo, ciInstallationId = ciInstallationId, scmInstallationId = scmInstallationId)) {
            LOGGER.traceAsync { "Repo is CI disabled" }
            return false
        }

        // Org member exists
        val orgMember = orgMemberStore.findById(id = orgMemberId)
            ?: run {
                LOGGER.traceAsync { "Org member does not exist" }
                return false
            }

        // Org member enabled
        if (!orgMember.enableCiTriage) {
            LOGGER.traceAsync { "Org member is CI disabled" }
            return false
        }

        return true
    }

    override suspend fun isTriageEnabled(
        orgId: OrgId,
    ): Boolean {
        return orgSettingsStore.getEnableTriagePipeline(orgId = orgId)
    }
}
