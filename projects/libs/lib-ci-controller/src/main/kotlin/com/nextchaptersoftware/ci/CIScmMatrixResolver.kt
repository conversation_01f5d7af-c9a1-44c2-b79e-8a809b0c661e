package com.nextchaptersoftware.ci

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.db.models.Provider

class CIScmMatrixResolver(
    private val ciConfig: CIConfig,
) {
    /**
     * SCM providers that support CI.
     */
    fun getSupportedScmProviders(ciProvider: Provider): Set<Provider> {
        check(ciProvider.isCiProvider) { "Expected CI provider, but was ${ciProvider.name}" }

        return when (ciProvider) {
            Provider.BitbucketPipelines -> setOf(
                Provider.Bitbucket,
            )

            Provider.Buildkite -> setOfNotNull(
                Provider.Bitbucket,
                Provider.GitHub,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )

            Provider.CircleCI -> setOfNotNull(
                Provider.Bitbucket,
                Provider.GitHub,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )

            Provider.GitHubActions -> setOf(
                Provider.GitHub,
                Provider.GitHubEnterprise,
            )

            Provider.GitLabPipelines -> setOf(
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )

            else -> error("Unsupported CI provider: $ciProvider")
        }
            .filterNot { it == Provider.Bitbucket && !ciConfig.features.enableScmBitbucket }
            .filterNot { it == Provider.GitLab && !ciConfig.features.enableScmGitLab }
            .filterNot { it == Provider.GitLabSelfHosted && !ciConfig.features.enableScmGitLabSelfHosted }
            .toSet()
    }
}
