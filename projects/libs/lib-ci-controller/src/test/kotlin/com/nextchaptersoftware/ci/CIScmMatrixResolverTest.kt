package com.nextchaptersoftware.ci

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.db.models.Provider
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows

class CIScmMatrixResolverTest {

    @Nested
    inner class DEV {
        private val resolver = CIScmMatrixResolver(
            ciConfig = CIConfig.getTestInstance(overrideEnvironment = "dev"),
        )

        @Test
        fun `getSupportedScmProviders throws when not a ci provider`() {
            Provider.entries.filterNot { it.isCiProvider }.forEach {
                assertThrows<IllegalStateException> {
                    resolver.getSupportedScmProviders(it)
                }
            }
        }

        @Test
        fun `getSupportedScmProviders does not throws when a ci provider`() {
            Provider.entries.filter { it.isCiProvider }.forEach {
                assertDoesNotThrow {
                    resolver.getSupportedScmProviders(it)
                }
            }
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for BitbucketPipelines`() {
            assertThat(resolver.getSupportedScmProviders(Provider.BitbucketPipelines)).containsExactlyInAnyOrder(
                Provider.Bitbucket,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for Buildkite`() {
            assertThat(resolver.getSupportedScmProviders(Provider.Buildkite)).containsExactlyInAnyOrder(
                Provider.Bitbucket,
                Provider.GitHub,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for CircleCI`() {
            assertThat(resolver.getSupportedScmProviders(Provider.CircleCI)).containsExactlyInAnyOrder(
                Provider.Bitbucket,
                Provider.GitHub,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for GitHubActions`() {
            assertThat(resolver.getSupportedScmProviders(Provider.GitHubActions)).containsExactlyInAnyOrder(
                Provider.GitHub,
                Provider.GitHubEnterprise,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for GitLabPipelines`() {
            assertThat(resolver.getSupportedScmProviders(Provider.GitLabPipelines)).containsExactlyInAnyOrder(
                Provider.GitLab,
                Provider.GitLabSelfHosted,
            )
        }
    }

    @Nested
    inner class PROD {
        private val resolver = CIScmMatrixResolver(
            ciConfig = CIConfig.getTestInstance(overrideEnvironment = "prod"),
        )

        @Test
        fun `getSupportedScmProviders throws when not a ci provider`() {
            Provider.entries.filterNot { it.isCiProvider }.forEach {
                assertThrows<IllegalStateException> {
                    resolver.getSupportedScmProviders(it)
                }
            }
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for BitbucketPipelines`() {
            assertThat(resolver.getSupportedScmProviders(Provider.BitbucketPipelines)).containsExactlyInAnyOrder()
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for Buildkite`() {
            assertThat(resolver.getSupportedScmProviders(Provider.Buildkite)).containsExactlyInAnyOrder(
                Provider.GitHub,
                Provider.GitHubEnterprise,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for CircleCI`() {
            assertThat(resolver.getSupportedScmProviders(Provider.CircleCI)).containsExactlyInAnyOrder(
                Provider.GitHub,
                Provider.GitHubEnterprise,
            )
        }

        @Test
        fun `getSupportedScmProviders returns SCM providers for GitHubActions`() {
            assertThat(resolver.getSupportedScmProviders(Provider.GitHubActions)).containsExactlyInAnyOrder(
                Provider.GitHub,
                Provider.GitHubEnterprise,
            )
        }
    }
}
