package com.nextchaptersoftware.cache

import com.nextchaptersoftware.log.kotlin.debugSync

private val LOGGER = mu.KotlinLogging.logger {}

class ReadThroughCacheLogger<K, V>(
    val name: String?,
    val delegate: ReadThroughCache<K, V>,
) : ReadThroughCache<K, V> {

    override suspend fun get(key: K): V? {
        return delegate.get(key).also {
            LOGGER.debugSync(
                "cache" to name,
                "cacheClass" to delegate::class.simpleName,
                "cacheOperation" to "get",
                "cacheKey" to key,
                "cacheResult" to if (it == null) "MISS" else "HIT",
            ) { "get" }
        }
    }

    override suspend fun put(key: K, value: V) {
        return delegate.put(key, value).also {
            LOGGER.debugSync(
                "cache" to name,
                "cacheClass" to delegate::class.simpleName,
                "cacheOperation" to "put",
                "cacheKey" to key,
            ) { "put" }
        }
    }

    override suspend fun getOrCompute(key: K, compute: suspend (K) -> V): V {
        var didCompute = false
        return delegate.getOrCompute(key) {
            didCompute = true
            compute(key)
        }.also {
            LOGGER.debugSync(
                "cache" to name,
                "cacheClass" to delegate::class.simpleName,
                "cacheOperation" to "compute",
                "cacheKey" to key,
                "cacheResult" to if (didCompute) "MISS" else "HIT",
            ) { "compute" }
        }
    }

    override fun clear() {
        delegate.clear()
        LOGGER.debugSync(
            "cache" to name,
            "cacheClass" to delegate::class.simpleName,
            "cacheOperation" to "clear",
        ) { "clear" }
    }
}

fun <K, V> ReadThroughCache<K, V>.logged(
    name: String? = null,
) = ReadThroughCacheLogger(
    name = name ?: this.javaClass.simpleName,
    delegate = this,
)
