package com.nextchaptersoftware.activation

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.config.ScmConfig

/**
 * Determines if providers are operational.
 */
class ProviderActivation(
    private val ciConfig: CIConfig = CIConfig.INSTANCE,
    private val config: GlobalConfig = GlobalConfig.INSTANCE,
    private val scmConfig: ScmConfig = ScmConfig.INSTANCE,
) {
    @Suppress("CyclomaticComplexMethod")
    fun isActive(provider: Provider): Boolean {
        return when (provider) {
            // Public providers with public [OAuth] Apps are only active if configured for the environment.
            Provider.Asana -> config.providers.asana != null

            Provider.AzureDevOps -> scmConfig.azureDevOps != null

            Provider.Bitbucket -> scmConfig.bitbucketCloud != null

            Provider.Confluence -> config.providers.confluence != null

            Provider.BitbucketPipelines -> ciConfig.features.enableBitbucketPipelines

            Provider.GitHub -> scmConfig.githubCloud != null

            Provider.GitHubActions -> scmConfig.githubCloud != null

            Provider.GitLab -> scmConfig.gitlabCloud != null

            Provider.GitLabPipelines -> ciConfig.features.enableGitLabPipelines

            Provider.GoogleDrive -> config.providers.googleDrive != null

            Provider.Jira -> config.providers.jira != null

            Provider.Linear -> config.providers.linear != null

            Provider.Notion -> config.providers.notion != null

            // FIXME richie true for now
            Provider.Slack -> true

            Provider.AwsIdentityCenter,
            Provider.BitbucketDataCenter,
            Provider.Buildkite,
            Provider.CircleCI,
            Provider.Coda,
            Provider.ConfluenceDataCenter,
            Provider.CustomIntegration,
            Provider.GenericSaml,
            Provider.GitHubEnterprise,
            Provider.GitLabSelfHosted,
            Provider.GoogleDriveWorkspace,
            Provider.GoogleWorkspace,
            Provider.JiraDataCenter,
            Provider.MicrosoftEntra,
            Provider.Okta,
            Provider.PingOne,
            Provider.StackOverflowTeams,
            Provider.Unblocked,
            Provider.Web,
                -> true

            Provider.Aws,
                -> false
        }
    }
}
