package com.nextchaptersoftware.activation

import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.config.ScmConfig
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class ProviderActivationTest {

    private fun setupProviderActivation(
        environment: String,
        overrideGlobalConfig: GlobalConfig? = null,
        overrideScmConfig: ScmConfig? = null,
    ) = ProviderActivation(
        ciConfig = CIConfig.getTestInstance(
            overrideEnvironment = environment,
            overrideUser = "nobody",
        ),
        config = overrideGlobalConfig ?: GlobalConfig.getTestInstance(
            overrideEnvironment = environment,
            overrideUser = "nobody",
        ),
        scmConfig = overrideScmConfig ?: ScmConfig.getTestInstance(
            overrideEnvironment = environment,
            overrideUser = "nobody",
        ),
    )

    private fun ProviderActivation.expectActive(provider: Provider) {
        assertThat(isActive(provider))
            .withFailMessage("Expected '$provider' to be active")
            .isTrue
    }

    private fun ProviderActivation.expectInactive(provider: Provider) {
        assertThat(isActive(provider))
            .withFailMessage("Expected '$provider' to be inactive")
            .isFalse
    }

    @Test
    fun local() {
        val providerActivation = setupProviderActivation(
            environment = "local",
        )

        Provider.entries.forEach {
            when (it) {
                Provider.Asana,
                Provider.AwsIdentityCenter,
                Provider.AzureDevOps,
                Provider.Bitbucket,
                Provider.BitbucketDataCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHub,
                Provider.GitHubActions,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabPipelines,
                Provider.GitLabSelfHosted,
                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.Jira,
                Provider.JiraDataCenter,
                Provider.Linear,
                Provider.MicrosoftEntra,
                Provider.Notion,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> providerActivation.expectActive(it)

                Provider.Aws,
                    -> providerActivation.expectInactive(it)
            }
        }
    }

    @Test
    fun dev() {
        val providerActivation = setupProviderActivation(
            environment = "dev",
        )

        Provider.entries.forEach {
            when (it) {
                Provider.Asana,
                Provider.AwsIdentityCenter,
                Provider.AzureDevOps,
                Provider.Bitbucket,
                Provider.BitbucketDataCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHub,
                Provider.GitHubActions,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabPipelines,
                Provider.GitLabSelfHosted,
                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.Jira,
                Provider.JiraDataCenter,
                Provider.Linear,
                Provider.MicrosoftEntra,
                Provider.Notion,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> providerActivation.expectActive(it)

                Provider.Aws,
                    -> providerActivation.expectInactive(it)
            }
        }
    }

    @Test
    fun prod() {
        val providerActivation = setupProviderActivation(
            environment = "prod",
        )

        Provider.entries.forEach {
            when (it) {
                Provider.Asana,
                Provider.AwsIdentityCenter,
                Provider.AzureDevOps,
                Provider.Bitbucket,
                Provider.BitbucketDataCenter,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHub,
                Provider.GitHubActions,
                Provider.GitHubEnterprise,
                Provider.GitLab,
                Provider.GitLabSelfHosted,
                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.Jira,
                Provider.JiraDataCenter,
                Provider.Linear,
                Provider.MicrosoftEntra,
                Provider.Notion,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> providerActivation.expectActive(it)

                Provider.Aws,
                Provider.BitbucketPipelines,
                Provider.GitLabPipelines,
                    -> providerActivation.expectInactive(it)
            }
        }
    }

    @Test
    fun `prod no public providers`() {
        val providerActivation = setupProviderActivation(
            environment = "prod",
            overrideScmConfig = ScmConfig.getTestInstance(
                overrideEnvironment = "prod",
                overrideUser = "nobody",
            ).copy(
                azureDevOps = null,
                bitbucketCloud = null,
                githubCloud = null,
                gitlabCloud = null,
            ),
            overrideGlobalConfig = GlobalConfig.getTestInstance(
                overrideEnvironment = "prod",
                overrideUser = "nobody",
            ).copy(
                providers = GlobalConfig.INSTANCE.providers.copy(
                    asana = null,
                    confluence = null,
                    googleDrive = null,
                    jira = null,
                    linear = null,
                    notion = null,
                ),
            ),
        )

        Provider.entries.forEach {
            when (it) {
                Provider.AwsIdentityCenter,
                Provider.BitbucketDataCenter,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHubEnterprise,
                Provider.GitLabSelfHosted,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.JiraDataCenter,
                Provider.MicrosoftEntra,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> providerActivation.expectActive(it)

                Provider.Asana,
                Provider.Aws,
                Provider.AzureDevOps,
                Provider.Bitbucket,
                Provider.BitbucketPipelines,
                Provider.Confluence,
                Provider.GitHub,
                Provider.GitHubActions,
                Provider.GitLab,
                Provider.GitLabPipelines,
                Provider.GoogleDrive,
                Provider.Jira,
                Provider.Linear,
                Provider.Notion,
                    -> providerActivation.expectInactive(it)
            }
        }
    }

    @Disabled($$"Could not resolve substitution to a value: ${SERVICE_BASE_URL}")
    @Test
    fun onprem() {
        val providerActivation = setupProviderActivation(
            environment = "onprem",
        )

        Provider.entries.forEach {
            when (it) {
                Provider.AwsIdentityCenter,
                Provider.BitbucketDataCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHubActions,
                Provider.GitHubEnterprise,
                Provider.GitLabSelfHosted,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.JiraDataCenter,
                Provider.MicrosoftEntra,
                Provider.Okta,
                Provider.PingOne,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> providerActivation.expectActive(it)

                Provider.Asana,
                Provider.Aws,
                Provider.AzureDevOps,
                Provider.Bitbucket,
                Provider.Confluence,
                Provider.GitHub,
                Provider.GitLab,
                Provider.GitLabPipelines,
                Provider.GoogleDrive,
                Provider.Jira,
                Provider.Linear,
                Provider.Notion,
                Provider.Slack,
                    -> providerActivation.expectInactive(it)
            }
        }
    }
}
