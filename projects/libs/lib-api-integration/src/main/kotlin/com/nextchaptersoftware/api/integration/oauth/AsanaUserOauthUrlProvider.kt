package com.nextchaptersoftware.api.integration.oauth

import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.asana.auth.oauth.AsanaAuthState
import com.nextchaptersoftware.config.AsanaConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import io.ktor.http.Url
import io.ktor.http.parametersOf
import java.util.UUID

class AsanaUserOauthUrlProvider(
    private val providerRedirectUrlService: ProviderRedirectUrlService,
    private val config: AsanaConfig,
) {
    suspend fun get(
        orgId: OrgId,
        personId: PersonId,
        provider: Provider,
        redirectUrl: Url?,
        clientState: String?,
        targetIdentityId: IdentityId? = null,
    ): Url {
        val authState = AsanaAuthState(
            orgId = orgId,
            personId = personId,
            redirectUrl = redirectUrl,
        )

        return providerRedirectUrlService.buildRedirectUrl(
            oAuthApiType = provider,
            nonce = UUID.randomUUID(),
            authRedirectOverrideUrl = checkNotNull(config.oauth.oauthCallbackUrl).asUrl,
            clientState = clientState,
            targetIdentityId = targetIdentityId,
            state = authState.encodeAuthState(),
            extraParameters = parametersOf("response_type", "code"),
        )
    }
}
