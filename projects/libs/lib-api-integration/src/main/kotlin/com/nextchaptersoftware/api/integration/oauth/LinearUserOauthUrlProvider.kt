package com.nextchaptersoftware.api.integration.oauth

import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.config.LinearConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.linear.auth.oauth.LinearAuthState
import io.ktor.http.Url
import java.util.UUID

class LinearUserOauthUrlProvider(
    private val providerRedirectUrlService: ProviderRedirectUrlService,
    private val config: LinearConfig,
) {
    suspend fun get(
        orgId: OrgId,
        personId: PersonId,
        redirectUrl: Url?,
        clientState: String?,
        targetIdentityId: IdentityId? = null,
    ): Url {
        val authState = LinearAuthState(
            orgId = orgId,
            personId = personId,
            redirectUrl = redirectUrl,
        )

        return providerRedirectUrlService.buildRedirectUrl(
            oAuthApiType = Provider.Linear,
            nonce = UUID.randomUUID(),
            authRedirectOverrideUrl = checkNotNull(config.oauth.oauthCallbackUrl).asUrl,
            clientState = clientState,
            targetIdentityId = targetIdentityId,
            state = authState.encodeAuthState(),
            extraParameters = null,
        )
    }
}
