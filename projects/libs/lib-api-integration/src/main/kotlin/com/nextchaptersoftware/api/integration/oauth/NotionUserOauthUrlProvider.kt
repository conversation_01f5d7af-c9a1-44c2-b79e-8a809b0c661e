package com.nextchaptersoftware.api.integration.oauth

import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.config.NotionConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.notion.auth.oauth.NotionAuthState
import io.ktor.http.Url
import java.util.UUID

class NotionUserOauthUrlProvider(
    private val providerRedirectUrlService: ProviderRedirectUrlService,
    private val config: NotionConfig,
) {
    suspend fun get(
        orgId: OrgId,
        personId: PersonId,
        redirectUrl: Url?,
        clientState: String?,
        targetIdentityId: IdentityId? = null,
    ): Url {
        val notionAuthState = NotionAuthState(
            orgId = orgId,
            personId = personId,
            redirectUrl = redirectUrl,
        )

        return providerRedirectUrlService.buildRedirectUrl(
            oAuthApiType = Provider.Notion,
            nonce = UUID.randomUUID(),
            authRedirectOverrideUrl = checkNotNull(config.oauth.oauthCallbackUrl).asUrl,
            clientState = clientState,
            targetIdentityId = targetIdentityId,
            state = notionAuthState.encodeAuthState(),
            extraParameters = null,
        )
    }
}
