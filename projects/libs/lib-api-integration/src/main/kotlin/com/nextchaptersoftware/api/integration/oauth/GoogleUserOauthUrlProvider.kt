package com.nextchaptersoftware.api.integration.oauth

import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.config.GoogleDriveConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.google.auth.oauth.GoogleAuthState
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import io.ktor.http.Parameters
import io.ktor.http.Url
import java.util.UUID

class GoogleUserOauthUrlProvider(
    private val providerRedirectUrlService: ProviderRedirectUrlService,
    private val config: GoogleDriveConfig,
) {
    suspend fun get(
        orgId: OrgId,
        personId: PersonId,
        provider: Provider,
        redirectUrl: Url?,
        clientState: String?,
        targetIdentityId: IdentityId? = null,
    ): Url {
        val authState = GoogleAuthState(
            orgId = orgId,
            personId = personId,
            redirectUrl = redirectUrl,
        )

        return providerRedirectUrlService.buildRedirectUrl(
            oAuthApiType = provider,
            nonce = UUID.randomUUID(),
            authRedirectOverrideUrl = checkNotNull(config.oauth.oauthCallbackUrl).asUrl,
            clientState = clientState,
            targetIdentityId = targetIdentityId,
            state = authState.encodeAuthState(),
            extraParameters = Parameters.build {
                append("access_type", "offline")
                append("include_granted_scopes", "true")
                append("prompt", "consent")
            },
        )
    }
}
