package com.nextchaptersoftware.api.integration.services

import com.nextchaptersoftware.activation.ProviderActivation
import com.nextchaptersoftware.api.integration.factory.ProviderIntegrationFactory
import com.nextchaptersoftware.api.integration.models.IntegrationModel
import com.nextchaptersoftware.ci.CIScmMatrixResolver
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import io.ktor.http.Url

class IntegrationService(
    private val ciScmMatrixResolver: CIScmMatrixResolver = CIScmMatrixResolver(CIConfig.INSTANCE),
    private val installationStore: InstallationStore = Stores.installationStore,
    private val integrationFactory: ProviderIntegrationFactory,
    private val providerActivation: ProviderActivation,
) {
    suspend fun listIntegrations(
        orgId: OrgId,
        identityId: IdentityId,
        personId: PersonId,
        overrideAuthRedirectUrl: Url?,
        dataIntegrationRedirectUrl: Url?,
        clientState: String?,
    ): List<IntegrationModel> {
        val integrationProviders = Provider.integrations(
            excludeScmProviders = false,
            includeIf = providerActivation::isActive,
        )

        val installedProviders = getInstalledProviders(orgId, integrationProviders)

        return integrationProviders
            .filter { provider ->
                getPrerequisites(provider)
                    ?.let { prerequisites -> prerequisites.any { it in installedProviders } }
                    ?: true
            }
            .mapNotNull { provider ->
                integrationFactory.generate(
                    orgId = orgId,
                    identityId = identityId,
                    personId = personId,
                    provider = provider,
                    overrideAuthRedirectUrl = overrideAuthRedirectUrl,
                    dataIntegrationRedirectUrl = dataIntegrationRedirectUrl,
                    clientState = clientState,
                )
            }
    }

    private suspend fun getInstalledProviders(
        orgId: OrgId,
        integrationProviders: List<Provider>,
    ): Set<Provider> {
        return installationStore.findByProvider(orgId = orgId, providers = integrationProviders.toSet())
            .map { it.provider }
            .toSet()
    }

    private fun getPrerequisites(provider: Provider): Set<Provider>? {
        return when (provider) {
            Provider.BitbucketPipelines,
            Provider.Buildkite,
            Provider.CircleCI,
            Provider.GitHubActions,
            Provider.GitLabPipelines,
                -> ciScmMatrixResolver.getSupportedScmProviders(provider)

            else -> null
        }
    }
}
