package com.nextchaptersoftware.scm.config

import com.nextchaptersoftware.config.ConfigLoader
import kotlinx.serialization.Serializable

@Serializable
data class ScmConfig(
    val azureDevOps: AzureDevOpsConfig?,
    val bitbucketCloud: BitbucketCloudConfig?,
    val bitbucketDataCenter: BitbucketDataCenterConfig,
    val githubCloud: GitHubCloudConfig?,
    val githubEnterprise: GitHubEnterpriseConfig,
    val gitlabCloud: GitLabCloudConfig?,
    val gitlabSelfHosted: GitLabSelfHostedConfig,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<ScmConfig>(scope = "scm")

        fun getTestInstance(
            overrideEnvironment: String? = null,
            overrideUser: String? = null,
        ) = ConfigLoader.loadConfig<ScmConfig>(
            scope = "scm",
            overrideEnvironment = overrideEnvironment,
            overrideUser = overrideUser,
        )
    }
}
