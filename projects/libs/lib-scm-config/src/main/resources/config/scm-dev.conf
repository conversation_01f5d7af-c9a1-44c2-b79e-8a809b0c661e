// We are intentionally reusing the key from gitlabSelfHosted
sharedAppSecretsPublicKey: "MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAsmEiubF770Rca+use3oxIjnQ/uN4EVk1VrV+bjBPQiOlUrOO37uFTiLscswPyjTICXmlVn3SHWdsQEAmZ6o9biqT/pkqEBL+K4JpzlFCOc7xiTW4ZqvW2MP3d2W9yPRoGclEMJsqr/q8MzYi0WoGKd36TdWHzbWxjmK8aa/hxgB0uGXkaccfsS3VwF3qgz+u6RYzLXsbKi9yqv2NjK2lqsY0jMCTtGDr9oeFvdYAGXOvhqsLlF2uVv/dkR8T00UmH5m7BkNKVtIvm2UG+ldeyFnnkmOF2z6H0BSRbqBn8ugYlbsolV6GiGXnem3mg/17iuGUKMajM4BOcIaMGWGR5WjQFFzfHD1/dTvLY4d7HfQOh0u5y1uWzrbg1n4s3vp4oiZFh9tnGTZ7gAGZS8kSCKYZMOBfe96aNvecLfXxQcdSuO1YSQ4hiCmhyFX/ljzicy3Sdx3t4d/7nTX5B7IbDhdtg6i4j8+viMHoHsQdaH1ynCUtjksHmS//RSZybgdv7iSmi4H9kQ/Cwvkzy9JJosQtyJXLUGhRa8bpLy6YHhwFiHFweV68+/Xlfa9kYyDtDT8fDaZpFpqmP6Ci59zBoJEcQsWjVJcF0c5mCA783faxqVTyB1bfLuD/iMQruuOzMEmhzdz2zHTiivsthKrpSboXztgVsNMeuU14v2diCvkCAwEAAQ=="

azureDevOps {
    oauth {
        // Unblocked DEV
        // https://aex.dev.azure.com/app/view?clientId=049d814d-7d7f-4acf-8337-e67304132196
        clientId: "049D814D-7D7F-4ACF-8337-E67304132196"
    }
    webhookUrl: "https://dev.getunblocked.com/api/hooks/azureDevOps"
}

bitbucketCloud {
    oauth {
        // App [https://bitbucket.org/getunblocked/workspace/settings/api]
        clientId: "3PDG85QgJByaBbvJPt"
    }
    webhookUrl: "https://dev.getunblocked.com/api/hooks/bitbucket"
}

bitbucketDataCenter {
    webhookUrl: "https://dev.getunblocked.com/api/hooks/bitbucketDataCenter"
    appSecretsPublicKey: ${sharedAppSecretsPublicKey}
}

githubCloud {
    appHtmlUrl: "https://github.com/apps/dev-un-blocked"
    appId: "213675"
    appSlug: "dev-un-blocked"
    oauth {
        // App [https://github.com/organizations/NextChapterSoftware/settings/apps/dev-un-blocked]
        clientId: "Iv1.20df0a224cd6d0c1"
    }
}

githubEnterprise {
    appCreateRedirectUrl: "https://dev.getunblocked.com/dashboard/login/enterprise/exchange"
    appInstallRedirectUrl: "https://dev.getunblocked.com/dashboard/install/redirect"
    appUserAuthRedirectUrls: [
        "https://dev.getunblocked.com/dashboard/login/exchange"
        "http://localhost:9000/login/exchange"
        "http://localhost:9000/login/enterprise/exchange"
    ]
    appWebhookUrl: "https://dev.getunblocked.com/api/hooks/githubEnterprise"
    appHomepage: "https://dev.getunblocked.com"
    appName: "Unblocked DEV"
}

gitlabCloud {
    authRedirectOverrideUrl: "http://localhost:9000/login/exchange"
    oauth {
        // App [https://gitlab.com/groups/getunblocked/-/settings/applications/242309]
        clientId: "b60eccbd95db99ace6aea173546853193953394c152ebd403baf9a6594b0e257"
    }
    webhookUrl: "https://dev.getunblocked.com/api/hooks/gitlab"
}

gitlabSelfHosted {
    authRedirectOverrideUrl: "http://localhost:9000/login/exchange"
    webhookUrl: "https://dev.getunblocked.com/api/hooks/gitlabSelfHosted"
    appSecretsPublicKey: ${sharedAppSecretsPublicKey}
}
