azureDevOps {
    apiBaseUrl: "https://app.vssps.visualstudio.com"
    authRedirectOverrideUrl: null
    oauth {
        authorizationUrl: "https://app.vssps.visualstudio.com/oauth2/authorize"
        includeResponseType: true
        scopes: "vso.build vso.code vso.entitlements vso.graph vso.memberentitlementmanagement vso.profile_write vso.project vso.threads_full vso.work"
    }
}

bitbucketCloud {
    apiBaseUrl: "https://api.bitbucket.org/2.0/"
    oauth {
        authorizationUrl: "https://bitbucket.org/site/oauth2/authorize"
        includeResponseType: true
    }
}

bitbucketDataCenter {
    authRedirectOverrideUrl: null
}

githubCloud {
    apiBaseUrl: "https://api.github.com"
    appHtmlUrl: "https://github.com/apps/dev-un-blocked"
    appSlug: "dev-un-blocked"
    oauth {
        authorizationUrl: "https://github.com/login/oauth/authorize"
        includeResponseType: false
    }
}

gitlabCloud {
    apiBaseUrl: "https://gitlab.com/api/v4/"
    authRedirectOverrideUrl: null
    oauth {
        authorizationUrl: "https://gitlab.com/oauth/authorize"
        includeResponseType: true
        scopes: "api read_user"
    }
}

gitlabSelfHosted {
    authRedirectOverrideUrl: null
}
