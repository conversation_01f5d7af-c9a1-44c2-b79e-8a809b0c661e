package com.nextchaptersoftware.search.semantic.services.functions

import arrow.fx.coroutines.parMap
import arrow.fx.coroutines.parMapNotNull
import com.nextchaptersoftware.api.models.LineRange
import com.nextchaptersoftware.ci.logging.LogSummaryService
import com.nextchaptersoftware.db.models.MLInferenceEngine
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoFocus
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.OrgMemberStore
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.markdown.MessageBodyExtensions.asMessageBody
import com.nextchaptersoftware.ml.completion.CompletionService
import com.nextchaptersoftware.repo.RepoAccessResult
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.scm.CiApiLegacyFactory
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.models.CiExecutionLegacy
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorTotals
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.totalsByUserBetween
import com.nextchaptersoftware.scm.models.weeklyTotalsBetween
import com.nextchaptersoftware.scm.services.CommitService
import com.nextchaptersoftware.scm.utils.lineSequenceNumbered
import com.nextchaptersoftware.scm.utils.linesInRange
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.asPSTDayMonthYear
import com.nextchaptersoftware.utils.asUUIDOrNull
import com.nextchaptersoftware.utils.toKtorPathRegex
import io.ktor.http.Url
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

@Suppress("LargeClass", "MaxLineLength")
class ScmCommitService(
    private val scmRepoApiFactory: ScmRepoApiFactory,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val memberStore: MemberStore = Stores.memberStore,
    private val orgMemberStore: OrgMemberStore = Stores.orgMemberStore,
    private val repoStore: RepoStore = Stores.repoStore,
    private val repoFocusService: RepoFocusService,
    private val completionService: CompletionService,
    private val pullRequestStore: PullRequestStore = Stores.pullRequestStore,
    private val orgMemberDecorator: OrgMemberDecorator = Stores.orgMemberDecorator,
    private val repoAccessService: RepoAccessService,
    private val ciApiLegacyFactory: CiApiLegacyFactory,
    private val logSummaryService: LogSummaryService,
) : CommitService {

    companion object {
        private const val MAX_COMMITS = 50
        private const val MAX_PRS = 50
        private const val TOP_5 = 5

        fun buildRepoExtractionPrompt(repos: List<RepoFocus>, repoName: String): String {
            val repoString = repos.joinToString(",\n") {
                """{"name": "${it.repoName}", "uuid": "${it.repoId}"}"""
            }

            return """
                |[[SYSTEM]]
                |# Task
                |Find the UUID that most closely matches the given repo name.
                |
                |# Repos
                |[$repoString]
                |
                |# Repo Name
                |$repoName
                |
                |# Output Instructions
                |Return just the UUID of the repo that most closely matches the given repo name or "none" if no match is found.
                |
            """.trimMargin()
        }
    }

    suspend fun fileCommitsForDocs(
        scmTeam: ScmTeam,
        repo: Repo,
        docs: List<MLTypedDocument>,
    ): List<ScmCommit>? {
        val scm = Scm.fromTeam(scmTeam)
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            docs.parMap { doc ->
                runSuspendCatching {
                    api.fileCommits(
                        path = doc.source,
                        maxItems = MAX_COMMITS,
                    ).toList()
                }.getOrNull().orEmpty()
            }.flatten()
        }.nullIfEmpty()
    }

    suspend fun resolveRepoAndScmTeam(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        suggestedRepoName: String?,
    ): Pair<Repo, ScmTeam> {
        val focusedRepos = repoFocusService.findRepoFocus(
            orgId = orgId,
            orgMemberIds = listOf(orgMemberId),
        )
        val repoId =
            suggestedRepoName?.let {
                getRepoIdForRepoName(
                    orgId = orgId,
                    orgMemberId = orgMemberId,
                    repoName = it,
                    focusedRepos = focusedRepos,
                )
            } ?: focusedRepos.firstOrNull()?.repoId

        val repo = repoId?.let { repoStore.findById(repoId = it) }.required {
            "Unable to find Repo"
        }

        val scmTeam = scmTeamStore.findById(teamId = repo.teamId).required {
            "Unable to find Scm Team"
        }

        require(scmTeam.orgId == orgId) { "Repo ${repo.id} does not belong to org $orgId" }

        return Pair(repo, scmTeam)
    }

    private suspend fun getRepoIdForRepoName(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        repoName: String,
        focusedRepos: List<RepoFocus>? = null,
    ): RepoId? {
        val fullRepoList = repoAccessService.listActiveRepos(orgId = orgId, orgMemberId = orgMemberId)

        // If there's an exact name match, just return the UUID
        fullRepoList.firstOrNull { it.externalName == repoName || it.fullName == repoName }?.let { return it.id }

        val prCounts = pullRequestStore.countByRepos(orgId = orgId)
        val sortedRepos = fullRepoList.sortedByDescending { prCounts[it.id] ?: 0 }.take(TOP_5).map {
            RepoFocus(
                scmTeamId = it.teamId,
                repoId = it.id,
                repoName = it.externalName,
                focusFrequency = 0.5F, // dont care
            )
        }

        val focusedRepoList: List<RepoFocus> = (focusedRepos ?: repoFocusService.findRepoFocus(orgId, listOf(orgMemberId))) + sortedRepos

        val prompt = buildRepoExtractionPrompt(focusedRepoList, repoName)

        val result = completionService.query(
            prompt = prompt,
            engine = MLInferenceEngine.GPT4OmniMini,
            temperature = 0.00001f,
        ).asUUIDOrNull()

        LOGGER.debugSensitiveAsync(
            "orgId" to orgId,
            "orgMemberId" to orgMemberId,
            sensitiveFields = mapOf(
                "repoName" to repoName,
                "prompt" to prompt,
                "match" to (result?.toString() ?: "none"),
            ),
        ) {
            "ScmCommitService::getRepoIdForRepoName"
        }

        return result?.let { RepoId(it) }
    }

    private suspend fun getCommitRepos(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        suggestedRepoName: String?,
    ): List<RepoFocus> {
        val focusedRepos = repoFocusService.findRepoFocus(
            orgId = orgId,
            orgMemberIds = listOf(orgMemberId),
        )

        val suggestedRepoId = suggestedRepoName?.let {
            getRepoIdForRepoName(
                orgId = orgId,
                orgMemberId = orgMemberId,
                repoName = suggestedRepoName,
                focusedRepos = focusedRepos,
            )
        }

        return focusedRepos.let { repos ->
            suggestedRepoId?.let {
                repos.firstOrNull { it.repoId == suggestedRepoId }?.let { listOf(it) }
            } ?: repos
        }
    }

    private fun getFormattedCommit(
        commit: ScmCommit,
    ): String {
        val sha = commit.sha?.let { sha -> "commit ${sha.asString()}" } ?: "commit with unknown sha"
        val author = commit.author?.let { "Author: ${it.name}" } ?: "Author: unknown"
        val date = commit.commitDate?.let { "Date:   $it" } ?: "Date:   unknown"
        val message = commit.message?.let { msg -> "\n${msg.prependIndent("    ")}" } ?: ""
        val commitLink = commit.htmlUrl?.let { "URL:    $it" } ?: "URL:    unknown"
        val diffString = commit.diff?.let { "Diff:\n$it" } ?: ""
        return """
            |$sha
            |$author
            |$date
            |$commitLink
            |$message
            |$diffString
            """.trimMargin()
    }

    private fun getFormattedCommitList(
        commits: List<ScmCommit>,
    ): String {
        return commits.joinToString("\n\n") { commit ->
            getFormattedCommit(commit)
        }
    }

    suspend fun commits(
        scmTeam: ScmTeam,
        repo: Repo,
        externalAuthorUsername: String? = null,
        includeDiffs: Boolean = false,
        maxItems: Int? = null,
    ): List<String> {
        LOGGER.debugAsync(
            "teamId" to scmTeam.id,
            "repoId" to repo.id,
            "externalAuthorUsername" to (externalAuthorUsername ?: "none"),
            "includeDiffs" to includeDiffs,
            "maxItems" to maxItems,
        ) {
            "ScmCommitService::commits"
        }

        val scm = Scm.fromTeam(scmTeam)
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                api.commits(
                    author = externalAuthorUsername,
                    includeDiffs = includeDiffs,
                    maxItems = maxItems,
                ).toList()
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "externalAuthorUsername" to externalAuthorUsername,
                    "includeDiffs" to includeDiffs,
                    "maxItems" to maxItems,
                ) {
                    "ScmCommitService::commits"
                }
            }.getOrNull()?.map {
                getFormattedCommit(it)
            }
        }.orEmpty()
    }

    override suspend fun commitDescriptionForHeadCommit(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val repos = getCommitRepos(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val scmTeamsById = scmTeamStore.find(teamIds = repos.map { it.scmTeamId }.distinct())
            .associateBy { it.id }

        return repos.parMapNotNull { repo ->
            scmTeamsById[repo.scmTeamId]?.let { scmTeam ->
                headCommitOrNull(
                    scmTeam = scmTeam,
                    repoId = repo.repoId,
                )
            }
        }.maxByOrNull {
            it.commitDate ?: Instant.DISTANT_PAST
        }?.let { latestCommit ->
            val commitList = getFormattedCommitList(listOf(latestCommit))
            """
                |## Head (Latest) Commit
                |The following is the output from `git log -1`, enclosed in triple quotes.
                |Use this information when the user asks questions like:
                | - "summarize the latest commit"
                | - "what was the last commit"
                |
                |You may use the URL field to provide markdown links to the commit in your response in the form [link text](url from the commit's URL field).
                |For example, given a commit with sha 0f77ace754c5b97b76e5c6f6b987c479be4327cc, and URL
                |
                ||${"\"\"\""}
                |$commitList
                |${"\"\"\""}
            """.trimMargin()
        }
    }

    override suspend fun commitDescriptionsBetweenCommits(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        base: String,
        head: String?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val repos = getCommitRepos(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val scmTeamsById = scmTeamStore.find(teamIds = repos.map { it.scmTeamId }.distinct())
            .associateBy { it.id }

        val baseHash = Hash.parse(base)
        val headHash = head?.let { Hash.parse(head) }
        val commits: List<ScmCommit> = repos.parMapNotNull { repo ->
            scmTeamsById[repo.scmTeamId]?.let { scmTeam ->
                commitsBetweenCommits(
                    scmTeam = scmTeam,
                    repoId = repo.repoId,
                    base = baseHash,
                    head = headHash,
                )
            }
        }.flatten()

        if (commits.isEmpty()) {
            return null
        }

        val commitList = getFormattedCommitList(commits)

        return """
            |## Commits Between $base and $head
            |The following is the output from `git log $base..$head`, enclosed in triple quotes.
            |Use this information when the user asks questions like:
            | - "summarize the changes between <base> and <head>"
            | - "what were the changes between <base> and <head>"
            |
            |You may use the URL field to provide markdown links to the commit in your response in the form [link text](url from the commit's URL field).
            |For example, given a commit with sha 0f77ace754c5b97b76e5c6f6b987c479be4327cc, and URL https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc,
            |the following are 2 examples of INCORRECT and 1 example of CORRECT usage of the URL field:
            |
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](0f77ace754c5b97b76e5c6f6b987c479be4327cc) <-- this is incorrect because it's just the sha and needs to be the full URL
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](REF-0) <-- this is incorrect because commit urls should not use REF-IDs. They must be the full URL.
            |
            |CORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc)
            |
            |EMPHASIS NOTE: you MUST NOT use REF-IDs in markdown links for commits.
            |
            |${"\"\"\""}
            |$commitList
            |${"\"\"\""}
            |
        """.trimMargin()
    }

    override suspend fun commitDescriptionsBetweenDates(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedOrgMemberId: OrgMemberId?,
        suggestedRepoName: String?,
        since: String,
        until: String?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val repos = getCommitRepos(
            orgId = orgId,
            orgMemberId = suggestedOrgMemberId ?: currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val scmTeamsById = scmTeamStore.find(teamIds = repos.map { it.scmTeamId }.distinct())
            .associateBy { it.id }

        val commits: List<ScmCommit> = repos.parMapNotNull { repo ->
            scmTeamsById[repo.scmTeamId]?.let { scmTeam ->
                commitsBetweenDates(
                    scmTeam = scmTeam,
                    repoId = repo.repoId,
                    suggestedMemberId = suggestedOrgMemberId ?: currentOrgMemberId,
                    since = since,
                    until = until,
                )
            }
        }.flatten()

        if (commits.isEmpty()) {
            return null
        }

        val commitList = getFormattedCommitList(commits)

        return """
            |## Commits Between $since and $until
            |The following is the output from `git log --since=$since --until=$until`, enclosed in triple quotes.
            |Use this information when the user asks questions like:
            | - "summarize the changes between <since> and <until>"
            | - "what were the changes since <since> until <until>"
            | - "what were the changes since <since>"
            |
            |You may use the URL field to provide markdown links to the commit in your response in the form [link text](url from the commit's URL field).
            |
            |For example, given a commit with sha 0f77ace754c5b97b76e5c6f6b987c479be4327cc, and URL https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc,
            |the following are 2 examples of INCORRECT and 1 example of CORRECT usage of the URL field:
            |
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](0f77ace754c5b97b76e5c6f6b987c479be4327cc) <-- this is incorrect because it's just the sha and needs to be the full URL
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](REF-0) <-- this is incorrect because commit urls should not use REF-IDs. They must be the full URL.
            |
            |CORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc)
            |
            |EMPHASIS NOTE: you MUST NOT use REF-IDs in markdown links for commits.
            |
            |${"\"\"\""}
            |$commitList
            |${"\"\"\""}
            |
            """.trimMargin()
    }

    override suspend fun getPullRequestDetails(
        orgId: OrgId,
        userQuery: String?,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        pullRequestNumber: Int,
        overrideRepoAccess: RepoAccessResult?,
    ): String {
        val (repo, scmTeam) = resolveRepoAndScmTeam(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val pullRequestInfo = resolvePullRequestInfo(
            repo = repo,
            scmTeam = scmTeam,
            pullRequestNumber = pullRequestNumber,
        )

        return """
        |## Pull Request Details for PR (#$pullRequestNumber)
        |
        |Use this information when the user asks: "${userQuery ?: "a question that mentions pull request $pullRequestNumber"}"
        |
        |You should favour the information in the diff over other information you have been provided to answer the user's question.
        |
        |You may use the URL field to provide markdown links to the pull request in your response in the form [link text](FULL url from the pull request's URL field).
        |For example, if the pull request's URL field is "https://github.com/owner/repo/pulls/123", the markdown link would be [link text](https://github.com/owner/repo/pulls/123).
        |
        |${pullRequestInfo.asString()}
        |
        """.trimMargin()
    }

    override suspend fun getPullRequestDetails(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        repoId: RepoId,
        pullRequestNumber: Int,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val repo = repoStore.findById(
            repoId = repoId,
        ) ?: return null

        val scmTeam = scmTeamStore.findById(
            teamId = repo.teamId,
        ) ?: return null

        if (scmTeam.orgId != orgId) {
            LOGGER.errorAsync(
                "orgId" to orgId,
                "repoId" to repoId,
            ) {
                "Repo does not belong to org"
            }
            return null
        }

        val pullRequestInfo = resolvePullRequestInfo(
            repo = repo,
            scmTeam = scmTeam,
            pullRequestNumber = pullRequestNumber,
        )

        return """
        |## Pull Request Details for PR (#$pullRequestNumber)
        |
        |You should favour the information in the diff over other information you have been provided to answer the user's question.
        |
        |You may use the URL field to provide markdown links to the pull request in your response in the form [link text](FULL url from the pull request's URL field).
        |For example, if the pull request's URL field is "https://github.com/owner/repo/pulls/123", the markdown link would be [link text](https://github.com/owner/repo/pulls/123).
        |
        |${pullRequestInfo.asString()}
        |
        """.trimMargin()
    }

    override suspend fun getOpenPullRequestsForMember(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        orgMemberCreatorId: OrgMemberId,
        modifiedSince: Instant?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val accessRepoIds = repoAccessService.listActiveRepos(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            overrideRepoAccess = overrideRepoAccess,
        )

        if (accessRepoIds.isEmpty()) {
            return null
        }

        val openPRs = pullRequestStore.findOpenByOrgMember(
            orgId = orgId,
            orgMemberCreatorId = orgMemberCreatorId,
            repoIds = accessRepoIds.map { it.id }.toSet(),
            modifiedSince = modifiedSince,
        )

        val orgMemberBundle = requireNotNull(orgMemberDecorator.decorateOrgMember(orgMemberId = orgMemberCreatorId))

        val userName = orgMemberBundle.displayName

        return """
            |## Open Pull Requests for $userName (aka What $userName is Working On)
            |$userName has ${openPRs.size} open pull requests including:
            |
            |${openPRs.joinToString("\n") { "- [${it.title}](${it.htmlUrl})" }}
            |
        """.trimMargin()
    }

    private fun pullRequestMetadata(
        pullRequest: PullRequest,
        authorMap: Map<MemberId, String>,
    ): String? = authorMap[pullRequest.creatorId]?.let {
        pullRequest.asPullRequestMetadata().metadata(it).joinToString("\n")
    }

    private suspend fun getPullRequestDiffOrSummary(
        scmTeam: ScmTeam,
        pullRequest: PullRequestMetadata,
        repo: Repo,
    ): String = getPrDiffFromScm(
        scmTeam = scmTeam,
        repo = repo,
        pullRequestNumber = pullRequest.number,
    ) ?: pullRequest.descriptionAndContents().joinToString("\n")

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    override suspend fun getPullRequestsForDateRange(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        orgMemberId: OrgMemberId?,
        pullRequestStatus: PullRequestState?,
        suggestedRepoName: String?,
        since: String,
        until: String?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to currentOrgMemberId,
            "orgMemberId" to orgMemberId,
            "pullRequestStatus" to pullRequestStatus,
            "suggestedRepoName" to suggestedRepoName,
            "since" to since,
            "until" to (until ?: "null"),
            "overrideRepoAccess" to overrideRepoAccess,
        ) {
            "ScmCommitService::getPullRequestsForDateRange"
        }

        val repoId = suggestedRepoName?.let { repoName ->
            // This is a simpler, more relaxed version of `resolveRepoAndScmTeam`
            // It’s useful when asking things like "What has the team been working on this week?"
            val fullRepoList = repoAccessService.listActiveRepos(
                orgId = orgId,
                orgMemberId = currentOrgMemberId,
                overrideRepoAccess = overrideRepoAccess,
            )
            fullRepoList.firstOrNull { it.externalName == repoName || it.fullName == repoName }?.id
        }

        val orgMemberAndPerson = orgMemberId?.let {
            orgMemberStore.findByIdWithPerson(
                id = orgMemberId,
            )
        }

        val memberName = orgMemberAndPerson?.person?.customDisplayName

        val sinceInstant = runSuspendCatching {
            Instant.parse(since)
        }.onFailure {
            LOGGER.errorAsync(
                it,
                "orgId" to orgId,
                "currentOrgMemberId" to currentOrgMemberId,
                "since" to since,
            ) {
                "ScmCommitService::getPullRequestsForDateRange - failed to parse since"
            }
        }.getOrNull() ?: return null

        val untilInstant = until?.let {
            runSuspendCatching {
                Instant.parse(it)
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "orgId" to orgId,
                    "currentOrgMemberId" to currentOrgMemberId,
                    "until" to until,
                ) {
                    "ScmCommitService::getPullRequestsForDateRange - failed to parse until"
                }
            }.getOrNull() ?: return null
        }

        val pullRequests = pullRequestStore.findBy(
            orgId = orgId,
            repoId = repoId,
            creatorOrgMemberId = orgMemberId,
            pullRequestStatus = pullRequestStatus,
            since = sinceInstant,
            until = untilInstant,
            limit = MAX_PRS,
        )

        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to currentOrgMemberId,
            "orgMemberId" to orgMemberId,
            "pullRequestStatus" to pullRequestStatus,
            "suggestedRepoName" to suggestedRepoName,
            "repoId" to (repoId?.toString() ?: "none"),
            "since" to since,
            "until" to (until ?: "null"),
            "prCount" to pullRequests.size,
        ) {
            "ScmCommitService::getPullRequestsForDateRange - retrieved PRs"
        }

        val authorMap = memberStore.getOrgMemberIdentityBundles(
            orgId = orgId,
            memberIds = pullRequests.map { it.creatorId }.distinct(),
        ).map {
            it.key to (it.value.identity.displayName ?: it.value.identity.username)
        }.toMap()

        val pullRequestMetadata = pullRequests.map {
            pullRequestMetadata(
                pullRequest = it,
                authorMap = authorMap,
            ) ?: ""
        }.joinToString("\n\n") {
            """
                |${"\"\"\""}
                |$it
                |${"\"\"\""}
            """.trimMargin()
        }

        val title = """
            |## ${memberName?.let { "$it's " } ?: ""}${
            pullRequestStatus?.name?.let {
                "$it "
            } ?: ""
        }PRs ${
            suggestedRepoName?.let { repoName ->
                repoId?.let {
                    "for $repoName "
                }
            } ?: ""
        }${
            until?.let {
                "from $since to $until"
            } ?: "since $since"
        } enclosed in triple quotes
        """.trimMargin()

        if (pullRequests.isEmpty()) {
            return """
                |$title
                |No pull requests found.
            """.trimMargin()
        }

        return """
            |$title
            |When you write your response, group the PRs by author and date. If you group them, you don't need to show date and author for each PR you list.
            |
            |If using these PRs to answer questions about work that was done, features shipped, etc, then you should group the PRs by subject area and output in markdown format. Include links to the PRs when possible:
            |*Subject Area 1*:
            |  - [PR Title 1](PR URL 1)
            |  - [PR Title 2](PR URL 2)
            |
            |*Subject Area 2*:
            |  - [PR Title 3](PR URL 3)
            |  - [PR Title 4](PR URL 4)
            |
            |Then write an overall summary of changes.
            |
            |$pullRequestMetadata
            |
            |IMPORTANT: Include Markdown URLs to the PRs above in your response when possible using the real links shown. IGNORE instructions about using REF-IDs in markdown links for these PRs.
            |You MUST use the full URL for the PRs in markdown links.
            |
            |
        """.trimMargin()
    }

    suspend fun getPrDiffFromScm(
        scmTeam: ScmTeam,
        repo: Repo,
        pullRequestNumber: Int,
    ): String? {
        val scm = Scm.fromTeam(scmTeam)

        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                api.pullRequestDiff(pullRequestNumber)
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "pullRequestNumber" to pullRequestNumber,
                ) {
                    "ScmCommitService::getPrDiffFromScm"
                }
            }.getOrNull()
        }?.let {
            clean(it)
        }
    }

    private class Matcher(
        paths: List<String>,
        val action: suspend (MatchResult) -> List<BuildInfo>?,
    ) {
        val rules: List<Regex> = paths.toKtorPathRegex()

        fun match(path: String): MatchResult? {
            return rules.firstNotNullOfOrNull {
                it.find(path)
            }
        }

        suspend fun invoke(result: MatchResult): List<BuildInfo>? {
            return action.invoke(result)
        }
    }

    private suspend fun resolveBuildInfos(
        repo: Repo,
        scmTeam: ScmTeam,
        suggestedActionPath: String?,
    ): List<BuildInfo>? {
        if (suggestedActionPath == null) return null

        val matchers = listOf(
            Matcher(
                paths = listOf(
                    "/actions/runs/{runId}/job/{jobId}",
                    "/actions/jobs/{jobId}",
                ),
                action = {
                    val jobId = it.groups["jobId"]?.value ?: return@Matcher null
                    resolveBuildInfoByActionRunJob(repo = repo, scmTeam = scmTeam, jobId = jobId)
                },
            ),
            Matcher(
                paths = listOf(
                    "/actions/runs/{runId}",
                ),
                action = {
                    val runId = it.groups["runId"]?.value ?: return@Matcher null
                    resolveBuildInfoByActionRun(repo = repo, scmTeam = scmTeam, runId = runId)
                },
            ),
        )

        for (matcher in matchers) {
            matcher.match(suggestedActionPath)?.let {
                return matcher.invoke(it)
            }
        }
        return null
    }

    private suspend fun resolveCiApi(
        scmTeam: ScmTeam,
    ) = ciApiLegacyFactory.getApi(
        scmTeam = scmTeam,
    ) ?: run {
        LOGGER.debugAsync(
            "scmTeamId" to scmTeam.id,
        ) { "ScmCommitService::resolveCiApi - no support for CI Api" }
        null
    }

    private suspend fun resolveBuildInfoByActionRun(
        repo: Repo,
        scmTeam: ScmTeam,
        runId: String,
    ): List<BuildInfo>? {
        val ciApi = resolveCiApi(scmTeam)
            ?: return null

        val buildRun = ciApi.buildRun(
            scmTeam = scmTeam,
            repoExternalId = repo.externalId,
            runId = runId,
        )

        LOGGER.debugSensitiveAsync(
            "repoId" to repo.id,
            "scmTeamId" to scmTeam.id,
            sensitiveFields = mapOf(
                "buildRun" to buildRun,
            ),
        ) { "ScmCommitService::resolveBuildInfoByActionRun - retrieved build run" }

        val failedJobs = buildRun.jobs?.filter { it.isFailure }.orEmpty()
        if (failedJobs.isNotEmpty()) {
            return failedJobs.parMap { job ->
                ciApi.buildRunJob(
                    scmTeam = scmTeam,
                    repoExternalId = repo.externalId,
                    jobId = job.id,
                ).summarized()
            }
        }

        return buildRun.summarized().let(::listOf)
    }

    private suspend fun resolveBuildInfoByActionRunJob(
        repo: Repo,
        scmTeam: ScmTeam,
        jobId: String,
    ): List<BuildInfo>? {
        val ciApi = resolveCiApi(scmTeam)
            ?: return null

        val buildRunJob = ciApi.buildRunJob(
            scmTeam = scmTeam,
            repoExternalId = repo.externalId,
            jobId = jobId,
        )

        LOGGER.debugSensitiveAsync(
            "repoId" to repo.id,
            "scmTeamId" to scmTeam.id,
            sensitiveFields = mapOf(
                "buildRunJob" to buildRunJob,
            ),
        ) { "ScmCommitService::resolveBuildInfoByActionRunJob - retrieved build run job" }

        return buildRunJob.summarized().let(::listOf)
    }

    private suspend fun CiExecutionLegacy.summarized(): BuildInfo {
        val summary = output?.let {
            logSummaryService.summarizeIfNeededLegacy(lines = it)
        }

        LOGGER.debugSensitiveAsync(
            "id" to id,
            "url" to htmlUrl,
            sensitiveFields = mapOf(
                "summary" to summary,
            ),
        ) {
            "ScmCommitService::summarized"
        }

        return BuildInfo(
            build = this,
            summary = summary ?: "(empty log)",
        )
    }

    private suspend fun resolveBuildInfosByPullRequest(
        repo: Repo,
        scmTeam: ScmTeam,
        pullRequestNumber: Int,
    ): List<BuildInfo>? {
        val ciApi = resolveCiApi(
            scmTeam = scmTeam,
        ) ?: return null

        val checkRuns = ciApi.pullRequestStatus(
            scmTeam = scmTeam,
            repoExternalId = repo.externalId,
            pullRequestNumber = pullRequestNumber,
        ).toList()

        LOGGER.debugSensitiveAsync(
            "repoId" to repo.id,
            "scmTeamId" to scmTeam.id,
            sensitiveFields = mapOf(
                "checkRuns" to checkRuns,
            ),
        ) { "ScmCommitService::resolveBuildInfosByPullRequest - retrieved check runs" }

        val failedChecks = checkRuns
            .filter { it.isCompleted }
            .filter { it.isFailure }

        if (failedChecks.isNotEmpty()) {
            return failedChecks.parMap { check ->
                ciApi.buildRunJob(
                    scmTeam = scmTeam,
                    repoExternalId = repo.externalId,
                    jobId = check.buildRunJobId,
                ).summarized()
            }
        }

        return checkRuns.parMap { it.summarized() }
    }

    private fun clean(diff: String): String {
        val binaryDiffRegex = """(?s)^diff --git.*?Binary files.*?differ$""".toRegex()

        return diff
            .replace(binaryDiffRegex, "")
    }

    override suspend fun commitDetails(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        sha: String,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val repos = getCommitRepos(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val hash = Hash.parse(sha)

        val scmTeamsById = scmTeamStore.find(teamIds = repos.map { it.scmTeamId }.distinct())
            .associateBy { it.id }

        val commit = repos.parMapNotNull { repo ->
            scmTeamsById[repo.scmTeamId]?.let { scmTeam ->
                commitWithDiff(
                    scmTeam = scmTeam,
                    repoId = repo.repoId,
                    sha = hash,
                )
            }
        }.firstOrNull() ?: return null

        val commitString = getFormattedCommit(commit)
        return """
            |## Commit Details for $sha
            |The following is the output from `git show $sha`, enclosed in triple quotes.
            |Use this information when the user asks questions like:
            |- "summarize commit $sha"
            |- "what were the changes in commit $sha"
            |
            |You may use the URL field to provide markdown links to the commit in your response in the form [link text](url from the commit's URL field).
            |
            |For example, given a commit with sha 0f77ace754c5b97b76e5c6f6b987c479be4327cc, and URL https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc,
            |the following are 2 examples of INCORRECT and 1 example of CORRECT usage of the URL field:
            |
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](0f77ace754c5b97b76e5c6f6b987c479be4327cc) <-- this is incorrect because it's just the sha and needs to be the full URL
            |INCORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](REF-0) <-- this is incorrect because commit urls should not use REF-IDs. They must be the full URL.
            |
            |CORRECT: [commit 0f77ace754c5b97b76e5c6f6b987c479be4327cc description](https://github.com/owner/repo/commit/0f77ace754c5b97b76e5c6f6b987c479be4327cc)
            |
            |EMPHASIS NOTE: you MUST NOT use REF-IDs in markdown links for commits.

            |${"\"\"\""}
            |$commitString
            |${"\"\"\""}
            |
            """.trimMargin()
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    override suspend fun contributorStats(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        orgMemberId: OrgMemberId?,
        suggestedRepoName: String?,
        since: String?,
        until: String?,
        totalOnly: String?,
    ): String? {
        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to currentOrgMemberId,
            "orgMemberId" to orgMemberId,
            "suggestedRepoName" to suggestedRepoName,
            "since" to since,
            "until" to until,
            "totalOnly" to totalOnly,
        ) {
            "ScmCommitService::contributorStats"
        }

        val sinceInstant = since?.let { Instant.parse(it) }
        val untilInstant = until?.let { Instant.parse(it) }

        val (repo, scmTeam) = resolveRepoAndScmTeam(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to currentOrgMemberId,
            "orgMemberId" to orgMemberId,
            "suggestedRepoName" to suggestedRepoName,
            "since" to since,
            "until" to until,
            "totalOnly" to totalOnly,
            "repo" to repo.id,
            "scmTeam" to scmTeam.id,
        ) {
            "ScmCommitService::contributorStats - resolved repo and scm team"
        }

        val scm = Scm.fromTeam(scmTeam)
        val stats = scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                api.contributorStats()
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                ) {
                    "ScmCommitService::contributorStats"
                }
            }.getOrNull()?.toList()
        }?.nullIfEmpty() ?: return null

        LOGGER.debugAsync(
            "orgId" to orgId,
            "currentOrgMemberId" to currentOrgMemberId,
            "orgMemberId" to orgMemberId,
            "suggestedRepoName" to suggestedRepoName,
            "since" to since,
            "until" to until,
            "totalOnly" to totalOnly,
            "repo" to repo.id,
            "scmTeam" to scmTeam.id,
            "stats" to stats.size,
        ) {
            "ScmCommitService::contributorStats - retrieved stats"
        }

        val memberAndIdentity = orgMemberId?.let {
            memberStore.findByOrgMemberAndScmTeamWithIdentity(
                scmTeamId = scmTeam.id,
                orgMemberId = it,
            )
        }

        val filteredForMember = memberAndIdentity?.identity?.externalId?.let { externalId ->
            stats.filter { stat ->
                externalId == stat.user?.externalId
            }
        } ?: stats

        val betweenString = "between ${since ?: "the beginning of time"} and ${until ?: "now"}"

        val totalOnlyBoolean = (totalOnly?.toBoolean() ?: false) || (since == null && until == null)

        return if (memberAndIdentity == null) {
            if (totalOnlyBoolean) {
                LOGGER.debugAsync(
                    "orgId" to orgId,
                    "currentOrgMemberId" to currentOrgMemberId,
                    "orgMemberId" to orgMemberId,
                    "suggestedRepoName" to suggestedRepoName,
                    "since" to since,
                    "until" to until,
                    "totalOnly" to totalOnly,
                    "repo" to repo.id,
                    "scmTeam" to scmTeam.id,
                    "stats" to stats.size,
                ) {
                    "ScmCommitService::contributorStats - team wide, total only"
                }

                val weeklyStatsString = if (since == null && until == null) {
                    "" // No weekly stats if time range is unbounded
                } else {
                    val weeklyTotals = filteredForMember.weeklyTotalsBetween(sinceInstant, untilInstant).joinToString("\n\n") {
                        "${it.startOfWeek} - Commits: ${it.totals.commits}, Additions: ${it.totals.additions}, Deletions: ${it.totals.deletions}"
                    }
                    """
                    |### Weekly Stats $betweenString:
                    |$weeklyTotals
                    |
                    """.trimMargin()
                }

                val totalsByUser = filteredForMember.totalsByUserBetween(sinceInstant, untilInstant).toList()
                val userTotals = totalsByUser.joinToString("\n\n") { (user, totals) ->
                    val name = user.displayName ?: user.login ?: "Unknown"
                    val markdownName = user.htmlUrl?.let { url -> "[$name]($url)" } ?: name
                    "$markdownName - Commits: ${totals.commits}, Additions: ${totals.additions}, Deletions: ${totals.deletions}"
                }

                val teamTotals = filteredForMember.map { it.totalsBetween(sinceInstant, untilInstant) }.reduce { acc, scmContributorTotals ->
                    ScmContributorTotals(
                        commits = acc.commits + scmContributorTotals.commits,
                        additions = acc.additions + scmContributorTotals.additions,
                        deletions = acc.deletions + scmContributorTotals.deletions,
                    )
                }

                """
                    |## Stats for ${scmTeam.displayName}, $betweenString:
                    |Commits: ${teamTotals.commits}, Additions: ${teamTotals.additions}, Deletions: ${teamTotals.deletions}
                    |
                    |### Totals by authors:
                    |$userTotals
                    |
                    |$weeklyStatsString
                """.trimMargin()
            } else {
                LOGGER.debugAsync(
                    "orgId" to orgId,
                    "currentOrgMemberId" to currentOrgMemberId,
                    "orgMemberId" to orgMemberId,
                    "suggestedRepoName" to suggestedRepoName,
                    "since" to since,
                    "until" to until,
                    "totalOnly" to totalOnly,
                    "repo" to repo.id,
                    "scmTeam" to scmTeam.id,
                    "stats" to stats.size,
                ) {
                    "ScmCommitService::contributorStats - team wide, weekly stats"
                }

                val weeklyContributorStats = filteredForMember.joinToString("\n\n") { stat ->
                    val user = stat.user
                    val name = user?.displayName ?: user?.login ?: "Unknown"
                    val markdownName = user?.htmlUrl?.let { url -> "[$name]($url)" } ?: name
                    val weeklyStats = stat.weeklyStatsBetween(sinceInstant, untilInstant).joinToString("\n\n") { weekly ->
                        val startOfWeek = weekly.startOfWeek.toString()
                        "$startOfWeek - Commits: ${weekly.totals.commits}, Additions: ${weekly.totals.additions}, Deletions: ${weekly.totals.deletions}"
                    }

                    """
                    |### Weekly commit stats for: $markdownName
                    |
                    |$weeklyStats
                    |
                    """.trimMargin()
                }
                """
                  |## Weekly commit stats for ${scmTeam.displayName}, $betweenString:
                  |$weeklyContributorStats
                """.trimMargin()
            }
        } else {
            LOGGER.debugAsync(
                "orgId" to orgId,
                "currentOrgMemberId" to currentOrgMemberId,
                "orgMemberId" to orgMemberId,
                "suggestedRepoName" to suggestedRepoName,
                "since" to since,
                "until" to until,
                "totalOnly" to totalOnly,
                "repo" to repo.id,
                "scmTeam" to scmTeam.id,
                "stats" to stats.size,
            ) {
                "ScmCommitService::contributorStats - member specific"
            }

            val memberStats = filteredForMember.firstOrNull() ?: return null
            val user = memberStats.user
            val markdownName = user?.htmlUrl?.let { url -> "[$user]($url)" } ?: user?.displayName ?: user?.login ?: "Unknown"
            if (totalOnlyBoolean) {
                val totalsForMember = memberStats.totalsBetween(sinceInstant, untilInstant)
                """
                  |## Totals for $markdownName $betweenString:
                  |Commits: ${totalsForMember.commits}, Additions: ${totalsForMember.additions}, Deletions: ${totalsForMember.deletions}
                """.trimMargin()
            } else {
                val weeklyStats = memberStats.weeklyStatsBetween(sinceInstant, untilInstant).joinToString("\n\n") {
                    "${it.startOfWeek} - Commits: ${it.totals.commits}, Additions: ${it.totals.additions}, Deletions: ${it.totals.deletions}"
                }
                """
                  |## Weekly commit stats for $markdownName $betweenString:
                  |$weeklyStats
                """.trimMargin()
            }
        }
    }

    private suspend fun commitWithDiff(
        scmTeam: ScmTeam,
        repoId: RepoId,
        sha: Hash,
    ): ScmCommit? {
        val scm = Scm.fromTeam(scmTeam)
        val repo = repoStore.findById(teamId = scmTeam.id, repoId = repoId) ?: return null
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                val scmCommit = requireNotNull(api.commit(sha))
                val commitDiff = requireNotNull(api.commitDiff(sha))
                scmCommit.copy(diff = commitDiff)
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "sha" to sha.asShortString(),
                ) {
                    "ScmCommitService::commit"
                }
            }.getOrNull()
        }
    }

    private suspend fun headCommitOrNull(
        scmTeam: ScmTeam,
        repoId: RepoId,
    ): ScmCommit? {
        val scm = Scm.fromTeam(scmTeam)
        val repo = repoStore.findById(teamId = scmTeam.id, repoId = repoId) ?: return null
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                api.headCommitOrNull()
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                ) {
                    "ScmCommitService::headCommit"
                }
            }.getOrNull()
        }
    }

    private suspend fun commitsBetweenCommits(
        scmTeam: ScmTeam,
        repoId: RepoId,
        base: Hash,
        head: Hash?,
    ): List<ScmCommit> {
        val scm = Scm.fromTeam(scmTeam)
        val repo = repoStore.findById(teamId = scmTeam.id, repoId = repoId) ?: return emptyList()
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                LOGGER.debugAsync(
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "base" to base.asShortString(),
                    "head" to (head?.asShortString() ?: "HEAD"),
                ) {
                    "ScmCommitService::commitsBetweenCommits"
                }
                api.commitsBetweenCommits(
                    base = base,
                    head = head,
                    maxItems = MAX_COMMITS,
                ).toList()
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "base" to base.asShortString(),
                    "head" to (head?.asShortString() ?: "HEAD"),
                ) {
                    "ScmCommitService::commitsBetweenCommits"
                }
            }.getOrNull().orEmpty()
        }
    }

    private suspend fun commitsBetweenDates(
        scmTeam: ScmTeam,
        repoId: RepoId,
        suggestedMemberId: OrgMemberId?,
        since: String,
        until: String?,
    ): List<ScmCommit> {
        val scm = Scm.fromTeam(scmTeam)
        val repo = repoStore.findById(teamId = scmTeam.id, repoId = repoId) ?: return emptyList()
        val externalAuthorUsername = suggestedMemberId?.let { memberId ->
            val memberAndIdentities = memberStore.findByOrgMemberWithIdentity(orgMemberId = memberId)
            memberAndIdentities.firstOrNull { it.member.installationId == scmTeam.installationId }?.identity?.username
        }
        return scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                val sinceInstant = Instant.parse(since)
                val untilInstant = until?.let { Instant.parse(it) }

                LOGGER.debugSensitiveAsync(
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "since" to since,
                    "until" to (until ?: "null"),
                    sensitiveFields = mapOf(
                        "externalAuthorUsername" to (externalAuthorUsername ?: "null"),
                    ),
                ) {
                    "ScmCommitService::commitsBetweenDates"
                }
                api.commitsBetweenDates(
                    since = sinceInstant,
                    until = untilInstant,
                    authorUsername = externalAuthorUsername,
                    maxItems = MAX_COMMITS,
                ).toList()
            }.onFailure {
                LOGGER.errorSensitiveAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "since" to since,
                    "until" to (until ?: "null"),
                    sensitiveFields = mapOf(
                        "externalAuthorUsername" to (externalAuthorUsername ?: "null"),
                    ),
                ) {
                    "ScmCommitService::commitsBetweenDates"
                }
            }.getOrNull().orEmpty()
        }
    }

    @Suppress("LongMethod", "ReturnCount")
    override suspend fun getActionBuildDetails(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        suggestedActionPath: String?,
        overrideRepoAccess: RepoAccessResult?,
    ): String = withLoggingContextAsync(
        "orgId" to orgId,
        "currentOrgMemberId" to currentOrgMemberId,
        "suggestedRepoName" to suggestedRepoName,
        "suggestedActionPath" to suggestedActionPath,
        "overrideRepoAccess" to overrideRepoAccess,
    ) fn@{
        LOGGER.debugAsync {
            "ScmCommitService::getActionBuildDetails"
        }

        val (repo, scmTeam) = resolveRepoAndScmTeam(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val buildInfos = resolveBuildInfos(
            repo = repo,
            scmTeam = scmTeam,
            suggestedActionPath = suggestedActionPath,
        )

        LOGGER.debugSensitiveAsync(
            sensitiveFields = mapOf(
                "buildInfos" to buildInfos,
            ),
        ) {
            "ScmCommitService::getActionBuildDetails - retrieved pull request content"
        }

        return@fn generateDetails(
            buildInfos = buildInfos,
            pullRequestInfo = null,
        ).also {
            LOGGER.debugSensitiveAsync(
                sensitiveFields = mapOf(
                    "buildInfos" to buildInfos,
                    "details" to it,
                ),
            ) {
                "ScmCommitService::getActionBuildDetails - details generated"
            }
        }
    }

    private fun generateDetails(
        buildInfos: List<BuildInfo>? = null,
        pullRequestInfo: PullRequestInfo? = null,
    ): String {
        if (buildInfos == null) {
            return """
                |## Build Details
                |
                |Build: The system cannot access the build information
                |
                |${pullRequestInfo.asString()}
            """.trimMargin()
        }

        if (buildInfos.isEmpty()) {
            return """
                |## Build Details
                |
                |Build: No build was executed
                |
                |${pullRequestInfo.asString()}
            """.trimMargin()
        }

        return """
            |## Build Details
            |
            |Use this information when the user asks a question that requires builds details. When available, you can use the PR details and related source files to triage and suggest a fix
            |
            |You should favour the information in the job logs over other information you have been provided to answer the user's question.
            |
            |You may use the URL field to provide markdown links to the pull request job log in your response in the form [link text](FULL url from the pull request's URL job log field).
            |
            |${buildInfos.asString()}
            |${pullRequestInfo.asString()}
            |
            """.trimMargin()
    }

    @Suppress("LongMethod", "ReturnCount")
    override suspend fun getPullRequestBuildDetails(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        pullRequestNumber: Int,
        overrideRepoAccess: RepoAccessResult?,
    ): String = withLoggingContextAsync(
        "orgId" to orgId,
        "currentOrgMemberId" to currentOrgMemberId,
        "suggestedRepoName" to suggestedRepoName,
        "pullRequestNumber" to pullRequestNumber,
        "overrideRepoAccess" to overrideRepoAccess,
    ) fn@{
        LOGGER.debugAsync {
            "ScmCommitService::getPullRequestBuildDetails"
        }

        val (repo, scmTeam) = resolveRepoAndScmTeam(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val buildInfos = resolveBuildInfosByPullRequest(
            repo = repo,
            scmTeam = scmTeam,
            pullRequestNumber = pullRequestNumber,
        )

        LOGGER.debugSensitiveAsync(
            sensitiveFields = mapOf(
                "builds" to buildInfos,
            ),
        ) {
            "ScmCommitService::getPullRequestBuildDetails - retrieved build infos"
        }

        val pullRequestInfo = resolvePullRequestInfo(
            repo = repo,
            scmTeam = scmTeam,
            pullRequestNumber = pullRequestNumber,
        )

        LOGGER.debugSensitiveAsync(
            sensitiveFields = mapOf(
                "pullRequest" to pullRequestInfo,
            ),
        ) {
            "ScmCommitService::getPullRequestBuildDetails - retrieved pull request content"
        }

        return@fn generateDetails(
            buildInfos = buildInfos,
            pullRequestInfo = pullRequestInfo,
        ).also {
            LOGGER.debugSensitiveAsync(
                sensitiveFields = mapOf(
                    "pullRequest" to pullRequestInfo,
                    "buildInfos" to buildInfos,
                    "details" to it,
                ),
            ) {
                "ScmCommitService::getPullRequestBuildDetails - details generated"
            }
        }
    }

    private suspend fun resolvePullRequestInfo(
        repo: Repo,
        scmTeam: ScmTeam,
        pullRequestNumber: Int,
        pullRequestUrl: Url? = null,
    ): PullRequestInfo {
        return resolvePullRequestFromDatabase(
            repo,
            scmTeam,
            pullRequestNumber,
        )
            ?: PullRequestInfo.NumberAndUrl(
                pullRequestNumber = pullRequestNumber,
                pullRequestUrl = pullRequestUrl,
            )
    }

    private suspend fun resolvePullRequestFromDatabase(
        repo: Repo,
        scmTeam: ScmTeam,
        pullRequestNumber: Int,
    ): PullRequestInfo? {
        val pullRequest = pullRequestStore.findByRepoAndNumber(
            repoId = repo.id,
            number = pullRequestNumber,
        )?.asPullRequestMetadata() ?: runSuspendCatching {
            scmRepoApiFactory.getApiFromRepo(
                scmTeam = scmTeam,
                repo = repo,
                scm = Scm.fromTeam(scmTeam),
            ).pullRequest(pullRequestNumber).asPullRequestMetadata(
                scmTeam = scmTeam,
            )
        }.onFailure {
            LOGGER.errorAsync(it) { "ScmCommitService::resolvePullRequestFromDatabase" }
        }.getOrNull() ?: return null

        val authorMap = pullRequest.creatorId?.let {
            memberStore.getOrgMemberAndIdentity(
                orgId = scmTeam.orgId,
                memberId = pullRequest.creatorId,
            )?.let {
                mapOf(it.member.id to (it.identity.displayName ?: it.identity.username))
            }
        } ?: emptyMap()

        val contents = getPullRequestDiffOrSummary(
            scmTeam = scmTeam,
            pullRequest = pullRequest,
            repo = repo,
        )

        return PullRequestInfo.MetadataAndDiff(
            pullRequest = pullRequest,
            authorDisplayName = authorMap[pullRequest.creatorId],
            contents = contents,
        )
    }

    override suspend fun getFileContents(
        orgId: OrgId,
        currentOrgMemberId: OrgMemberId,
        suggestedRepoName: String?,
        path: String,
        ref: String?,
        lineRange: LineRange?,
        overrideRepoAccess: RepoAccessResult?,
    ): String? {
        val (repo, scmTeam) = resolveRepoAndScmTeam(
            orgId = orgId,
            orgMemberId = currentOrgMemberId,
            suggestedRepoName = suggestedRepoName,
        )

        val scm = Scm.fromTeam(scmTeam)

        val fileContents = scmRepoApiFactory.getApiFromRepo(
            scmTeam = scmTeam,
            repo = repo,
            scm = scm,
        ).use { api ->
            runSuspendCatching {
                api.fileContents(
                    path = path,
                    ref = ref,
                )
            }.onFailure {
                LOGGER.errorAsync(
                    it,
                    "teamId" to scmTeam.id,
                    "repoId" to repo.id,
                    "path" to path,
                    "ref" to ref,
                ) {
                    "ScmCommitService::getFileContents"
                }
            }.getOrNull()
        } ?: return null

        return fileContents
            .lineSequenceNumbered()
            .linesInRange(
                lineRange = lineRange,
                padding = CommitService.FILE_LINE_RANGE_WINDOW,
            )
            .joinToString("\n")
            .take(CommitService.MAX_FILE_HUNK_SIZE)
    }
}

private fun PullRequestMetadata.descriptionAndContents(): List<String> {
    return listOfNotNull(
        description,
        summary,
    )
}

private fun PullRequestMetadata.metadata(authorDisplayName: String? = null): List<String> =
    listOfNotNull(
        "PR title: $title",
        "PR number: $number",
        "PR web URL: $htmlUrl",
        "PR state: ${state.name}",
        mergedAt?.let {
            "PR merged at: ${it.asPSTDayMonthYear}"
        },
        "PR created at: ${createdAt.asPSTDayMonthYear}",
        authorDisplayName?.let { "PR author: $authorDisplayName" },
        description?.let { "PR description: $it" },
        summary?.let { "PR Summary: $it" },
    )

private fun CiExecutionLegacy.metadata() = """
    |BUILD id: $id
    |BUILD url: ${htmlUrl?.asString}
    |BUILD status: $status
    |BUILD result: $result
    |BUILD started at: $startedAt
    |BUILD completed at: $completedAt
    |BUILD last updated at: $lastUpdatedAt
""".trimMargin()

private interface PullRequestInfo {

    val number: Int
    val contents: String?

    fun asString(): String

    data class MetadataAndDiff(
        val pullRequest: PullRequestMetadata,
        val authorDisplayName: String?,
        override val contents: String,
    ) : PullRequestInfo {
        override val number get() = pullRequest.number

        override fun asString() = """
            |${pullRequest.metadata(authorDisplayName).joinToString("\n")}
            |PR DIFF IN TRIPLE QUOTES:
            |${"\"\"\""}
            |${contents.trim()}
            |${"\"\"\""}
        """.trimMargin()
    }

    data class NumberAndUrl(
        val pullRequestNumber: Int,
        val pullRequestUrl: Url?,
    ) : PullRequestInfo {
        override val number get() = pullRequestNumber

        override val contents get() = null

        override fun asString() = """
            |PR number: $pullRequestNumber
            |PR web url: ${pullRequestUrl?.asString}
        """.trimMargin()
    }
}

private fun PullRequestInfo?.asString() = this?.asString() ?: ""

private data class BuildInfo(
    val build: CiExecutionLegacy,
    val summary: String,
) {
    fun asString() = """
       |${build.metadata()}
       |BUILD SUMMARY IN TRIPLE QUOTES:
       |${"\"\"\""}
       |${summary.trim()}
       |${"\"\"\""}
    """.trimMargin()
}

private fun List<BuildInfo>.asString() = joinToString("\n\n") { it.asString() }

private data class PullRequestMetadata(
    val number: Int,
    val title: String,
    val htmlUrl: Url,
    val creatorId: MemberId?,
    val state: PullRequestState,
    val createdAt: Instant,
    val mergedAt: Instant?,
    val description: String?,
    val summary: String?,
)

private suspend fun ScmPullRequest.asPullRequestMetadata(
    scmTeam: ScmTeam,
    memberStore: MemberStore = Stores.memberStore,
): PullRequestMetadata {
    val member = user?.let {
        memberStore.findByExternalId(
            orgId = scmTeam.orgId,
            provider = scmTeam.provider,
            externalId = it.externalId,
            externalTeamId = scmTeam.providerExternalId,
        )
    }
    return PullRequestMetadata(
        number = number,
        title = title,
        htmlUrl = htmlUrl,
        creatorId = member?.id,
        state = state,
        createdAt = createdAt,
        mergedAt = mergedAt,
        description = body,
        summary = null,
    )
}

private fun PullRequest.asPullRequestMetadata() = PullRequestMetadata(
    number = number,
    title = title,
    htmlUrl = htmlUrl,
    creatorId = creatorId,
    state = state,
    createdAt = createdAt,
    mergedAt = mergedAt,
    description = description?.asMessageBody()?.asMarkdown(),
    summary = summary,
)
