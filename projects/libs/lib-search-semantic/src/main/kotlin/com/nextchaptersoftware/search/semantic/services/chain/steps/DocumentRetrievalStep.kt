package com.nextchaptersoftware.search.semantic.services.chain.steps

import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.db.models.AnswerDepthPreference
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.log.sensitive.debugSensitiveAsync
import com.nextchaptersoftware.log.sensitive.withSensitiveLoggingContextAsync
import com.nextchaptersoftware.ml.prompt.input.asConversationMessageData
import com.nextchaptersoftware.ml.query.context.DocumentContext
import com.nextchaptersoftware.ml.query.context.DocumentQueryContext
import com.nextchaptersoftware.ml.query.context.QueryContext
import com.nextchaptersoftware.models.serializedHighlightedCode
import com.nextchaptersoftware.models.serializedVisibleCode
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.chain.ChainExecutionContext
import com.nextchaptersoftware.search.semantic.services.chain.InstrumentedChainStep
import com.nextchaptersoftware.search.semantic.services.chain.addStepMetrics
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.semantic.services.retrieval.WideSlackEmbeddingsRetrievalStyle
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.DemoFilter
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.PostRetrievalFilter
import com.nextchaptersoftware.search.semantic.services.retrieval.filters.SlackOriginThreadFilter
import com.nextchaptersoftware.search.semantic.services.retrieval.promotion.DemoPromoter
import com.nextchaptersoftware.search.semantic.services.retrieval.promotion.DocumentPromotionCondition
import com.nextchaptersoftware.utils.Base64.base64Decode
import kotlin.time.Duration
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class DocumentRetrievalStep(
    private val semanticDocumentRetriever: SemanticDocumentRetriever,
    private val documentEvaluationRetriever: DocumentEvaluationRetriever,
    private val documentRetrievalTimeout: Duration,
    private val useAgenticRetrievalOverride: Boolean? = null,
    private val useShallowRetrievalWhenDsacOff: Boolean = false,
    private val postRetrievalFilters: List<PostRetrievalFilter> = emptyList(),
    private val loadingStateUpdater: suspend (String) -> Unit = {},
) : InstrumentedChainStep<QueryContext, DocumentQueryContext, DocumentContext> {

    companion object {
        private const val NCS_ORG_ID = "2f6b3875-16b7-40d4-8ab4-9ccb6f5ce417"
        private val MERMAID_QUERY = "c2FtbCBhdXRoIGZsb3c=".base64Decode()
        private const val LESS_DEPTH_DOC_SIZE = 30
    }

    @Suppress("CyclomaticComplexMethod", "LongMethod")
    override suspend fun execute(context: ChainExecutionContext<QueryContext>, input: DocumentQueryContext): DocumentContext {
        val inputContext = context.inputContext
        val template = inputContext.queryTemplate

        val orgId = inputContext.org.id
        val orgMemberId = inputContext.questioner.orgMemberAndPerson.orgMember.id

        val (docs, mlFunctionOutput) = withSensitiveLoggingContextAsync(
            "orgId" to orgId,
            sensitiveFields = mapOf(
                "query" to input.documentQueryText,
            ),
        ) {
            val wideSlackEmbeddingsRetrievalStyle = when {
                inputContext.isDemoMode -> WideSlackEmbeddingsRetrievalStyle.Never
                inputContext.disableWideSlackEmbeddings -> WideSlackEmbeddingsRetrievalStyle.Never
                else -> WideSlackEmbeddingsRetrievalStyle.Always
            }

            val depth = inputContext.answerPreferences?.depth ?: inputContext.questioner.orgMemberAndPerson.orgMember.answerPreferences.depth

            val userAgenticRetrievalOverride = when (depth) {
                AnswerDepthPreference.DefaultDepth, AnswerDepthPreference.LessDepth -> null
                AnswerDepthPreference.MoreDepth -> true
            }

            val maxDocuments = when (depth) {
                AnswerDepthPreference.DefaultDepth, AnswerDepthPreference.MoreDepth -> template.maxDocuments
                AnswerDepthPreference.LessDepth -> LESS_DEPTH_DOC_SIZE
            }

            val useAgenticRetrieval = useAgenticRetrievalOverride ?: userAgenticRetrievalOverride ?: template.useAgenticRetrieval
            val dataPresetConfiguration = when (inputContext.isDemoMode) {
                false -> inputContext.dataSourcePresetConfiguration
                true -> DataSourcePresetConfiguration.All
            }

            val slackOriginThreadFilter = inputContext.callingSlackThreadTs?.let {
                SlackOriginThreadFilter(it)
            }

            suspend fun semanticRetrieval(
                promotionConditions: List<DocumentPromotionCondition>,
                additionalPostRetrievalFilters: List<PostRetrievalFilter> = emptyList(),
            ): List<MLTypedDocument> {
                val allFilters = postRetrievalFilters + additionalPostRetrievalFilters
                return semanticDocumentRetriever.doRetrieval(
                    orgId = orgId,
                    orgMemberId = orgMemberId,
                    documentQuery = input.documentQuery,
                    template = template,
                    maxDocuments = maxDocuments,
                    documentTimeout = documentRetrievalTimeout,
                    exclusiveRepoSet = input.exclusiveRepos,
                    nonExclusiveRepoSet = input.nonExclusiveRepos,
                    dsacContext = inputContext.dsacContext,
                    useShallowRetrievalWhenDsacOff = useShallowRetrievalWhenDsacOff,
                    dataSourcePresetConfiguration = dataPresetConfiguration,
                    wideSlackEmbeddingsRetrievalStyle = wideSlackEmbeddingsRetrievalStyle,
                    postRetrievalFilters = allFilters,
                    documentPromotionConditions = promotionConditions,
                )
            }

            suspend fun evalRetrieval(
                additionalPostRetrievalFilters: List<PostRetrievalFilter> = emptyList(),
            ): Pair<List<MLTypedDocument>, Set<String>> {
                val maxDepthOverride = when {
                    useAgenticRetrieval -> null
                    else -> 0
                }

                LOGGER.debugSensitiveAsync(
                    "orgId" to orgId,
                    sensitiveFields = mapOf(
                        "query" to input.documentQueryText,
                    ),
                ) {
                    "New document retrieval with ML functions"
                }
                val priorMessages = inputContext.priorMessages?.messages?.mapNotNull {
                    it.asConversationMessageData(inputContext.priorMessages?.botOrgMemberId)
                }

                val allFilters = postRetrievalFilters + additionalPostRetrievalFilters

                return documentEvaluationRetriever.retrieve(
                    org = inputContext.org,
                    questioner = inputContext.questioner,
                    userQuery = inputContext.query,
                    documentQuery = input.documentQuery,
                    maxDocuments = maxDocuments,
                    maxDepthOverride = maxDepthOverride,
                    searchTemplate = template,
                    priorMessages = priorMessages,
                    highlightedCode = inputContext.localContext?.serializedHighlightedCode(),
                    visibleCode = inputContext.localContext?.serializedVisibleCode(),
                    documentTimeout = documentRetrievalTimeout,
                    dsacContext = inputContext.dsacContext,
                    exclusiveRepoSet = input.exclusiveRepos,
                    nonExclusiveRepoSet = input.nonExclusiveRepos,
                    wideSlackEmbeddingsRetrievalStyle = wideSlackEmbeddingsRetrievalStyle,
                    loadingStateUpdater = loadingStateUpdater,
                    postRetrievalFilters = allFilters,
                    slackChannelId = inputContext.callingSlackChannelId,
                    slackThreadTs = inputContext.callingSlackThreadTs,
                    slackTs = inputContext.callingSlackTs,
                    dataSourcePresetConfiguration = dataPresetConfiguration,
                    metricsCollector = { metrics ->
                        context.addStepMetrics(metrics)
                    },
                )
            }

            when {
                inputContext.isDemoMode -> {
                   Pair(
                       first = semanticRetrieval(
                           promotionConditions = listOf(DemoPromoter()),
                           additionalPostRetrievalFilters = listOfNotNull(DemoFilter(), slackOriginThreadFilter),
                       ),
                       second = emptySet(),
                   )
                }

                (inputContext.query?.lowercase()?.contains(MERMAID_QUERY.lowercase()) == true && orgId.toString() == NCS_ORG_ID) -> {
                    evalRetrieval(
                        additionalPostRetrievalFilters = listOfNotNull(DemoFilter(), slackOriginThreadFilter),
                    )
                }

                else -> evalRetrieval()
            }
        }

        val demoFiltered = if (inputContext.isDemoMode || orgId.toString() == NCS_ORG_ID) {
            docs.filterNot {
                it.source.contains("infrastructure/cdk/") || it.source.contains("sandbox/") || it.source.contains("experiment/")
            }
        } else {
            docs
        }

        return DocumentContext(
            documentQueryContext = input,
            documents = demoFiltered,
            mlFunctionContent = mlFunctionOutput,
        )
    }
}
