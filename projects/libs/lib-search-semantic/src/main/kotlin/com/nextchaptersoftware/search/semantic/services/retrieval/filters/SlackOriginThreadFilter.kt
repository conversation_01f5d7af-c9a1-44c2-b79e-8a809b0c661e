package com.nextchaptersoftware.search.semantic.services.retrieval.filters

import com.nextchaptersoftware.db.models.InsightType
import com.nextchaptersoftware.db.models.MLTypedDocument
import com.nextchaptersoftware.slack.utils.SlackUrl

class SlackOriginThreadFilter(private val originatingSlackThreadTs: String?) : PostRetrievalFilter {
    override suspend fun filter(query: String, doc: MLTypedDocument): <PERSON><PERSON><PERSON> {
        // Only filter Slack thread documents if originating thread ts is present
        if (originatingSlackThreadTs.isNullOrBlank()) return true
        if (doc.sourceType != InsightType.Slack) return true
        val url = doc.externalUrl?.toString() ?: return true
        val slackUrls = SlackUrl.parseAll(url)
        // If any parsed threadTs matches the originating thread, filter it out
        return slackUrls.none { it.threadTs == originatingSlackThreadTs }
    }
}
