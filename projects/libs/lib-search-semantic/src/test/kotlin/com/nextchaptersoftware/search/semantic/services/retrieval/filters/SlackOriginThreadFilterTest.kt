package com.nextchaptersoftware.search.semantic.services.retrieval.filters

import com.nextchaptersoftware.db.MockDataClasses
import com.nextchaptersoftware.db.models.DocumentType
import com.nextchaptersoftware.db.models.InsightType
import io.ktor.http.Url
import kotlinx.coroutines.test.runTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class SlackOriginThreadFilterTest {
    private fun slackDocWithUrl(url: String) = MockDataClasses.typedDocument(
        content = "Slack message",
        source = "test",
        documentType = DocumentType.Thread,
        sourceType = InsightType.Slack,
        externalUrl = Url(url),
    )

    private fun nonSlackDoc() = MockDataClasses.typedDocument(
        content = "Not slack",
        source = "test",
        documentType = DocumentType.Thread,
        sourceType = InsightType.Documentation,
    )

    @Test
    fun `filters out slack thread doc with matching thread ts`() = runTest {
        val threadTs = "1663893300.463649"
        val url = "https://chapter2global.slack.com/archives/C02FZAJ4KUP/p1663893300463649?thread_ts=$threadTs&cid=C02FZAJ4KUP"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = slackDocWithUrl(url)
        assertThat(filter.filter("query", doc)).isFalse()
    }

    @Test
    fun `filters out slack documentation doc with matching thread ts`() = runTest {
        val threadTs = "1663893300.463649"
        val url = "https://chapter2global.slack.com/archives/C02FZAJ4KUP/p1663893300463649?thread_ts=$threadTs&cid=C02FZAJ4KUP"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = MockDataClasses.typedDocument(
            content = "Slack message as documentation",
            source = "test",
            documentType = DocumentType.Documentation,
            sourceType = InsightType.Slack,
            externalUrl = Url(url),
        )
        assertThat(filter.filter("query", doc)).isFalse()
    }

    @Test
    fun `does not filter out slack thread doc with non-matching thread ts`() = runTest {
        val threadTs = "1663893300.463649"
        val differentThreadTs = "1663893300.463650"
        val url = "https://chapter2global.slack.com/archives/C02FZAJ4KUP/p1663893300463650?thread_ts=$differentThreadTs&cid=C02FZAJ4KUP"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = slackDocWithUrl(url)
        assertThat(filter.filter("query", doc)).isTrue()
    }

    @Test
    fun `does not filter out slack documentation doc with non-matching thread ts`() = runTest {
        val threadTs = "1663893300.463649"
        val differentThreadTs = "1663893300.463650"
        val url = "https://chapter2global.slack.com/archives/C02FZAJ4KUP/p1663893300463650?thread_ts=$differentThreadTs&cid=C02FZAJ4KUP"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = MockDataClasses.typedDocument(
            content = "Slack message as documentation",
            source = "test",
            documentType = DocumentType.Documentation,
            sourceType = InsightType.Slack,
            externalUrl = Url(url),
        )
        assertThat(filter.filter("query", doc)).isTrue()
    }

    @Test
    fun `does not filter out non-slack docs`() = runTest {
        val threadTs = "1663893300.463649"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = nonSlackDoc()
        assertThat(filter.filter("query", doc)).isTrue()
    }

    @Test
    fun `does not filter slack thread doc with no external URL`() = runTest {
        val threadTs = "1663893300.463649"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = MockDataClasses.typedDocument(
            content = "Slack message",
            source = "test",
            documentType = DocumentType.Thread,
            sourceType = InsightType.Slack,
            externalUrl = null, // No external URL
        )
        assertThat(filter.filter("query", doc)).isTrue()
    }

    @Test
    fun `does not filter slack documentation doc with no external URL`() = runTest {
        val threadTs = "1663893300.463649"
        val filter = SlackOriginThreadFilter(threadTs)
        val doc = MockDataClasses.typedDocument(
            content = "Slack message as documentation",
            source = "test",
            documentType = DocumentType.Documentation,
            sourceType = InsightType.Slack,
            externalUrl = null, // No external URL
        )
        assertThat(filter.filter("query", doc)).isTrue()
    }

    @Test
    fun `does not filter if originating thread ts is null or blank`() = runTest {
        val threadTs = "1663893300.463649"
        val url = "https://chapter2global.slack.com/archives/C02FZAJ4KUP/p1663893300463649?thread_ts=$threadTs&cid=C02FZAJ4KUP"
        val doc = slackDocWithUrl(url)
        assertThat(SlackOriginThreadFilter(null).filter("query", doc)).isTrue()
        assertThat(SlackOriginThreadFilter("").filter("query", doc)).isTrue()
    }
}
