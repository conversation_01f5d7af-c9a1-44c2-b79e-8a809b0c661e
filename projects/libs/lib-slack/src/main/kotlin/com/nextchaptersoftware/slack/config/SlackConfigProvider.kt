package com.nextchaptersoftware.slack.config

import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.SlackConfig
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.SlackSettingsModel.slackUserAcceptanceTesting
import com.nextchaptersoftware.db.stores.SlackSettingsStore
import com.nextchaptersoftware.db.stores.Stores

class SlackConfigProvider(
    private val config: SlackConfig = GlobalConfig.INSTANCE.providers.slack,
    private val uatConfig: SlackConfig? = GlobalConfig.INSTANCE.providers.slackUAT,
    private val slackSettingsStore: SlackSettingsStore = Stores.slackSettingsStore,
) {
    /**
     * For Slack App Approval, we need to allow for an org to specify whether
     * Slack App Installation should be using a custom Unblocked app with additional scopes/events.
     *
     * To do that, we have a setting called [slackUserAcceptanceTesting].
     * In that case, we use a special configuration that binds the slack for that org to a UAT-specific app.
     */
    suspend fun getSlackConfig(orgId: OrgId): SlackConfig {
        val slackUserAcceptanceTest = slackSettingsStore.getSlackUserAcceptanceTesting(orgId = orgId)
        return if (slackUserAcceptanceTest == true) {
            checkNotNull(uatConfig)
        } else {
            config
        }
    }
}
