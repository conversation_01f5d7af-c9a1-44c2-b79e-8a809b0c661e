package com.nextchaptersoftware.api.services

import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.identityStore
import com.nextchaptersoftware.db.stores.Stores.personStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.notification.services.InactiveFollowupService
import com.nextchaptersoftware.search.semantic.services.samplequestions.SampleQuestionNotifier
import com.nextchaptersoftware.segment.SegmentProvider
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.types.EmailAddress
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.never
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.times

class PersonUpsertServiceTest : DatabaseTestsBase() {

    private val emailService = mock<EmailService>()
    private val inactiveFollowupService = mock<InactiveFollowupService>()
    private val sampleQuestionNotifier = mock<SampleQuestionNotifier>()
    private val segmentProvider = mock<SegmentProvider>()
    private val slackNotifier = mock<SlackNotifier>()
    private val service = PersonUpsertService(
        emailService = emailService,
        inactiveFollowupService = inactiveFollowupService,
        sampleQuestionNotifier = sampleQuestionNotifier,
        segmentProvider = segmentProvider,
        slackNotifier = slackNotifier,
    )

    @Test
    fun `creates a new person`() = suspendingDatabaseTest {
        makePerson(primaryEmail = EmailAddress.random()).also {
            makeIdentity(person = it, provider = Provider.Bitbucket)
        }.idValue

        val identity = makeIdentity(
            person = null,
            primaryEmail = EmailAddress.of("<EMAIL>"),
            emails = listOf(EmailAddress.of("<EMAIL>")),
        ).asDataModel()
        assertThat(identity.person).isNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = null)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
        }

        verify(slackNotifier, times(1)).announceNewPerson(personId = eq(upsertedPersonId), identityId = eq(identity.id))
    }

    @Test
    fun `assigns person from currently signed in person`() = suspendingDatabaseTest {
        val currentlySignedInPerson = makePerson().also { makeIdentity(person = it, provider = Provider.Bitbucket) }.idValue
        val identity = makeIdentity(person = null, provider = Provider.GitHub, primaryEmail = EmailAddress.of("<EMAIL>")).asDataModel()
        assertThat(identity.person).isNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = currentlySignedInPerson)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
            assertThat(currentlySignedInPerson).isEqualTo(upsertedPersonId)
        }

        verify(slackNotifier, never()).announceNewPerson(personId = any(), identityId = any())
    }

    @Test
    fun `assigns person from person matching identity email`() = suspendingDatabaseTest {
        val email = EmailAddress.random()
        val otherPersonId = makePerson(primaryEmail = email).also {
            makeIdentity(person = it, provider = Provider.Bitbucket, primaryEmail = email, emails = listOf(email)).asDataModel()
        }.idValue

        val identity = makeIdentity(person = null, provider = Provider.GitHub, primaryEmail = email).asDataModel()
        assertThat(identity.person).isNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = null)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
            assertThat(otherPersonId).isEqualTo(upsertedPersonId)
        }

        verify(slackNotifier, never()).announceNewPerson(personId = any(), identityId = any())
    }

    @Test
    fun `no-op if identity already has a person`() = suspendingDatabaseTest {
        val person = makePerson()
        val email = EmailAddress.random()
        val identity = makeIdentity(person = person, primaryEmail = email, emails = listOf(email)).asDataModel()
        assertThat(identity.person).isNotNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = null)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
            assertThat(person.idValue).isEqualTo(upsertedPersonId)
        }

        verify(slackNotifier, never()).announceNewPerson(personId = any(), identityId = any())
    }

    @Test
    fun `no-op if identity already has a person and is signed in as same person`() = suspendingDatabaseTest {
        val person = makePerson()
        val identity = makeIdentity(
            person = person,
            primaryEmail = EmailAddress.of("<EMAIL>"),
            emails = listOf(EmailAddress.of("<EMAIL>")),
        ).asDataModel()
        assertThat(identity.person).isNotNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = person.idValue)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
            assertThat(person.idValue).isEqualTo(upsertedPersonId)
        }

        verify(slackNotifier, never()).announceNewPerson(personId = any(), identityId = any())
    }

    @Test
    fun `merges if identity already has a person and is signed in as different person`() = suspendingDatabaseTest {
        val currentlySignedInPerson = makePerson().also { makeIdentity(person = it, provider = Provider.Bitbucket) }.idValue
        val person = makePerson()
        val identity = makeIdentity(
            person = person,
            primaryEmail = EmailAddress.of("<EMAIL>"),
            emails = listOf(EmailAddress.of("<EMAIL>")),
        ).asDataModel()
        assertThat(identity.person).isNotNull()

        val upsertedPersonId = service.upsertPerson(identity = identity, currentlySignedInPersonId = currentlySignedInPerson)

        identityStore.findById(identityId = identity.id).also {
            assertThat(it?.person).isEqualTo(upsertedPersonId)
            assertThat(currentlySignedInPerson).isEqualTo(upsertedPersonId)
        }

        personStore.findById(id = person.idValue).also {
            assertThat(it).isNull()
        }

        verify(slackNotifier, never()).announceNewPerson(personId = any(), identityId = any())
    }
}
