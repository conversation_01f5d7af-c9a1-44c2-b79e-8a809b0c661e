package com.nextchaptersoftware.api.services.install

import com.nextchaptersoftware.api.models.InstallationV2
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.cache.LocalOptionalReadThroughCache
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.TeamSettingsStore
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import kotlin.time.Duration.Companion.minutes
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ScmInstallationService(
    private val scmInstallationDelegate: ScmInstallationDelegateInterface,
    private val scmNoAuthApiFactory: ScmNoAuthApiFactory,
    private val scmWebFactory: ScmWebFactory,
    private val teamSettingsStore: TeamSettingsStore = Stores.teamSettingsStore,
) {

    companion object {
        /**
         * Accounts rarely change, so we cache them for a long time.
         * This is especially important because we are using a no-auth API, which has low rate limits.
         */
        private val CACHED_ACCOUNTS = LocalOptionalReadThroughCache<String, ScmAccount>(
            writeTimeout = 10.minutes,
        )
    }

    suspend fun getAuthorizedInstallations(scm: Scm, identity: Identity): List<ScmInstallationAccount> {
        if (!identity.provider.isScmProvider || scm != Scm.fromIdentity(identity)) {
            // FIXME: currently spewing this warning
            // https://app.logz.io/#/explore/goto/************************************?switchToAccountId=411850
            LOGGER.warnAsync(
                "identityId" to identity.id,
                "scm" to scm.uniqueSignature,
            ) { "Identity SCM does not match expected SCM" }
            return emptyList()
        }

        return runSuspendCatching {
            scmInstallationDelegate.getAuthorizedInstallations(scm = scm, identity = identity)
        }.onFailure {
            LOGGER.warnAsync(
                t = it,
                "identityId" to identity.id,
                "scm" to scm.uniqueSignature,
            ) { "Failed to get authorized installations" }
        }.getOrElse {
            emptyList()
        }
    }

    suspend fun getScmTeamInstallation(scmTeam: ScmTeam): ScmInstallationAccount {
        return runSuspendCatching {
            scmInstallationDelegate.getScmTeamInstallation(scmTeam = scmTeam)
        }.onFailure {
            LOGGER.warnAsync(
                t = it,
                "scmTeamId" to scmTeam.id,
            ) { "Failed to get scmTeam installation account" }
        }.getOrNull()
            ?: throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = """${scmTeam.provider.displayName} account not available""",
                detail = "The ${scmTeam.provider.displayName} account is currently not available and appears to be offline.",
                url = null,
                reasonType = null,
            )
    }

    /**
     * Get the publicly accessible account for the given owner login.
     * This can be either an org or a user account.
     * This does not require authentication.
     */
    suspend fun getPublicInstallation(scm: Scm, ownerLogin: String): ScmInstallationAccount? {
        val account = getOrg(scm, ownerLogin) ?: getUser(scm, ownerLogin)
        return account?.let {
            ScmInstallationAccount(
                account = account,
                // We have no idea what the installation ID is when not-authorized
                externalInstallationId = it.externalId,
                // We have no idea what the allReposAccessible is when not-authorized
                allReposAccessible = true,
                // We have no idea what the CI status is when not-authorized
                supportsCI = false,
            )
        }
    }

    private suspend fun getOrg(scm: Scm, orgName: String): ScmAccount? {
        val key = listOf("ORG", scm.uniqueSignature, orgName).joinToString("|")
        return CACHED_ACCOUNTS.getOrCompute(key) {
            val api = scmNoAuthApiFactory.from(orgId = null, scm = scm)
            runSuspendCatching {
                api.getOrgByLogin(orgName).let { ScmAccount.Org(it) }
            }.getOrElse {
                LOGGER.warnAsync(
                    it,
                    "scm" to scm.uniqueSignature,
                    "provider" to scm.provider.displayName,
                    "orgName" to orgName,
                ) { "Could not find SCM org" }
                null
            }
        }
    }

    private suspend fun getUser(scm: Scm, userName: String): ScmAccount? {
        val key = listOf("USER", scm.uniqueSignature, userName).joinToString("|")
        return CACHED_ACCOUNTS.getOrCompute(key) {
            val api = scmNoAuthApiFactory.from(orgId = null, scm = scm)
            runSuspendCatching {
                api.getUserByLogin(userName).let { ScmAccount.User(it) }
            }.getOrElse {
                LOGGER.warnAsync(
                    it,
                    "scm" to scm.uniqueSignature,
                    "provider" to scm.provider.displayName,
                    "orgName" to userName,
                ) { "Could not find SCM user" }
                null
            }
        }
    }

    suspend fun getGeneralInstallUrl(scm: Scm): Url? {
        return scmWebFactory.from(scm).appInstallGeneralUrl
    }

    suspend fun getInstallUrl(scm: Scm, externalId: String): Url? {
        return scmWebFactory.from(scm).getAppInstallTargetedUrl(externalId)
    }

    suspend fun getInstallationFromScm(installationAccount: ScmInstallationAccount, scm: Scm): InstallationV2 {
        val account = installationAccount.account

        // The autoSelectNewSourceRepos flag is not set yet for the team, since the team does not exist.
        // The purpose of this flag is to indicate what the default value should be when the installation/team is created.
        val autoSelectNewSourceRepos = true

        return InstallationV2(
            allowAllRepos = autoSelectNewSourceRepos,
            avatarUrl = account.avatarUrl.asString,
            displayName = account.displayName ?: account.login,
            enterpriseProviderId = scm.providerEnterpriseId?.value,
            fullPath = account.fullPath,
            htmlUrl = account.htmlUrl.asString,
            installUrl = getInstallUrl(scm, account.externalId)?.asString,
            installationId = FullyQualifiedInstallationId(scm = scm, installationId = account.externalId).asString,
            isInstalled = false,
            isPersonalAccount = account is ScmAccount.User,
            provider = scm.provider.asApiModel(),
            teamId = null,
            allReposAccessible = installationAccount.allReposAccessible,
        )
    }

    suspend fun getInstallationFromTeam(scmTeam: ScmTeam, allReposAccessible: Boolean): InstallationV2 {
        val scm = Scm.fromTeam(scmTeam)
        val autoSelectNewSourceRepos = teamSettingsStore.getAutoSelectNewSourceRepos(scmTeam.id)

        return InstallationV2(
            allowAllRepos = autoSelectNewSourceRepos,
            avatarUrl = scmTeam.providerAvatarUrl.asString,
            displayName = scmTeam.displayName,
            enterpriseProviderId = scmTeam.providerEnterpriseId?.value,
            fullPath = scmTeam.providerLogin,
            htmlUrl = scmTeam.providerHtmlUrl.asString,
            installUrl = getInstallUrl(scm, scmTeam.providerExternalId)?.toString(),
            installationId = FullyQualifiedInstallationId(scm, scmTeam.providerExternalId).asString,
            isInstalled = scmTeam.isScmInstalled,
            isPersonalAccount = scmTeam.providerIsPersonalAccount,
            provider = scmTeam.provider.asApiModel(),
            teamId = scmTeam.orgId.value,
            allReposAccessible = allReposAccessible,
        )
    }

    suspend fun refreshTeamResourcesIfUninitialized(
        scmTeam: ScmTeam,
    ) {
        if (!scmTeam.hasProviderInitialized) {
            refreshTeamResources(scmTeam)
        }
    }

    suspend fun refreshTeamResources(
        scmTeam: ScmTeam,
    ) {
        scmInstallationDelegate.refreshTeamResources(
            scmTeam = scmTeam,
        )
    }
}
