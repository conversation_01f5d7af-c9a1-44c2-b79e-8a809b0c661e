@file:Suppress("ktlint:nextchaptersoftware:no-dao-argument")

package com.nextchaptersoftware.pr.ingestion

import com.nextchaptersoftware.common.model.Message
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Member
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.MessageModel
import com.nextchaptersoftware.db.models.OrgMember
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequest
import com.nextchaptersoftware.db.models.PullRequestCommentDAO
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestReviewDAO
import com.nextchaptersoftware.db.models.PullRequestReviewId
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.SourceMarkDAO
import com.nextchaptersoftware.db.models.SourceMarkModel
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.dashboardUrl
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.PullRequestCommentStore
import com.nextchaptersoftware.db.stores.PullRequestReviewStore
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.SourceMarkStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.errorSync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.markdown.MessageBodyConverter.asMarkdown
import com.nextchaptersoftware.pr.ingestion.signature.UnblockedPRCommentDecoratorProvider
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.ServerResponseException
import io.ktor.http.HttpStatusCode
import mu.KotlinLogging
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

private val LOGGER = KotlinLogging.logger {}

class UnblockedPRCommentService(
    private val scmUserApiFactory: ScmUserApiFactory,
    private val messageStore: MessageStore = Stores.messageStore,
    private val reviewStore: PullRequestReviewStore = Stores.pullRequestReviewStore,
    private val commentStore: PullRequestCommentStore = Stores.pullRequestCommentStore,
    private val pullRequestStore: PullRequestStore = Stores.pullRequestStore,
    private val sourceMarkStore: SourceMarkStore = Stores.sourceMarkStore,
    private val urlBuilderProvider: UrlBuilderProvider,
    private val commentDecoratorProvider: UnblockedPRCommentDecoratorProvider,
) {
    suspend fun handlePullRequest(
        pullRequestId: PullRequestId,
    ) {
        LOGGER.debugAsync("pullRequestId" to pullRequestId) { "Processing pull request" }

        val pullRequest = suspendedTransaction { PullRequestDAO.findById(pullRequestId) }
        if (pullRequest == null) {
            LOGGER.errorAsync("pullRequestId" to pullRequestId) { "Unknown pull request" }
            return
        }

        updatePullRequest(pullRequest)
    }

    suspend fun handleComment(
        commentId: PullRequestCommentId,
    ) {
        LOGGER.debugAsync("commentId" to commentId) { "Processing comment" }

        val comment = suspendedTransaction { PullRequestCommentDAO.findById(commentId) }
        if (comment == null) {
            LOGGER.errorAsync("commentId" to commentId) { "Unknown comment" }
            return
        }

        when {
            comment.isDeleted -> deleteIssueComment(comment)
            comment.prCommentId != null -> updateIssueComment(comment)
            else -> createIssueComment(comment)
        }
    }

    suspend fun handleReview(
        reviewId: PullRequestReviewId,
    ) {
        LOGGER.debugAsync("reviewId" to reviewId) { "Processing review" }

        val review = suspendedTransaction { PullRequestReviewDAO.findById(reviewId) }
        if (review == null) {
            LOGGER.errorAsync("reviewId" to reviewId) { "Unknown review" }
            return
        }

        updateReview(review)
    }

    suspend fun handle(
        messageId: MessageId,
    ) {
        LOGGER.debugAsync("messageId" to messageId) { "Processing message" }

        val message = suspendedTransaction { MessageDAO.findById(messageId) }
        if (message == null) {
            LOGGER.errorAsync("messageId" to messageId) { "Unknown message" }
            return
        }

        when {
            message.isDeleted -> {
                when {
                message.isGitHubTopLevelComment -> deleteIssueComment(message)
                else -> deleteReviewComment(message)
            }
            }

            message.prCommentId != null -> {
                when {
                message.isGitHubTopLevelComment -> updateIssueComment(message)
                else -> updateReviewComment(message)
            }
            }

            else -> {
                val shouldCreateIssueComment = suspendedTransaction {
                    val thread = message.thread
                    val provider = thread.repo?.provider ?: error("No provider for PR")
                    provider.hasIssueComments && SourceMarkDAO.count(SourceMarkModel.thread eq thread.id) <= 0
                }
                when (shouldCreateIssueComment) {
                    true -> createIssueComment(message)
                    else -> createReviewComment(message)
                }
            }
        }
    }

    private fun ByteArray.asMessageBody(): Message.MessageBody? {
        return runSuspendCatching {
            Message.MessageBody.parseFrom(this)
        }.getOrElse {
            LOGGER.errorSync(it) { "Message content could not be parsed" }
            null
        }
    }

    private suspend fun syncToGitHub(
        scmTeam: ScmTeam,
        repo: Repo,
        author: Member,
        block: suspend (ScmUserApi) -> Unit,
    ) = withLoggingContextAsync(
        "teamId" to scmTeam.id,
        "repoId" to repo.id,
        "authorId" to author.id,
    ) {
        if (!scmTeam.isScmInstalled) {
            LOGGER.errorAsync { "Team is not SCM connected" }
            return@withLoggingContextAsync null
        }

        if (!repo.isScmConnected) {
            LOGGER.errorAsync { "Repo is not SCM connected" }
            return@withLoggingContextAsync null
        }

        scmUserApiFactory.getApiFromIdentity(orgId = scmTeam.orgId, identityId = author.identityId, scm = Scm.fromTeam(scmTeam)).use {
            runSuspendCatching {
                block(it)
            }.getOrElse {
                LOGGER.errorAsync(it) { "Failed to sync to GitHub" }

                when (it) {
                    is ServerResponseException -> throw it

                    // retry server exceptions, usually transient
                    is ClientRequestException -> when (it.response.status) {
                        HttpStatusCode.NotFound -> {}

                        // swallow 404s to avoid retrying
                        else -> {
                            throw it
                        } // retry everything else
                    }

                    else -> throw it // retry unexpected exceptions
                }
            }
        }
    }

    private suspend fun createIssueComment(
        pullRequestComment: PullRequestCommentDAO,
    ) = withLoggingContextAsync(
        "commentId" to pullRequestComment.id.value,
    ) {
        if (pullRequestComment.prCommentId != null) {
            LOGGER.warnAsync { "Comment already created in GitHub" }
            return@withLoggingContextAsync
        }

        val commentBody = pullRequestComment.content.asMessageBody()
        if (commentBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (author, _, pullRequest, scmTeam, repo) = suspendedTransaction {
            val pullRequest = pullRequestComment.pullRequest
            val author = pullRequestComment.author
            val authorOrgMember = pullRequestComment.authorOrgMember
            val repo = pullRequest.repo
            val scmTeam = repo.scmTeam

            Models(
                author = author.asDataModel(),
                authorOrgMember = authorOrgMember.asDataModel(),
                pullRequest = pullRequest.asDataModel(),
                scmTeam = scmTeam.asDataModel(),
                repo = repo.asDataModel(),
            )
        }

        val body = commentBody.asMarkdown()

        author?.let {
            syncToGitHub(scmTeam, repo, author) { client ->
                val comment = client.createIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    issueNumber = pullRequest.number,
                    body = GitHubBody(body = body),
                )

                commentStore.setPrCommentDetails(
                    comment = pullRequestComment,
                    prCommentId = comment.id.toString(),
                    prCommentUrl = comment.htmlUrl,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun updateIssueComment(
        pullRequestComment: PullRequestCommentDAO,
    ) = withLoggingContextAsync(
        "commentId" to pullRequestComment.id.value,
    ) {
        val prCommentId = pullRequestComment.prCommentId ?: return@withLoggingContextAsync

        val commentBody = pullRequestComment.content.asMessageBody()
        if (commentBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (author, _, _, scmTeam, repo) = suspendedTransaction {
            val pullRequest = pullRequestComment.pullRequest
            val author = pullRequestComment.author
            val authorOrgMember = pullRequestComment.authorOrgMember
            val repo = pullRequest.repo
            val scmTeam = repo.scmTeam

            Models(
                author = author.asDataModel(),
                authorOrgMember = authorOrgMember.asDataModel(),
                pullRequest = pullRequest.asDataModel(),
                scmTeam = scmTeam.asDataModel(),
                repo = repo.asDataModel(),
            )
        }

        val body = commentBody.asMarkdown()

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                val comment = client.updateIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = prCommentId,
                    body = GitHubBody(body),
                )

                commentStore.updatePrCommentDetails(
                    comment = pullRequestComment,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun deleteIssueComment(
        comment: PullRequestCommentDAO,
    ) = withLoggingContextAsync(
        "commentId" to comment.id.value,
    ) {
        val prCommentId = comment.prCommentId ?: return@withLoggingContextAsync

        val (author, _, _, scmTeam, repo) = suspendedTransaction {
            val pullRequest = comment.pullRequest
            val author = comment.author
            val authorOrgMember = comment.authorOrgMember
            val repo = pullRequest.repo
            val scmTeam = repo.scmTeam

            Models(
                author = author.asDataModel(),
                authorOrgMember = authorOrgMember.asDataModel(),
                pullRequest = pullRequest.asDataModel(),
                scmTeam = scmTeam.asDataModel(),
                repo = repo.asDataModel(),
            )
        }

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                client.deleteIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = prCommentId,
                )

                commentStore.clearPrCommentDetails(comment)
            }
        }
    }

    private suspend fun createIssueComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        if (message.prCommentId != null) {
            LOGGER.warnAsync { "Comment already created in GitHub" }
            return@withLoggingContextAsync
        }

        val commentBody = message.content.asMessageBody()
        if (commentBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (author, _, pullRequest, scmTeam, repo) = suspendedTransaction {
            val thread = message.thread
            val pullRequest = thread.pullRequest ?: error("Thread is not a pull request thread")
            val author = message.author
            val authorOrgMember = message.authorOrgMember
            val repo = thread.repo ?: error("PR Thread is missing repo")
            val scmTeam = repo.scmTeam

            Models(
                author = author?.asDataModel(),
                authorOrgMember = authorOrgMember.asDataModel(),
                pullRequest = pullRequest.asDataModel(),
                scmTeam = scmTeam.asDataModel(),
                repo = repo.asDataModel(),
            )
        }

        val body = commentBody.asMarkdown()

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                val comment = client.createIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    issueNumber = pullRequest.number,
                    body = GitHubBody(body = body),
                )

                messageStore.setPrCommentDetails(
                    message = message,
                    prCommentId = comment.id.toString(),
                    prCommentUrl = comment.htmlUrl,
                    prCommentUpdatedAt = comment.updatedAt,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun updateIssueComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        val prCommentId = message.prCommentId ?: return@withLoggingContextAsync

        val commentBody = message.content.asMessageBody()
        if (commentBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (author, scmTeam, repo) = suspendedTransaction {
            val author = message.author
            val repo = message.thread.repo ?: error("PR Thread is missing repo")
            val scmTeam = repo.scmTeam

            Triple(author?.asDataModel(), scmTeam.asDataModel(), repo.asDataModel())
        }

        val body = commentBody.asMarkdown()

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                val comment = client.updateIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = prCommentId,
                    body = GitHubBody(body),
                )

                messageStore.updatePrCommentDetails(
                    message = message,
                    prCommentUpdatedAt = comment.updatedAt,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun deleteIssueComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        val prCommentId = message.prCommentId ?: return@withLoggingContextAsync

        val (author, scmTeam, repo) = suspendedTransaction {
            val author = message.author
            val repo = message.thread.repo ?: error("PR Thread is missing repo")
            val scmTeam = repo.scmTeam

            Triple(author?.asDataModel(), scmTeam.asDataModel(), repo.asDataModel())
        }

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                client.deleteIssueComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = prCommentId,
                )

                messageStore.clearPrCommentDetails(message)
            }
        }
    }

    private suspend fun updateReview(
        pullRequestReview: PullRequestReviewDAO,
    ) = withLoggingContextAsync(
        "reviewId" to pullRequestReview.id.value,
    ) {
        val content = pullRequestReview.content
        if (content == null) {
            LOGGER.warnAsync { "Review content is empty, not updating" }
            return@withLoggingContextAsync
        }

        val reviewBody = content.asMessageBody()
        if (reviewBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (author, _, pullRequest, scmTeam, repo) = suspendedTransaction {
            val pullRequest = pullRequestReview.pullRequest
            val author = pullRequestReview.author
            val authorOrgMember = pullRequestReview.authorOrgMember
            val repo = pullRequest.repo
            val scmTeam = repo.scmTeam

            Models(
                author = author.asDataModel(),
                authorOrgMember = authorOrgMember.asDataModel(),
                pullRequest = pullRequest.asDataModel(),
                scmTeam = scmTeam.asDataModel(),
                repo = repo.asDataModel(),
            )
        }

        val body = reviewBody.asMarkdown()

        author?.let {
            syncToGitHub(scmTeam, repo, author) { client ->
                val review = client.updateReviewComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    pullRequestNumber = pullRequest.number,
                    reviewId = pullRequestReview.prReviewId,
                    body = GitHubBody(body = body),
                )

                review.bodyHash?.let { reviewStore.updatePrReviewDetails(review = pullRequestReview, prReviewBodyHash = it) }
            }
        }
    }

    private suspend fun createReviewComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        if (message.prCommentId != null) {
            LOGGER.warnAsync { "Message already created in GitHub" }
            return@withLoggingContextAsync
        }

        val messageBody = message.content.asMessageBody()
        if (messageBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val firstMessage = suspendedTransaction {
            messageStore.findFirstMessageForThread(trx = this, threadId = message.threadId)
        }
        val firstMessageCommentId = firstMessage.prCommentId ?: return@withLoggingContextAsync

        val (scmTeam, repo) = sourceMarkStore.getTeamAndRepo(message.threadId) ?: error("PR Thread is missing repo")
        val (author, pullRequest) = suspendedTransaction {
            Pair(message.author?.asDataModel(), message.thread.pullRequest?.asDataModel())
        }
        val pullRequestNumber = pullRequest?.number ?: return@withLoggingContextAsync

        val body = messageBody.asMarkdown().let {
            commentDecoratorProvider.get(teamId = scmTeam.id).appendCreateSignature(
                body = it,
                link = message.asDataModel().dashboardUrl(
                    urlBuilderProvider = urlBuilderProvider,
                    orgId = scmTeam.orgId,
                ),
            )
        }

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                val comment = client.createPullRequestReviewComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    pullRequestNumber = pullRequestNumber,
                    commentId = firstMessageCommentId,
                    body = GitHubBody(body = body),
                )

                messageStore.setPrCommentDetails(
                    message = message,
                    prCommentId = comment.id.toString(),
                    prCommentUrl = comment.htmlUrl,
                    prCommentUpdatedAt = comment.updatedAt,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun updateReviewComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        val commentId = message.prCommentId ?: return@withLoggingContextAsync // Message was not ingested from GitHub

        val messageBody = message.content.asMessageBody()
        if (messageBody == null) {
            LOGGER.warnAsync { "Not posting to Github because deserialization failed" }
            return@withLoggingContextAsync
        }

        val (scmTeam, repo) = sourceMarkStore.getTeamAndRepo(message.threadId) ?: error("PR Thread is missing repo")
        val author = suspendedTransaction { message.author?.asDataModel() }

        val body = messageBody.asMarkdown().let {
            commentDecoratorProvider.get(teamId = scmTeam.id).appendEditSignature(
                body = it,
                link = message.asDataModel().dashboardUrl(
                    urlBuilderProvider = urlBuilderProvider,
                    orgId = scmTeam.orgId,
                ),
            )
        }

        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                val comment = client.updatePullRequestReviewComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = commentId,
                    body = GitHubBody(body = body),
                )

                messageStore.updatePrCommentDetails(
                    message = message,
                    prCommentUpdatedAt = comment.updatedAt,
                    prCommentBodyHash = comment.bodyHash,
                )
            }
        }
    }

    private suspend fun deleteReviewComment(
        message: MessageDAO,
    ) = withLoggingContextAsync(
        "messageId" to message.idValue,
    ) {
        val commentId = message.prCommentId ?: return@withLoggingContextAsync // Message was not ingested from GitHub

        val (scmTeam, repo) = sourceMarkStore.getTeamAndRepo(message.threadId) ?: error("PR Thread is missing repo")
        val author = suspendedTransaction { message.author?.asDataModel() }
        author?.let {
            syncToGitHub(scmTeam = scmTeam, repo = repo, author = author) { client ->
                client.deletePullRequestReviewComment(
                    owner = repo.externalOwner,
                    repoName = repo.externalName,
                    commentId = commentId,
                )

                messageStore.clearPrCommentDetails(message)
            }
        }
    }

    private suspend fun updatePullRequest(
        pullRequest: PullRequestDAO,
    ) = withLoggingContextAsync(
        "pullRequestId" to pullRequest.id.value,
    ) {
        val (team, repo, creator) = suspendedTransaction {
            val repo = pullRequest.repo
            val scmTeam = repo.scmTeam
            Triple(scmTeam.asDataModel(), repo.asDataModel(), pullRequest.creator.asDataModel())
        }

        syncToGitHub(team, repo, creator) { client ->
            val ghPullRequest = client.updatePullRequest(
                owner = repo.externalOwner,
                repoName = repo.externalName,
                prNumber = pullRequest.number,
                body = GitHubPullRequestUpdateBody(body = pullRequest.description?.asMessageBody()?.asMarkdown()),
            )

            pullRequestStore.updateDescriptionHash(pullRequest = pullRequest, descriptionHash = ghPullRequest.bodyHash)
        }
    }
}

private val MessageDAO.threadId: ThreadId
    get() = readValues[MessageModel.thread].value

private val Provider.hasIssueComments: Boolean
    get() = when (this) {
        Provider.GitHub,
        Provider.GitHubEnterprise,
            -> true

        Provider.Asana,
        Provider.Aws,
        Provider.AwsIdentityCenter,
        Provider.AzureDevOps,
        Provider.Bitbucket,
        Provider.BitbucketDataCenter,
        Provider.BitbucketPipelines,
        Provider.Buildkite,
        Provider.CircleCI,
        Provider.Coda,
        Provider.Confluence,
        Provider.ConfluenceDataCenter,
        Provider.CustomIntegration,
        Provider.GenericSaml,
        Provider.GitHubActions,
        Provider.GitLab,
        Provider.GitLabPipelines,
        Provider.GitLabSelfHosted,
        Provider.GoogleDrive,
        Provider.GoogleDriveWorkspace,
        Provider.GoogleWorkspace,
        Provider.Jira,
        Provider.JiraDataCenter,
        Provider.Linear,
        Provider.MicrosoftEntra,
        Provider.Notion,
        Provider.Okta,
        Provider.PingOne,
        Provider.Slack,
        Provider.StackOverflowTeams,
        Provider.Unblocked,
        Provider.Web,
            -> false
    }

private val MessageDAO.isGitHubTopLevelComment: Boolean
    get() = readValues[MessageModel.prCommentUrl]?.contains("#issuecomment") ?: false

private data class Models(
    val author: Member?,
    val authorOrgMember: OrgMember,
    val pullRequest: PullRequest,
    val scmTeam: ScmTeam,
    val repo: Repo,
)
