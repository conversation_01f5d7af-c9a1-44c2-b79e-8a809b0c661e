package com.nextchaptersoftware.pr.ingestion

import com.nextchaptersoftware.common.model.messageBody
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeMessage
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePullRequest
import com.nextchaptersoftware.db.ModelBuilders.makePullRequestComment
import com.nextchaptersoftware.db.ModelBuilders.makePullRequestReview
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSourceMark
import com.nextchaptersoftware.db.ModelBuilders.makeThread
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MessageDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.PullRequestCommentDAO
import com.nextchaptersoftware.db.models.PullRequestDAO
import com.nextchaptersoftware.db.models.PullRequestReviewDAO
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SourceMarkDAO
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.stores.MessageStore
import com.nextchaptersoftware.db.stores.PullRequestCommentStore
import com.nextchaptersoftware.db.stores.PullRequestReviewStore
import com.nextchaptersoftware.db.stores.PullRequestStore
import com.nextchaptersoftware.db.stores.SourceMarkStore
import com.nextchaptersoftware.db.stores.TeamAndRepo
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.markdown.MarkdownConverter.asMessageBody
import com.nextchaptersoftware.pr.ingestion.signature.NoOpUnblockedPRCommentDecorator
import com.nextchaptersoftware.pr.ingestion.signature.UnblockedPRCommentDecoratorProvider
import com.nextchaptersoftware.scm.MockObjects
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.models.transformers.asScmPullRequestReview
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.request
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.verify
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verifyNoInteractions

class UnblockedPRCommentServiceTest : DatabaseTestsBase() {
    private lateinit var identity: IdentityDAO
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var author: MemberDAO
    private lateinit var repo: RepoDAO
    private lateinit var thread: ThreadDAO
    private lateinit var message: MessageDAO
    private lateinit var sourcemark: SourceMarkDAO
    private lateinit var pullRequest: PullRequestDAO
    private lateinit var review: PullRequestReviewDAO
    private lateinit var prComment: PullRequestCommentDAO

    private val pullRequestStore = mock<PullRequestStore>()
    private val commentStore = mock<PullRequestCommentStore>()
    private val reviewStore = mock<PullRequestReviewStore>()
    private val messageStore = mock<MessageStore>()
    private val sourceMarkStore = mock<SourceMarkStore>()
    private val scmUserApiFactory = mock<ScmUserApiFactory>()
    private val scmUserApi = mock<ScmUserApi>()
    private val commentDecoratorProvider = mock<UnblockedPRCommentDecoratorProvider>().also {
        runBlocking { `when`(it.get(any())).thenReturn(NoOpUnblockedPRCommentDecorator()) }
    }
    private val pr = MockObjects.gitHubPullRequest()
    private val prReviewComment = MockObjects.gitHubPullRequestReviewComment()
    private val prReview = MockObjects.gitHubPullRequestReview(body = "Hello there").asScmPullRequestReview
    private val issueComment = MockObjects.gitHubIssueComment()

    private val notFoundException = ClientRequestException(
        response = mock<HttpResponse>().also {
            `when`(it.status).thenReturn(HttpStatusCode.NotFound)
            `when`(it.call).thenReturn(mock())
            `when`(it.request).thenReturn(mock())
            `when`(it.request.method).thenReturn(mock())
        },
        cachedResponseText = "",
    )

    private val forbiddenException = ClientRequestException(
        response = mock<HttpResponse>().also {
            `when`(it.status).thenReturn(HttpStatusCode.Forbidden)
            `when`(it.call).thenReturn(mock())
            `when`(it.request).thenReturn(mock())
            `when`(it.request.method).thenReturn(mock())
        },
        cachedResponseText = "",
    )

    private val service = UnblockedPRCommentService(
        pullRequestStore = pullRequestStore,
        commentStore = commentStore,
        reviewStore = reviewStore,
        messageStore = messageStore,
        sourceMarkStore = sourceMarkStore,
        scmUserApiFactory = scmUserApiFactory,
        urlBuilderProvider = StandardUrlBuilderProvider(),
        commentDecoratorProvider = commentDecoratorProvider,
    )

    suspend fun setup() {
        identity = makeIdentity()
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        author = makeMember(scmTeam = scmTeam, identity = identity)
        repo = makeRepo(scmTeam = scmTeam)
        pullRequest = makePullRequest(
            repo = repo,
            creator = author,
        )
        thread = makeThread(org = org, pullRequest = pullRequest)
        message = makeMessage(
            author = author,
            thread = thread,
            content = messageBody { version = "1" }.toByteArray(),
            prCommentId = "789",
        )
        sourcemark = makeSourceMark(scmTeam = scmTeam, repo = repo, thread = thread)
        review = makePullRequestReview(
            pullRequest = pullRequest,
            author = author,
            content = "Hello".asMessageBody().toByteArray(),
        )
        prComment = makePullRequestComment(
            pullRequest = pullRequest,
            author = author,
            content = issueComment.body.asMessageBody().toByteArray(),
            prCommentId = issueComment.id.toString(),
        )

        `when`(messageStore.findOrNull(trx = null, id = message.idValue)).thenReturn(message)
        `when`(sourceMarkStore.getTeamAndRepo(thread.idValue)).thenReturn(TeamAndRepo(scmTeam.asDataModel(), repo.asDataModel()))
        `when`(
            scmUserApiFactory.getApiFromIdentity(
                orgId = eq(scmTeam.asDataModel().orgId),
                identityId = eq(identity.id.value),
                scm = any(),
                clientEngine = any(),
            ),
        )
            .thenReturn(scmUserApi)
    }

    @Test
    fun `create pull request comment`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction { prComment.prCommentId = null }

        `when`(
            scmUserApi.createIssueComment(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq(pullRequest.number),
                any(),
            ),
        ).thenReturn(issueComment)

        service.handleComment(prComment.id.value)

        verify(commentStore, times(1)).setPrCommentDetails(
            argThat { arg -> arg.id.value == prComment.id.value },
            argThat { arg -> arg == issueComment.id.toString() },
            argThat { arg -> arg == issueComment.htmlUrl },
            argThat { arg -> arg == issueComment.bodyHash },
        )
    }

    @Test
    fun `create pull request comment -- pull request not found`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction { prComment.prCommentId = null }

        `when`(scmUserApi.createIssueComment(any(), any(), any(), any())).thenThrow(notFoundException)

        service.handleComment(prComment.id.value)

        verifyNoInteractions(commentStore)
    }

    @Test
    fun `update pull request comment`() = suspendingDatabaseTest {
        setup()
        `when`(
            scmUserApi.updateIssueComment(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq(issueComment.id.toString()),
                any(),
            ),
        ).thenReturn(issueComment)

        service.handleComment(prComment.id.value)

        verify(commentStore, times(1)).updatePrCommentDetails(
            argThat { arg -> arg.id.value == prComment.id.value },
            argThat { arg -> arg == issueComment.bodyHash },
        )
    }

    @Test
    fun `update pull request comment -- comment not found`() = suspendingDatabaseTest {
        setup()
        `when`(scmUserApi.updateIssueComment(any(), any(), any(), any())).thenThrow(notFoundException)

        service.handleComment(prComment.id.value)

        verifyNoInteractions(commentStore)
    }

    @Test
    fun `delete pull request comment`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction { prComment.isDeleted = true }
        service.handleComment(prComment.id.value)

        verify(commentStore, times(1)).clearPrCommentDetails(
            argThat { arg -> arg.id.value == prComment.id.value },
        )
    }

    @Test
    fun `delete pull request comment -- comment not found`() = suspendingDatabaseTest {
        setup()
        `when`(scmUserApi.deleteIssueComment(any(), any(), any())).thenThrow(notFoundException)
        suspendedTransaction { prComment.isDeleted = true }
        service.handleComment(prComment.id.value)

        verifyNoInteractions(commentStore)
    }

    @Test
    fun `create pull request top level comment`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            message.prCommentId = null
            sourcemark.delete()
        }

        `when`(
            scmUserApi.createIssueComment(
                owner = eq(repo.externalOwner),
                repoName = eq(repo.externalName),
                issueNumber = eq(pullRequest.number),
                body = any(),
            ),
        ).thenReturn(issueComment)

        service.handle(message.idValue)

        verify(messageStore, times(1)).setPrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
            argThat { arg -> arg == issueComment.id.toString() },
            argThat { arg -> arg == issueComment.htmlUrl },
            argThat { arg -> arg == issueComment.updatedAt },
            argThat { arg -> arg == issueComment.bodyHash },
        )
    }

    @Test
    fun `create pull request top level comment -- pull request not found`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction {
            message.prCommentId = null
            sourcemark.delete()
        }

        `when`(
            scmUserApi.createIssueComment(
                owner = any(),
                repoName = any(),
                issueNumber = any(),
                body = any(),
            ),
        ).thenThrow(notFoundException)

        service.handle(message.idValue)

        verifyNoInteractions(messageStore)
    }

    @Test
    fun `update pull request top level comment`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            message.prCommentId = issueComment.id.toString()
            message.prCommentUrl = issueComment.htmlUrl.asString
            sourcemark.delete()
        }

        `when`(
            scmUserApi.updateIssueComment(
                owner = eq(repo.externalOwner),
                repoName = eq(repo.externalName),
                commentId = eq(issueComment.id.toString()),
                body = any(),
            ),
        ).thenReturn(issueComment)

        service.handle(message.idValue)

        verify(messageStore, times(1)).updatePrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
            argThat { arg -> arg == issueComment.updatedAt },
            argThat { arg -> arg == issueComment.bodyHash },
        )
    }

    @Test
    fun `update pull request top level comment -- comment not found`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            message.prCommentId = issueComment.id.toString()
            message.prCommentUrl = issueComment.htmlUrl.asString
            sourcemark.delete()
        }

        `when`(
            scmUserApi.updateIssueComment(
                owner = any(),
                repoName = any(),
                commentId = any(),
                body = any(),
            ),
        ).thenThrow(notFoundException)

        service.handle(message.idValue)

        verifyNoInteractions(messageStore)
    }

    @Test
    fun `delete pull request top level comment`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            message.isDeleted = true
            message.prCommentId = issueComment.id.toString()
            message.prCommentUrl = issueComment.htmlUrl.asString
            sourcemark.delete()
        }

        service.handle(message.idValue)

        verify(scmUserApi, times(1)).deleteIssueComment(
            owner = eq(repo.externalOwner),
            repoName = eq(repo.externalName),
            commentId = eq(issueComment.id.toString()),
        )

        verify(messageStore, times(1)).clearPrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
        )
    }

    @Test
    fun `delete pull request top level comment -- comment not found`() = suspendingDatabaseTest {
        setup()

        suspendedTransaction {
            message.isDeleted = true
            message.prCommentId = issueComment.id.toString()
            message.prCommentUrl = issueComment.htmlUrl.asString
            sourcemark.delete()
        }

        `when`(
            scmUserApi.deleteIssueComment(
                owner = any(),
                repoName = any(),
                commentId = any(),
            ),
        ).thenThrow(notFoundException)

        service.handle(message.idValue)

        verifyNoInteractions(messageStore)
    }

    @Test
    fun `update pull request review`() = suspendingDatabaseTest {
        setup()

        `when`(
            scmUserApi.updateReviewComment(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq(pullRequest.number),
                eq(review.prReviewId),
                any(),
            ),
        ).thenReturn(prReview)

        service.handleReview(review.id.value)

        verify(reviewStore, times(1)).updatePrReviewDetails(
            argThat { arg -> arg.id.value == review.id.value },
            argThat { arg -> arg == prReview.bodyHash },
        )
    }

    @Test
    fun `update pull request review -- review not found`() = suspendingDatabaseTest {
        setup()

        `when`(scmUserApi.updateReviewComment(any(), any(), any(), any(), any())).thenThrow(notFoundException)

        service.handleReview(review.id.value)

        verifyNoInteractions(reviewStore)
    }

    @Test
    fun `create pull request review comment`() = suspendingDatabaseTest {
        setup()
        val firstMessage = makeMessage(prCommentId = "abcd").asDataModel()
        suspendedTransaction { message.prCommentId = null }

        `when`(messageStore.findFirstMessageForThread(anyOrNull(), any())).thenReturn(firstMessage)

        `when`(
            scmUserApi.createPullRequestReviewComment(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq(pullRequest.number),
                eq("abcd"),
                any(),
            ),
        ).thenReturn(prReviewComment)

        service.handle(message.idValue)

        verify(messageStore, times(1)).setPrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
            argThat { arg -> arg == prReviewComment.id.toString() },
            argThat { arg -> arg == prReviewComment.htmlUrl },
            argThat { arg -> arg == prReviewComment.updatedAt },
            argThat { arg -> arg == prReviewComment.bodyHash },
        )
    }

    @Test
    fun `create pull request review comment -- message not found`() = suspendingDatabaseTest {
        setup()
        val firstMessage = makeMessage(prCommentId = "abcd").asDataModel()
        suspendedTransaction { message.prCommentId = null }

        `when`(messageStore.findFirstMessageForThread(anyOrNull(), any())).thenReturn(firstMessage)

        `when`(scmUserApi.createPullRequestReviewComment(any(), any(), any(), any(), any())).thenThrow(notFoundException)

        service.handle(message.idValue)

        verify(messageStore, times(0)).setPrCommentDetails(any(), any(), any(), any(), any())
    }

    @Test
    fun `create pull request review comment -- forbidden`() = suspendingDatabaseTest {
        setup()
        val firstMessage = makeMessage(prCommentId = "abcd").asDataModel()
        suspendedTransaction { message.prCommentId = null }

        `when`(messageStore.findFirstMessageForThread(anyOrNull(), any())).thenReturn(firstMessage)

        `when`(scmUserApi.createPullRequestReviewComment(any(), any(), any(), any(), any())).thenThrow(forbiddenException)

        assertThrows<ClientRequestException> {
            service.handle(message.idValue)
        }
    }

    @Test
    fun `update pull request review comment`() = suspendingDatabaseTest {
        setup()
        `when`(
            scmUserApi.updatePullRequestReviewComment(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq("789"),
                any(),
            ),
        ).thenReturn(prReviewComment)

        service.handle(message.idValue)

        verify(messageStore, times(1)).updatePrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
            argThat { arg -> arg == prReviewComment.updatedAt },
            argThat { arg -> arg == prReviewComment.bodyHash },
        )
    }

    @Test
    fun `update pull request review comment -- message not found`() = suspendingDatabaseTest {
        setup()

        `when`(scmUserApi.updatePullRequestReviewComment(any(), any(), any(), any())).thenThrow(notFoundException)

        service.handle(message.idValue)

        verifyNoInteractions(messageStore)
    }

    @Test
    fun `delete pull request review comment`() = suspendingDatabaseTest {
        setup()
        suspendedTransaction { message.isDeleted = true }
        service.handle(message.idValue)

        verify(messageStore, times(1)).clearPrCommentDetails(
            argThat { arg -> arg.idValue == message.idValue },
        )
    }

    @Test
    fun `delete pull request review comment -- message not found`() = suspendingDatabaseTest {
        setup()

        `when`(scmUserApi.deletePullRequestReviewComment(any(), any(), any())).thenThrow(notFoundException)

        suspendedTransaction { message.isDeleted = true }
        service.handle(message.idValue)

        verifyNoInteractions(messageStore)
    }

    @Test
    fun `update pull request`() = suspendingDatabaseTest {
        setup()

        `when`(
            scmUserApi.updatePullRequest(
                eq(repo.externalOwner),
                eq(repo.externalName),
                eq(pullRequest.number),
                any(),
            ),
        ).thenReturn(pr)

        service.handlePullRequest(pullRequest.id.value)

        verify(pullRequestStore, times(1)).updateDescriptionHash(
            argThat { arg -> arg.id == pullRequest.id },
            argThat { arg -> arg == pr.bodyHash },
        )
    }

    @Test
    fun `update pull request -- pull request not found`() = suspendingDatabaseTest {
        setup()

        `when`(scmUserApi.updatePullRequest(any(), any(), any(), any())).thenThrow(notFoundException)
        service.handlePullRequest(pullRequest.id.value)

        verifyNoInteractions(pullRequestStore)
    }
}
