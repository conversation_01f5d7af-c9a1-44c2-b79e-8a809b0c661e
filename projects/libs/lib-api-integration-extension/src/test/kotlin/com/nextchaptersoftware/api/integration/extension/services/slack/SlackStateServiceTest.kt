package com.nextchaptersoftware.api.integration.extension.services.slack

import com.nextchaptersoftware.api.models.SlackUserAppInstallState
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamDAO
import com.nextchaptersoftware.db.models.SlackTeamModel
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.memberAssociationStore
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.jetbrains.exposed.sql.update
import org.junit.jupiter.api.Test

class SlackStateServiceTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var orgMember: OrgMemberDAO
    private lateinit var scmMember: MemberDAO
    private lateinit var scmIdentity: IdentityDAO
    private lateinit var slackMember: MemberDAO
    private lateinit var slackIdentity: IdentityDAO
    private lateinit var slackTeam: SlackTeamDAO
    private lateinit var slackInstallation: InstallationDAO

    private val service = SlackStateService()

    @Suppress("MaxLineLength")
    private suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        slackInstallation = makeInstallation(
            org = org,
            provider = Provider.Slack,
        )
        slackTeam = makeSlackTeam(
            botScope = "app_mentions:read,channels:history,channels:join,channels:read,chat:write,files:read,commands,groups:history,groups:read,im:history,im:read,team:read,users.profile:read,users:read,users:read.email",
            userScope = "groups:read,groups:write.invites",
            org = org,
            installation = slackInstallation,
            slackExternalTeamId = slackInstallation.installationExternalId,
        )
        scmIdentity = makeIdentity(provider = Provider.GitHub, externalId = "identityA")
        orgMember = makeOrgMember(org = org)
        scmMember =
            makeMember(scmTeam = scmTeam, orgMember = orgMember, isPrimaryMember = true, isCurrentMember = true, identity = scmIdentity)

        slackIdentity = makeIdentity(provider = Provider.Slack, externalTeamId = slackInstallation.installationExternalId)
        slackMember =
            makeMember(identity = slackIdentity, org = org, installation = slackInstallation, isPrimaryMember = false)
        memberAssociationStore.setPrimaryMember(memberId = slackMember.idValue, primaryMemberId = scmMember.idValue)
    }

    @Suppress("MaxLineLength")
    @Test
    fun `test getHasPrivateScopes`() = suspendingDatabaseTest {
        setup()

        val initialHasPrivateScopes = service.getHasPrivateScopes(slackTeamId = slackTeam.idValue)
        assertThat(initialHasPrivateScopes).isTrue()

        suspendedTransaction {
            SlackTeamModel.update({ SlackTeamModel.id eq slackTeam.id.value }) { statement ->
                statement[this.botScope] = "chat:write"
            }
        }

        val updatedHasPrivateScopes = service.getHasPrivateScopes(slackTeamId = slackTeam.idValue)
        assertThat(updatedHasPrivateScopes).isFalse()

        suspendedTransaction {
            SlackTeamModel.update({ SlackTeamModel.id eq slackTeam.id.value }) { statement ->
                statement[this.botScope] =
                    "app_mentions:read,channels:history,channels:join,channels:read,chat:write,groups:history,groups:read,im:history,im:read,team:read,users.profile:read,users:read,users:read.email"
                statement[this.userScope] = "groups:read"
            }
        }

        val updatedHasPrivateScopes2 = service.getHasPrivateScopes(slackTeamId = slackTeam.idValue)
        assertThat(updatedHasPrivateScopes2).isFalse()
    }

    @Suppress("MaxLineLength")
    @Test
    fun `test getHasCommandScopes`() = suspendingDatabaseTest {
        setup()

        val initialHasCommandScopes = service.getHasCommandScopes(slackTeamId = slackTeam.idValue)
        assertThat(initialHasCommandScopes).isTrue()

        suspendedTransaction {
            SlackTeamModel.update({ SlackTeamModel.id eq slackTeam.id.value }) { statement ->
                statement[this.botScope] = "chat:write"
            }
        }

        val updatedHasCommandScopes = service.getHasCommandScopes(slackTeamId = slackTeam.idValue)
        assertThat(updatedHasCommandScopes).isFalse()
    }

    @Test
    fun `test getUserAppInstallState using member association`() = suspendingDatabaseTest {
        setup()

        val initialSlackUserState = service.getUserAppInstallState(
            orgId = org.idValue,
            orgMemberId = scmMember.orgMemberId,
            slackTeamId = slackTeam.idValue,
        )
        assertThat(initialSlackUserState).isEqualTo(SlackUserAppInstallState.disconnected)

        suspendedTransaction {
            IdentityModel.update({ IdentityModel.id eq slackIdentity.id.value }) { statement ->
                statement[this.rawAccessToken] = "blah".toByteArray()
            }
        }

        val updatedIdentity = Stores.identityStore.findById(identityId = slackIdentity.id.value)
        assertThat(updatedIdentity?.rawAccessToken).isNotNull()

        val finalSlackUserState = service.getUserAppInstallState(
            orgId = org.idValue,
            orgMemberId = scmMember.orgMemberId,
            slackTeamId = slackTeam.idValue,
        )
        assertThat(finalSlackUserState).isEqualTo(SlackUserAppInstallState.connected)
    }

    @Test
    fun `test getUserAppInstallState using orgMember association`() = suspendingDatabaseTest {
        setup()

        val orgMember1 = makeOrgMember(org = org)
        val scmIdentity1 = makeIdentity(provider = Provider.GitHub, externalId = "identityB")
        val scmMember1 = makeMember(
            scmTeam = scmTeam,
            orgMember = orgMember1,
            isPrimaryMember = true,
            isCurrentMember = true,
            identity = scmIdentity1,
        )

        val slackIdentity1 = makeIdentity(provider = Provider.Slack, externalTeamId = slackInstallation.installationExternalId)
        makeMember(
            identity = slackIdentity1,
            org = org,
            installation = slackInstallation,
            isPrimaryMember = false,
            orgMember = orgMember1,
        )

        val initialSlackUserState = service.getUserAppInstallState(
            orgId = org.idValue,
            orgMemberId = scmMember1.orgMemberId,
            slackTeamId = slackTeam.idValue,
        )
        assertThat(initialSlackUserState).isEqualTo(SlackUserAppInstallState.disconnected)

        suspendedTransaction {
            IdentityModel.update({ IdentityModel.id eq slackIdentity1.id.value }) { statement ->
                statement[this.rawAccessToken] = "blah".toByteArray()
            }
        }

        val updatedIdentity = Stores.identityStore.findById(identityId = slackIdentity1.id.value)
        assertThat(updatedIdentity?.rawAccessToken).isNotNull()

        val finalSlackUserState = service.getUserAppInstallState(
            orgId = org.idValue,
            orgMemberId = scmMember1.orgMemberId,
            slackTeamId = slackTeam.idValue,
        )
        assertThat(finalSlackUserState).isEqualTo(SlackUserAppInstallState.connected)
    }
}
