package com.nextchaptersoftware.api.integration.extension.services.slack

import com.nextchaptersoftware.api.models.SlackState
import com.nextchaptersoftware.api.models.SlackUserAppInstallState
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgMemberDecorator
import com.nextchaptersoftware.db.stores.SlackTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.slack.api.models.SlackPermission
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class SlackStateService(
    private val orgMemberDecorator: OrgMemberDecorator = Stores.orgMemberDecorator,
    private val slackTeamStore: SlackTeamStore = Stores.slackTeamStore,
    private val memberStore: MemberStore = Stores.memberStore,
    private val installationStore: InstallationStore = Stores.installationStore,
) {
    suspend fun getSlackState(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
    ): SlackState = withLoggingContextAsync(
        "orgId" to orgId,
        "orgMemberId" to orgMemberId,
        "slackTeamId" to slackTeamId,
    ) {
        val hasCommandScopes = getHasCommandScopes(slackTeamId = slackTeamId)
        val hasPrivateScopes = getHasPrivateScopes(slackTeamId = slackTeamId)
        val userAppInstallState = getUserAppInstallState(orgId = orgId, slackTeamId = slackTeamId, orgMemberId = orgMemberId)

        SlackState(
            userAppInstallState = userAppInstallState,
            hasPrivateScopes = hasPrivateScopes,
            hasCommandScopes = hasCommandScopes,
        )
    }

    internal suspend fun getHasPrivateScopes(
        slackTeamId: SlackTeamId,
    ): Boolean {
        val slackTeam = slackTeamStore.findById(slackTeamId = slackTeamId)
            ?: run {
                LOGGER.trace { "Could not find slack team" }
                return false
            }

        SlackPermission.contains(
            scope = slackTeam.botScope,
            permissions = listOf(
                SlackPermission.GROUPS_READ,
                SlackPermission.GROUPS_HISTORY,
                SlackPermission.IM_READ,
                SlackPermission.IM_HISTORY,
            ),
        ).takeIf { it } ?: run {
            LOGGER.trace { "Bot scope does not have private permissions" }
            return false
        }

        slackTeam.userScope?.let { userScope ->
            SlackPermission.contains(
                scope = userScope,
                permissions = listOf(SlackPermission.GROUPS_READ, SlackPermission.GROUPS_WRITE_INVITES),
            ).takeIf { it } ?: run {
                LOGGER.trace { "Bot scope does not have private permissions" }
                null
            }
        } ?: run {
            LOGGER.trace { "User scope does not exist" }
            return false
        }

        return true
    }

    internal suspend fun getHasCommandScopes(
        slackTeamId: SlackTeamId,
    ): Boolean {
        val slackTeam = slackTeamStore.findById(slackTeamId = slackTeamId)
            ?: run {
                LOGGER.trace { "Could not find slack team" }
                return false
            }

        SlackPermission.contains(
            scope = slackTeam.botScope,
            permissions = listOf(
                SlackPermission.COMMANDS,
                // Some legacy customers have command scopes (before app was on marketplace), but none of new slash commands.
                // We know that as part of the command scopes app review, we did the files:read scope addition, so we use that as the telltale
                // sign of needing an installation for legacy customers.
                SlackPermission.FILES_READ,
            ),
        ).takeIf { it } ?: run {
            LOGGER.trace { "Bot scope does not have command permissions" }
            return false
        }

        return true
    }

    internal suspend fun getUserAppInstallState(
        orgId: OrgId,
        orgMemberId: OrgMemberId,
        slackTeamId: SlackTeamId,
    ): SlackUserAppInstallState {
        val orgMemberBundle = orgMemberDecorator.decorateOrgMember(
            orgMemberId = orgMemberId,
        ) ?: run {
            LOGGER.trace { "Failed to find org member bundle" }
            return SlackUserAppInstallState.disconnected
        }

        val slackInstallation = installationStore.findBySlackTeam(orgId = orgId, slackTeamId = slackTeamId) ?: run {
            LOGGER.trace { "Failed to find slack installation" }
            return SlackUserAppInstallState.disconnected
        }

        val slackMemberIds = orgMemberBundle
            .getMembersByProvider(provider = Provider.Slack)
            .map { it.member.id }
            .nullIfEmpty()
            ?: run {
                LOGGER.trace { "Failed to find slack member ids" }
                return SlackUserAppInstallState.disconnected
            }

        memberStore.findIdentitiesByProviderWithRawToken(
            provider = Provider.Slack,
            orgId = orgId,
            installationId = slackInstallation.id,
            memberIds = slackMemberIds,
        ).nullIfEmpty() ?: run {
            LOGGER.trace { "Failed to find identities with raw token" }
            return SlackUserAppInstallState.disconnected
        }

        return SlackUserAppInstallState.connected
    }
}
