@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.api.auth.services

import com.auth0.jwt.JWT
import com.nextchaptersoftware.access.NoOpRestrictedAccessService
import com.nextchaptersoftware.access.RestrictedAccessException
import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.activation.ProviderActivation
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.api.auth.services.client.AsanaAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.ConfluenceAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.GoogleAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.JiraAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.LinearAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.NotionAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.ProviderAuthClientFactory
import com.nextchaptersoftware.api.auth.services.client.SlackAuthClientFactory
import com.nextchaptersoftware.api.auth.services.identity.DecisionIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.identity.ScmIdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.auth.services.url.login.ScmLoginSource
import com.nextchaptersoftware.api.auth.services.url.redirect.DecisionRedirectUrlService
import com.nextchaptersoftware.api.auth.services.url.redirect.ProviderRedirectUrlService
import com.nextchaptersoftware.api.auth.services.url.redirect.RedirectAuthOverrideService
import com.nextchaptersoftware.api.auth.services.url.redirect.ScmRedirectUrlService
import com.nextchaptersoftware.api.models.AgentType.dashboard
import com.nextchaptersoftware.api.models.AgentType.vscode
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.api.services.EmailService
import com.nextchaptersoftware.api.services.PersonEmailPreferencesService
import com.nextchaptersoftware.api.services.PersonUpsertService
import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.crypto.Decryptions
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makePrefilledAuthState
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.membership.MemberService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmAuthClientFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.security.store.TokenChainRedisStore
import com.nextchaptersoftware.test.utils.TestScopeExtensions.sleep
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.http.parametersOf
import io.ktor.server.auth.jwt.JWTCredential
import io.ktor.server.util.url
import io.ktor.util.encodeBase64
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import kotlinx.datetime.toKotlinInstant
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.data.Percentage
import org.jetbrains.exposed.sql.and
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.given
import org.mockito.kotlin.mock

class LoginServiceTest : DatabaseTestsBase() {
    private val authenticationConfig = GlobalConfig.INSTANCE.authentication

    private val jwt = Jwt(authenticationConfig = authenticationConfig)

    private val providerAuthClientFactory = ProviderAuthClientFactory(
        asanaAuthClientFactory = AsanaAuthClientFactory(),
        slackAuthClientFactory = SlackAuthClientFactory(),
        jiraAuthClientFactory = JiraAuthClientFactory(),
        confluenceAuthClientFactory = ConfluenceAuthClientFactory(),
        linearAuthClientFactory = LinearAuthClientFactory(),
        notionAuthClientFactory = NotionAuthClientFactory(),
        googleAuthClientFactory = GoogleAuthClientFactory(),
    )

    private val loginConfig = GlobalConfig.INSTANCE.copy(
        authentication = GlobalConfig.INSTANCE.authentication.copy(
            authRedirectUrl = "https://localhost/login/exchange",
            authRedirectAltUrls = listOf("https://otherdomain.com/path"),
        ),
    )

    private val userSecretService by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(GlobalConfig.INSTANCE.encryption.userSecrets4096PublicKey),
            decryption = Decryptions.NULL,
        )
    }

    private val tokenChainRedisStore by lazy {
        TokenChainRedisStore(Redis.API)
    }

    private val scmConfig = ScmConfig.INSTANCE.copy(
        githubCloud = checkNotNull(ScmConfig.INSTANCE.githubCloud).copy(
            oauth = checkNotNull(ScmConfig.INSTANCE.githubCloud).oauth.copy(
                clientId = "123",
            ),
        ),
        bitbucketCloud = checkNotNull(ScmConfig.INSTANCE.bitbucketCloud).copy(
            oauth = checkNotNull(ScmConfig.INSTANCE.bitbucketCloud).oauth.copy(
                clientId = "xyz",
            ),
        ),
    )

    private val loginService = makeLogin()

    private val personId = PersonId.random()

    @Test
    fun `generates tokens with identity`() {
        val uuid = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val orgs = setOf(OrgId.random())
        val token = loginService.generateAuthToken(
            AuthorizeAs.Self(realIdentityId = uuid, realPersonId = personId),
            orgIds = orgs,
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )
        val authToken = JWT().decodeJwt(token.token)
        val refreshToken = JWT().decodeJwt(token.refreshToken)
        assertThat(uuid.toString()).isEqualTo(authToken.subject)
        assertThat(uuid.toString()).isEqualTo(refreshToken.subject)
        assertThat(tokenChainId.toString()).isEqualTo(authToken.getClaim(Jwt.Claim.TokenChainId.value).asString())
        assertThat(tokenChainId.toString()).isEqualTo(refreshToken.getClaim(Jwt.Claim.TokenChainId.value).asString())

        val resultOrgsClaim = authToken.getClaim(Jwt.Claim.Orgs.value).asList(String::class.java).map(OrgId::fromString).toSet()
        assertThat(resultOrgsClaim).isEqualTo(orgs)
    }

    @Test
    fun `logout results in rejections of past tokens but not new ones`() = runTest {
        val uuid = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val token = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = personId),
            orgIds = emptySet(),
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )

        val refreshToken = JWTCredential(JWT().decodeJwt(token.refreshToken))

        val loginPrincipal = jwt.refreshTokenValidator(refreshToken, tokenChainRedisStore)
        assertThat(loginPrincipal).isNotNull

        loginService.logout(refreshToken)

        val logoutPrincipal = jwt.refreshTokenValidator(refreshToken, tokenChainRedisStore)
        assertThat(logoutPrincipal).isNull()

        sleep(1.seconds)

        val token2 = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = personId),
            orgIds = emptySet(),
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )

        val refreshToken2 = JWTCredential(JWT().decodeJwt(token2.refreshToken))
        val loginPrincipal2 = jwt.refreshTokenValidator(refreshToken2, tokenChainRedisStore)
        assertThat(loginPrincipal2).isNotNull
    }

    @Test
    fun `logout cant be used to roll back validity window`() = runTest {
        val uuid = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val token = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = personId),
            orgIds = emptySet(),
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )

        val refreshToken = JWTCredential(JWT().decodeJwt(token.refreshToken))

        sleep(1.seconds)

        val token2 = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = personId),
            orgIds = emptySet(),
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )
        val refreshToken2 = JWTCredential(JWT().decodeJwt(token2.refreshToken))

        // First logout with token 2, then try to log out with token1 to push the validity window back
        loginService.logout(refreshToken2)
        loginService.logout(refreshToken)

        // If refreshToken2 is valid, it's bad news
        val logoutPrincipal = jwt.refreshTokenValidator(refreshToken2, tokenChainRedisStore)
        assertThat(logoutPrincipal).isNull()
    }

    @Test
    fun `refresh tokens expire after expected expiry period`() {
        val uuid = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val orgs = setOf(OrgId.random())
        val now = Instant.nowWithMicrosecondPrecision()

        val token = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = PersonId.random()),
            orgIds = orgs,
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = null,
        )

        val authToken = JWT().decodeJwt(token.token)
        val refreshToken = JWT().decodeJwt(token.refreshToken)

        // AUTH token expires according to authTokenExpiry config
        assertThat(
            authToken.expiresAt.toInstant().toKotlinInstant().minus(now).inWholeSeconds,
        ).isCloseTo(
            authenticationConfig.authTokenExpiry.inWholeSeconds,
            Percentage.withPercentage(1.0),
        )

        // REFRESH token is identical to the original refresh token expiry
        assertThat(
            refreshToken.expiresAt.toInstant().toKotlinInstant().minus(now).inWholeSeconds,
        ).isCloseTo(
            authenticationConfig.refreshTokenExpiry.inWholeSeconds,
            Percentage.withPercentage(1.0),
        )
    }

    @Test
    fun `refresh token are not extended beyond max expiry`() {
        val uuid = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val orgs = setOf(OrgId.random())
        val now = Instant.nowWithMicrosecondPrecision()
        val maxRefreshTokenExpiresAt = now.plus(3.days)

        val token = loginService.generateAuthToken(
            authorizeAs = AuthorizeAs.Self(realIdentityId = uuid, realPersonId = PersonId.random()),
            orgIds = orgs,
            orgsMvra = emptySet(),
            orgsAuthReq = emptySet(),
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = maxRefreshTokenExpiresAt,
        )

        val authToken = JWT().decodeJwt(token.token)
        val refreshToken = JWT().decodeJwt(token.refreshToken)

        // AUTH token expires according to authTokenExpiry config
        assertThat(
            authToken.expiresAt.toInstant().toKotlinInstant().minus(now).inWholeSeconds,
        ).isCloseTo(
            authenticationConfig.authTokenExpiry.inWholeSeconds,
            Percentage.withPercentage(1.0),
        )

        // REFRESH token is identical to the original refresh token expiry
        assertThat(
            refreshToken.expiresAt.toInstant().toKotlinInstant().epochSeconds,
        ).isCloseTo(
            maxRefreshTokenExpiresAt.epochSeconds,
            Percentage.withPercentage(1.0),
        )
    }

    @Test
    fun `builds correct redirectUrl`() = suspendingDatabaseTest {
        val authState = makePrefilledAuthState()

        val url = loginService.buildRedirectUrl(
            oAuthApiType = Scm.GitHub,
            nonce = authState.nonce,
            authRedirectOverrideUrl = null,
            clientState = "https://completion.url",
            state = "hello",
            extraParameters = parametersOf("team", "T04GG7537MJ"),
            targetIdentityId = null,
            skipAccountLinking = null,
        )

        assertThat(
            url.asString,
        ).isEqualTo(
            url {
                host = "github.com"
                protocol = URLProtocol.HTTPS
                pathSegments = listOf("login", "oauth", "authorize")
                parameters["client_id"] = "123"
                parameters["state"] = OAuthState(
                    nonce = authState.nonce,
                    provider = ApiProvider.github,
                    clientState = "https://completion.url",
                    state = "hello",
                ).encode().encodeBase64()
                parameters["redirect_uri"] = url {
                    host = "localhost"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("login", "exchange")
                }
                parameters["team"] = "T04GG7537MJ"
            },
        )
    }

    @Test
    fun `builds correct alternative redirectUrl`() = suspendingDatabaseTest {
        val authState = makePrefilledAuthState()

        val url = loginService.buildRedirectUrl(
            oAuthApiType = Scm.GitHub,
            nonce = authState.nonce,
            authRedirectOverrideUrl = "https://otherdomain.com/path".asUrl,
            clientState = null,
            state = null,
            extraParameters = null,
            targetIdentityId = null,
            skipAccountLinking = null,
        )

        assertThat(
            url.asString,
        ).isEqualTo(
            url {
                host = "github.com"
                protocol = URLProtocol.HTTPS
                pathSegments = listOf("login", "oauth", "authorize")
                parameters["client_id"] = "123"
                parameters["state"] = OAuthState(
                    nonce = authState.nonce,
                    provider = ApiProvider.github,
                ).encode().encodeBase64()
                parameters["redirect_uri"] = url {
                    host = "otherdomain.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("path")
                }
            },
        )
    }

    @Test
    fun `builds bot auth url`() = suspendingDatabaseTest {
        val authState = makePrefilledAuthState()

        val botId = IdentityId.random()

        val url = loginService.buildRedirectUrl(
            oAuthApiType = Scm.Bitbucket,
            nonce = authState.nonce,
            authRedirectOverrideUrl = null,
            clientState = "https://completion.url",
            state = "hello",
            extraParameters = null,
            targetIdentityId = botId,
            skipAccountLinking = true,
        )

        assertThat(
            url.asString,
        ).isEqualTo(
            url {
                host = "bitbucket.org"
                protocol = URLProtocol.HTTPS
                pathSegments = listOf("site", "oauth2", "authorize")
                parameters["client_id"] = "xyz"
                parameters["state"] = OAuthState(
                    nonce = authState.nonce,
                    provider = ApiProvider.bitbucket,
                    clientState = "https://completion.url",
                    state = "hello",
                    targetIdentityId = botId.value,
                    skipAccountLinking = true,
                ).encode().encodeBase64()
                parameters["response_type"] = "code"
                parameters["redirect_uri"] = url {
                    host = "localhost"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("login", "exchange")
                }
            },
        )
    }

    @Test
    fun `rejects invalid redirectUrl`() = suspendingDatabaseTest {
        val authState = makePrefilledAuthState()

        assertThrows<BadRequestException> {
            loginService.buildRedirectUrl(
                oAuthApiType = Scm.GitHub,
                nonce = authState.nonce,
                authRedirectOverrideUrl = "https://baddomain/path".asUrl,
                clientState = null,
                state = null,
                extraParameters = null,
                targetIdentityId = null,
                skipAccountLinking = null,
            )
        }
    }

    @Test
    fun `creates correct login url`() {
        val clientSecret = UUID.randomUUID()
        val agentType = vscode

        val url = loginService.buildLoginUrl(
            loginSource = ScmLoginSource(Scm.GitHub),
            clientSecret = clientSecret,
            agentType = agentType,
            authRedirectOverrideUrl = "https://redirectUrl.com/path",
            clientState = "https://completion.url",
        )

        assertThat(
            url.asString,
        ).isEqualTo(
            url {
                host = GlobalConfig.INSTANCE.service.hostName
                protocol = when (GlobalConfig.INSTANCE.service.enableTLS) {
                    true -> URLProtocol.HTTPS
                    false -> URLProtocol.HTTP
                }
                port = GlobalConfig.INSTANCE.service.port
                parameters["clientSecret"] = clientSecret.toString()
                parameters["agentType"] = "vscode"
                parameters["overrideRedirectUrl"] = "https://redirectUrl.com/path"
                parameters["clientState"] = "https://completion.url"
                pathSegments = listOf("api", "login", "github")
            },
        )
    }

    @Test
    fun `creates correct login url with missing optional parameters`() {
        val url = loginService.buildLoginUrl(
            loginSource = ScmLoginSource(Scm.GitHub),
            clientSecret = null,
            agentType = null,
            authRedirectOverrideUrl = null,
            clientState = null,
        )

        assertThat(
            url {
                host = GlobalConfig.INSTANCE.service.hostName
                protocol = when (GlobalConfig.INSTANCE.service.enableTLS) {
                    true -> URLProtocol.HTTPS
                    false -> URLProtocol.HTTP
                }
                port = GlobalConfig.INSTANCE.service.port
                pathSegments = listOf("api", "login", "github")
            },
        ).isEqualTo(
            url.asString,
        )
    }

    @Test
    fun `creates client secret cookie from authState`() = suspendingDatabaseTest {
        val customLoginConfig = GlobalConfig.INSTANCE.copy(
            authentication = GlobalConfig.INSTANCE.authentication.copy(
                secretCookieMaxAge = 10,
                secureCookies = true,
                sameSite = "Strict",
            ),
            service = GlobalConfig.INSTANCE.service.copy(
                hostName = "testhost",
            ),
        )
        val customLoginService = makeLogin(config = customLoginConfig)
        val authState = makePrefilledAuthState()
        val cookie = customLoginService.createSecretCookie(authState.secret)

        assertThat("unblockedClientSecret").isEqualTo(cookie.name)
        assertThat(authState.secret.toString()).isEqualTo(cookie.value)
        assertThat(10).isEqualTo(cookie.maxAge)
        assertThat("testhost").isEqualTo(cookie.domain)
        assertThat("/api/login/exchangeV2").isEqualTo(cookie.path)
        assertThat(cookie.secure).isTrue
        assertThat(cookie.httpOnly).isTrue
        assertThat("Strict").isEqualTo(cookie.extensions["SameSite"])
    }

    @Test
    fun `creates cookie for client type`() {
        val cookie = loginService.createAgentTypeCookie(dashboard)
        assertThat("unblockedAgentType").isEqualTo(cookie.name)
        assertThat("dashboard").isEqualTo(cookie.value)
    }

    @Test
    fun `exchanges code for identity`() = suspendingDatabaseTest {
        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(
            object : OAuthApi {
                override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                    return OAuthTokenExchange(
                        oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                        redirectUrl = null,
                    )
                }

                override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                    TODO("not applicable")
                }
            },
        )

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(mockScmUserApiFactory.getApiFromTokens(orgId = anyOrNull(), tokens = any(), scm = any(), clientEngine = any())).thenReturn(
            mockScmUserApi,
        )
        `when`(mockScmUserApi.user()).thenReturn(
            ScmAuthUser(
                externalId = "123",
                avatarUrl = Url("https://example.com/avatar"),
                htmlUrl = Url("https://example.com/html"),
                username = "name",
                displayName = "displayName",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
                oauthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                isBot = false,
            ),
        )

        val customLogin = makeLogin(
            scmAuthApiFactory = mockScmAuthApiFactory,
            scmUserApiFactory = mockScmUserApiFactory,
        )

        assertDoesNotThrow {
            customLogin.exchangeAuthCodeForIdentity(
                code = "123",
                state = null,
                signedInPersonId = null,
                orgId = null,
                oAuthApiType = Scm.GitHub,
                overrideOAuthRedirectUrl = null,
            )
        }
    }

    @Test
    fun `exchanges code for identity does not change related person`() = suspendingDatabaseTest {
        val originalPerson = makePerson(
            customDisplayName = "original person",
            primaryEmail = EmailAddress.of("<EMAIL>"),
        ).also { person ->
            makeIdentity(externalId = "123", person = person)
        }.asDataModel()

        val otherPerson = makePerson(
            customDisplayName = "other person",
            primaryEmail = EmailAddress.of("<EMAIL>"),
        ).asDataModel()

        assertThat(
            suspendedTransaction {
                IdentityDAO
                    .find { (IdentityModel.person eq originalPerson.id) and (IdentityModel.provider eq Provider.GitHub) }
                    .single()
                    .asDataModel()
                    .externalId
            },
        ).isEqualTo("123")

        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(
            object : OAuthApi {
                override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                    return OAuthTokenExchange(
                        oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                        redirectUrl = null,
                    )
                }

                override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                    TODO("not applicable")
                }
            },
        )

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(
            mockScmUserApiFactory.getApiFromTokens(
                orgId = anyOrNull(),
                tokens = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(
            mockScmUserApi,
        )
        `when`(mockScmUserApi.user()).thenReturn(
            ScmAuthUser(
                externalId = "123",
                avatarUrl = Url("https://example.com/avatar"),
                htmlUrl = Url("https://example.com/html"),
                username = "name",
                displayName = "displayName",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
                oauthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                isBot = false,
            ),
        )

        val customLogin = makeLogin(
            scmAuthApiFactory = mockScmAuthApiFactory,
            scmUserApiFactory = mockScmUserApiFactory,
        )

        assertDoesNotThrow {
            customLogin.exchangeAuthCodeForIdentity(
                code = "123",
                state = null,
                signedInPersonId = null,
                orgId = null,
                oAuthApiType = Scm.GitHub,
                overrideOAuthRedirectUrl = null,
            )
        }

        assertThat(
            suspendedTransaction {
                IdentityDAO
                    .find { (IdentityModel.person eq originalPerson.id) and (IdentityModel.provider eq Provider.GitHub) }
                    .single()
                    .asDataModel()
                    .externalId
            },
        ).isEqualTo("123")

        assertThat(
            suspendedTransaction {
                IdentityDAO
                    .find { (IdentityModel.person eq otherPerson.id) and (IdentityModel.provider eq Provider.GitHub) }
                    .toList()
            },
        ).isEmpty()
    }

    @Test
    fun `exchanges code for identity updates orgMember with person`() = suspendingDatabaseTest {
        val originalPerson = makePerson(
            customDisplayName = "original person",
            primaryEmail = EmailAddress.of("<EMAIL>"),
        )

        val org = makeOrg()
        val installation = makeInstallation(org = org)
        val identity = makeIdentity(externalId = "123", person = null)
        val orgMember = makeOrgMember(org = org, createPerson = false)
        val member = makeMember(
            installation = installation,
            identity = identity,
            orgMember = orgMember,
        )

        // Create another member to see if this member is erroneously used
        makeMember()

        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(
            object : OAuthApi {
                override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                    return OAuthTokenExchange(
                        oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                        redirectUrl = null,
                    )
                }

                override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                    TODO("not applicable")
                }
            },
        )

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(
            mockScmUserApiFactory.getApiFromTokens(
                orgId = anyOrNull(),
                tokens = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(mockScmUserApi)
        `when`(mockScmUserApi.user()).thenReturn(
            ScmAuthUser(
                externalId = "123",
                avatarUrl = Url("https://example.com/avatar"),
                htmlUrl = Url("https://example.com/html"),
                username = "name",
                displayName = "displayName",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
                oauthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                isBot = false,
            ),
        )

        val customLogin = makeLogin(
            scmAuthApiFactory = mockScmAuthApiFactory,
            scmUserApiFactory = mockScmUserApiFactory,
        )

        assertDoesNotThrow {
            customLogin.exchangeAuthCodeForIdentity(
                code = "123",
                state = null,
                orgId = null,
                signedInPersonId = originalPerson.idValue,
                oAuthApiType = Scm.GitHub,
                overrideOAuthRedirectUrl = null,
            )
        }

        assertThat(
            suspendedTransaction {
                IdentityDAO
                    .find { (IdentityModel.person eq originalPerson.id) and (IdentityModel.provider eq Provider.GitHub) }
                    .single()
                    .asDataModel()
                    .externalId
            },
        ).isEqualTo("123")

        val updatedOrgMember = suspendedTransaction {
            OrgMemberDAO
                .find { (OrgMemberModel.person eq originalPerson.id) }
                .toList()
        }.single()

        assertThat(updatedOrgMember.id.value).isEqualTo(updatedOrgMember.id.value)
        assertThat(updatedOrgMember.person?.id?.value).isEqualTo(originalPerson.id.value)

        val updatedMember = suspendedTransaction {
            MemberDAO
                .find { (MemberModel.orgMember eq orgMember.id) }
                .toList()
        }.single()

        assertThat(updatedMember.id.value).isEqualTo(member.id.value)
        assertThat(updatedMember.isCurrentMember).isEqualTo(member.isCurrentMember)
        assertThat(updatedMember.providerRole).isEqualTo(member.providerRole)
    }

    @Test
    fun `login fails when account is not verified`() = suspendingDatabaseTest {
        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(
            object : OAuthApi {
                override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                    return OAuthTokenExchange(
                        oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                        redirectUrl = null,
                    )
                }

                override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                    TODO("not applicable")
                }
            },
        )

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(
            mockScmUserApiFactory.getApiFromTokens(
                orgId = anyOrNull(),
                tokens = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(mockScmUserApi)
        given(mockScmUserApi.user()).willAnswer {
            throw UserVisibleException(
                title = "title",
                detail = "detail",
                statusCode = HttpStatusCode.BadRequest,
                url = Url("https://example.com"),
            )
        }

        val customLogin = makeLogin(
            scmAuthApiFactory = mockScmAuthApiFactory,
            scmUserApiFactory = mockScmUserApiFactory,
        )

        assertThrows<UserVisibleException> {
            customLogin.exchangeAuthCodeForIdentity(
                code = "123",
                state = null,
                orgId = null,
                signedInPersonId = null,
                oAuthApiType = Scm.GitHub,
                overrideOAuthRedirectUrl = null,
            )
        }.also {
            assertThat(it.title).isEqualTo("title")
            assertThat(it.detail).isEqualTo("detail")
            assertThat(it.url).isEqualTo(Url("https://example.com"))
            assertThat(it.statusCode).isEqualTo(HttpStatusCode.BadRequest)
        }
    }

    @Test
    fun `identity login fails when access is restricted`() = suspendingDatabaseTest {
        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(
            object : OAuthApi {
                override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                    return OAuthTokenExchange(
                        oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                        redirectUrl = null,
                    )
                }

                override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                    TODO("not applicable")
                }
            },
        )

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(
            mockScmUserApiFactory.getApiFromTokens(
                orgId = anyOrNull(),
                tokens = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(mockScmUserApi)
        `when`(mockScmUserApi.user()).thenReturn(
            ScmAuthUser(
                externalId = "123",
                avatarUrl = Url("https://example.com/avatar"),
                htmlUrl = Url("https://example.com/html"),
                username = "name",
                displayName = "displayName",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
                oauthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                isBot = false,
            ),
        )
        val restrictedAccessService = mock<RestrictedAccessServiceInterface>()
        `when`(restrictedAccessService.checkUserAccess(any(), any(), anyOrNull())).thenAnswer {
            throw RestrictedAccessException("user not allowed")
        }

        val customLogin = makeLogin(
            scmAuthApiFactory = mockScmAuthApiFactory,
            scmUserApiFactory = mockScmUserApiFactory,
            restrictedAccessService = restrictedAccessService,
        )

        assertThrows<RestrictedAccessException> {
            customLogin.exchangeAuthCodeForIdentity(
                code = "123",
                state = null,
                orgId = null,
                signedInPersonId = null,
                oAuthApiType = Scm.GitHub,
                overrideOAuthRedirectUrl = null,
            )
        }
    }

    private fun makeLogin(
        config: GlobalConfig = loginConfig,
        scmAuthApiFactory: ScmAuthApiFactory = ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = ScmWebFactory(scmConfig = scmConfig, enterpriseAppConfigStore = Stores.enterpriseAppConfigStore),
        ),
        scmUserApiFactory: ScmUserApiFactory = ScmUserApiFactory(
            scmConfig = scmConfig,
            appConfigStore = Stores.enterpriseAppConfigStore,
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = UserSecretServiceResolver(
                userSecretServiceRSA = userSecretService,
                userSecretServiceAES = mock(),
            ),
        ),
        restrictedAccessService: RestrictedAccessServiceInterface = NoOpRestrictedAccessService(),
        messageProducer: ActiveMQProducer.ActiveMQProducerSession = mock<ActiveMQProducer.ActiveMQProducerSession>(),
    ): LoginService {
        val personUpsertService = PersonUpsertService(
            slackNotifier = mock(),
            emailService = EmailService(
                personEmailPreferencesService = PersonEmailPreferencesService(
                    personStore = Stores.personStore,
                    personEmailPreferencesStore = Stores.personEmailPreferencesStore,
                ),
                notificationEventEnqueueService = NotificationEventEnqueueService(
                    eventEnqueueService = StandardEventEnqueueService(
                        messageProducer = messageProducer,
                    ),
                ),
                memberService = MemberService(),
            ),
            inactiveFollowupService = mock(),
            sampleQuestionNotifier = mock(),
            segmentProvider = mock(),
        )

        return LoginService(
            authenticationConfig = config.authentication,
            deploymentConfig = config.service,
            identityAuthExchangeService = DecisionIdentityAuthExchangeService(
                scmIdentityAuthExchangeService = ScmIdentityAuthExchangeService(
                    scmUserApiFactory = scmUserApiFactory,
                    scmAuthApiFactory = scmAuthApiFactory,
                    restrictedAccessService = restrictedAccessService,
                    userSecretServiceResolver = UserSecretServiceResolver(
                        userSecretServiceRSA = userSecretService,
                        userSecretServiceAES = mock(),
                    ),
                    enterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
                    personUpsertService = personUpsertService,
                ),
                providerIdentityAuthExchangeService = mock(),
            ),
            loginUrlService = LoginUrlService(
                deploymentConfig = config.service,
            ),
            redirectUrlService = DecisionRedirectUrlService(
                scmRedirectUrlService = ScmRedirectUrlService(
                    providerActivation = ProviderActivation(),
                    redirectAuthOverrideService = RedirectAuthOverrideService(
                        authenticationConfig = config.authentication,
                        scmConfig = scmConfig,
                    ),
                    scmAuthClientFactory = ScmAuthClientFactory(scmConfig = scmConfig),
                ),
                providerRedirectUrlService = ProviderRedirectUrlService(
                    providerAuthClientFactory = providerAuthClientFactory,
                ),
            ),
            jwt = jwt,
            tokenChainRedisStore = tokenChainRedisStore,
        )
    }
}
