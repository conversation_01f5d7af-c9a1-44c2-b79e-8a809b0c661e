package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.Url
import java.util.UUID

class DecisionIdentityAuthExchangeService(
    private val scmIdentityAuthExchangeService: IdentityAuthExchangeService<Scm>,
    private val providerIdentityAuthExchangeService: IdentityAuthExchangeService<Provider>,
) : IdentityAuthExchangeService<OAuthApiType> {

    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: OAuthApiType,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        return when (oAuthApiType) {
            is Scm -> scmIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
                oAuthApiType = oAuthApiType,
                code = code,
                signedInPersonId = signedInPersonId,
                state = state,
                overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                orgId = orgId,
                sessionId = sessionId,
            )

            is Provider -> providerIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
                oAuthApiType = oAuthApiType,
                code = code,
                signedInPersonId = signedInPersonId,
                state = state,
                overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                orgId = orgId,
                sessionId = sessionId,
            )

            else -> error("Invalid oAuthApiType: ${oAuthApiType.displayName}")
        }
    }
}
