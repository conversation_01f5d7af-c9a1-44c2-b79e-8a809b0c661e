package com.nextchaptersoftware.api.auth.services.url.redirect

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.config.OAuthConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.Parameters
import io.ktor.http.Url
import java.util.UUID

class DecisionRedirectUrlService(
    private val scmRedirectUrlService: ScmRedirectUrlService,
    private val providerRedirectUrlService: ProviderRedirectUrlService,
) : RedirectUrlService {

    override suspend fun buildRedirectUrl(
        oAuthApiType: OAuthApiType,
        nonce: UUID,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        state: String?,
        targetIdentityId: IdentityId?,
        skipAccountLinking: Boolean?,
        extraParameters: Parameters?,
        oAuthConfig: OAuthConfig?,
    ): Url {
        return when (oAuthApiType) {
            is Scm -> scmRedirectUrlService.buildRedirectUrl(
                oAuthApiType = oAuthApiType,
                nonce = nonce,
                authRedirectOverrideUrl = authRedirectOverrideUrl,
                clientState = clientState,
                state = state,
                targetIdentityId = targetIdentityId,
                skipAccountLinking = skipAccountLinking,
                extraParameters = extraParameters,
                oAuthConfig = oAuthConfig,
            )

            is Provider -> providerRedirectUrlService.buildRedirectUrl(
                oAuthApiType = oAuthApiType,
                nonce = nonce,
                authRedirectOverrideUrl = authRedirectOverrideUrl,
                clientState = clientState,
                state = state,
                targetIdentityId = targetIdentityId,
                skipAccountLinking = skipAccountLinking,
                extraParameters = extraParameters,
                oAuthConfig = oAuthConfig,
            )

            else -> error("Invalid oAuthApiType: ${oAuthApiType.displayName}")
        }
    }
}
