package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import io.ktor.http.Url
import java.util.UUID

interface IdentityAuthExchangeService<T : OAuthApiType> {
    suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: T,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity
}
