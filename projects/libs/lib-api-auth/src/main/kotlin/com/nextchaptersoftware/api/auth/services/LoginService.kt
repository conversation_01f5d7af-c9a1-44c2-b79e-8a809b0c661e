package com.nextchaptersoftware.api.auth.services

import com.nextchaptersoftware.api.auth.services.identity.IdentityAuthExchangeService
import com.nextchaptersoftware.api.auth.services.url.login.LoginSource
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.auth.services.url.redirect.RedirectUrlService
import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.AuthToken
import com.nextchaptersoftware.api.models.ScopedToken
import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.config.DeploymentConfig
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.security.ScopedResource
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.security.store.TokenChainRedisStore
import com.nextchaptersoftware.utils.asUUIDOrNull
import io.ktor.http.Cookie
import io.ktor.http.Parameters
import io.ktor.http.Url
import io.ktor.server.auth.jwt.JWTPayloadHolder
import java.util.UUID
import kotlinx.datetime.Instant
import kotlinx.datetime.toKotlinInstant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class LoginService(
    private val authenticationConfig: AuthenticationConfig,
    private val deploymentConfig: DeploymentConfig,
    private val jwt: Jwt,
    private val tokenChainRedisStore: TokenChainRedisStore,
    private val identityAuthExchangeService: IdentityAuthExchangeService<in OAuthApiType>,
    private val loginUrlService: LoginUrlService,
    private val redirectUrlService: RedirectUrlService,
) {
    companion object {
        const val CLIENT_SERVICE_COOKIE_NAME = "unblockedClientSecret"
        const val AGENT_TYPE_COOKIE_NAME = "unblockedAgentType"
    }

    fun generateAuthToken(
        authorizeAs: AuthorizeAs,
        orgIds: Set<OrgId>,
        orgsMvra: Set<OrgId>,
        orgsAuthReq: Set<OrgId>,
        tokenChainId: UUID,
        maxRefreshTokenExpiresAt: Instant?,
    ): AuthToken = AuthToken(
        token = jwt.generateAuthToken(
            identityId = authorizeAs.apparentIdentityId.value,
            orgIds = orgIds.map { it.value }.toSet(),
            orgsMvra = orgsMvra.map { it.value }.toSet(),
            orgsAuthReq = orgsAuthReq.map { it.value }.toSet(),
            personId = authorizeAs.apparentPersonId.value,
            tokenChainId = tokenChainId,
            readOnly = authorizeAs is AuthorizeAs.Other,
        ),
        refreshToken = jwt.generateRefreshToken(
            identityId = authorizeAs.realIdentityId.value,
            tokenChainId = tokenChainId,
            maxExpiresAt = maxRefreshTokenExpiresAt,
        ),
    )

    fun generateScopedAuthToken(
        authorizeAs: AuthorizeAs,
        tokenChainId: UUID,
        orgIds: Set<OrgId>,
        orgsMvra: Set<OrgId>,
        orgsAuthReq: Set<OrgId>,
        scopes: List<ScopedResource>,
    ): ScopedToken {
        return ScopedToken(
            scopedAccessToken = jwt.generateAuthToken(
                identityId = authorizeAs.apparentIdentityId.value,
                personId = authorizeAs.apparentPersonId.value,
                tokenChainId = tokenChainId,
                orgIds = orgIds.map { it.value }.toSet(),
                orgsMvra = orgsMvra.map { it.value }.toSet(),
                orgsAuthReq = orgsAuthReq.map { it.value }.toSet(),
                readOnly = authorizeAs is AuthorizeAs.Other,
                scopes = scopes,
            ),
        )
    }

    suspend fun logout(
        refreshToken: JWTPayloadHolder,
    ) = runSuspendCatching {
        if (!refreshToken.audience.contains(Jwt.Audience.Refresh.value)) {
            return@runSuspendCatching
        }

        val identityId = refreshToken.subject?.asUUIDOrNull() ?: return@runSuspendCatching
        val tokenChainId = refreshToken[Jwt.Claim.TokenChainId.value]?.asUUIDOrNull() ?: return@runSuspendCatching
        val issuedAt = refreshToken.issuedAt?.toInstant()?.toKotlinInstant() ?: return@runSuspendCatching

        tokenChainRedisStore.getTokenChainMinIAT(
            identityId = identityId,
            tokenChainId = tokenChainId,
        )?.let {
            if (issuedAt < it) {
                LOGGER.errorAsync { "[SECURITY] - Logout: use of refresh token outside token chain window" }
                return@runSuspendCatching
            }
        }

        tokenChainRedisStore.setTokenChainMinIAT(
            identityId = identityId,
            tokenChainId = tokenChainId,
            iat = issuedAt,
            expiry = authenticationConfig.refreshTokenExpiry,
        )
    }

    suspend fun buildRedirectUrl(
        oAuthApiType: OAuthApiType,
        nonce: UUID,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        state: String?,
        extraParameters: Parameters?,
        targetIdentityId: IdentityId?,
        skipAccountLinking: Boolean?,
    ): Url {
        return redirectUrlService.buildRedirectUrl(
            oAuthApiType = oAuthApiType,
            nonce = nonce,
            authRedirectOverrideUrl = authRedirectOverrideUrl,
            clientState = clientState,
            state = state,
            extraParameters = extraParameters,
            targetIdentityId = targetIdentityId,
            skipAccountLinking = skipAccountLinking,
        )
    }

    fun buildLoginUrl(
        loginSource: LoginSource,
        clientSecret: UUID?,
        agentType: AgentType?,
        authRedirectOverrideUrl: String?,
        clientState: String?,
        orgId: OrgId? = null,
    ): Url {
        return loginUrlService.buildLoginUrl(
            loginSource = loginSource,
            clientSecret = clientSecret,
            agentType = agentType,
            authRedirectOverrideUrl = authRedirectOverrideUrl,
            clientState = clientState,
            orgId = orgId,
        )
    }

    fun createSecretCookie(secret: UUID) = Cookie(
        name = CLIENT_SERVICE_COOKIE_NAME,
        value = secret.toString(),
        maxAge = authenticationConfig.secretCookieMaxAge,
        domain = deploymentConfig.hostName,
        path = "/api/login/exchangeV2",
        secure = authenticationConfig.secureCookies,
        httpOnly = true,
        extensions = mapOf(
            "SameSite" to authenticationConfig.sameSite,
        ),
    )

    fun createAgentTypeCookie(agentType: AgentType) = Cookie(
        name = AGENT_TYPE_COOKIE_NAME,
        value = agentType.enumValue,
    )

    suspend fun <T : OAuthApiType> exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: T,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID? = null,
    ): Identity {
        return identityAuthExchangeService.exchangeAuthCodeForIdentity(
            code = code,
            state = state,
            signedInPersonId = signedInPersonId,
            oAuthApiType = oAuthApiType,
            overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
            orgId = orgId,
            sessionId = sessionId,
        )
    }
}
