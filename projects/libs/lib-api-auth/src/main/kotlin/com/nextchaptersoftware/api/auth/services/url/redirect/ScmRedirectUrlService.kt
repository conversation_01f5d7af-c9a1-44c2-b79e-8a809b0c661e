package com.nextchaptersoftware.api.auth.services.url.redirect

import com.nextchaptersoftware.activation.ProviderActivation
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.config.OAuthConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthClientFactory
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import io.ktor.http.Parameters
import io.ktor.http.Url
import java.util.UUID

class ScmRedirectUrlService(
    private val scmAuthClientFactory: ScmAuthClientFactory,
    private val redirectAuthOverrideService: RedirectAuthOverrideService,
    private val providerActivation: ProviderActivation,
) : RedirectUrlService {

    override suspend fun buildRedirectUrl(
        oAuthApiType: OAuthApiType,
        nonce: UUID,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        state: String?,
        targetIdentityId: IdentityId?,
        skipAccountLinking: Boolean?,
        extraParameters: Parameters?,
        oAuthConfig: OAuthConfig?,
    ): Url {
        val scm = when (oAuthApiType) {
            is Scm -> oAuthApiType
            else -> error("Unexpected provider type")
        }
        expectSignInCapableProvider(scm)

        val redirectUrl = redirectAuthOverrideService.getRedirectUrl(
            provider = scm.provider,
            authRedirectOverrideUrl = authRedirectOverrideUrl,
        )

        return scmAuthClientFactory.from(
            oAuthApiType = oAuthApiType,
            oAuthConfig = oAuthConfig,
        ).buildAuthorizationRequest(
            state = OAuthState(
                nonce = nonce,
                provider = scm.provider.asApiModel(),
                enterpriseProviderId = scm.providerEnterpriseId?.value,
                ssoProviderId = null,
                clientState = clientState,
                state = state,
                targetIdentityId = targetIdentityId?.value,
                skipAccountLinking = skipAccountLinking,
            ).encode().urlSafeBase64Encode(),
            redirectUrl = redirectUrl,
            extraParameters = extraParameters,
        )
    }

    private fun expectSignInCapableProvider(scm: Scm) {
        if (!scm.provider.isSignInCapable) {
            throw BadRequestException("Expected a sign-in capable provider")
        }
        if (!providerActivation.isActive(scm.provider)) {
            throw BadRequestException("Provider is not active")
        }
    }
}
