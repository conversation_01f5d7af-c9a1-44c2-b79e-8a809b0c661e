package com.nextchaptersoftware.api.auth.services.url.redirect

import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.config.OAuthConfig
import com.nextchaptersoftware.db.models.IdentityId
import io.ktor.http.Parameters
import io.ktor.http.Url
import java.util.UUID

interface RedirectUrlService {
    suspend fun buildRedirectUrl(
        oAuthApiType: OAuthApiType,
        nonce: UUID,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        state: String?,
        targetIdentityId: IdentityId? = null,
        skipAccountLinking: Boolean? = null,
        extraParameters: Parameters? = null,
        oAuthConfig: OAuthConfig? = null,
    ): Url
}
