package com.nextchaptersoftware.api.auth.services.url.redirect

import com.nextchaptersoftware.api.auth.services.client.ProviderAuthClientFactory
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.config.OAuthConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import io.ktor.http.Parameters
import io.ktor.http.Url
import java.util.UUID

class ProviderRedirectUrlService(
    private val providerAuthClientFactory: ProviderAuthClientFactory,
) : RedirectUrlService {

    override suspend fun buildRedirectUrl(
        oAuthApiType: OAuthApiType,
        nonce: UUID,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        state: String?,
        targetIdentityId: IdentityId?,
        skipAccountLinking: Boolean?,
        extraParameters: Parameters?,
        oAuthConfig: OAuthConfig?,
    ): Url {
        val provider = when (oAuthApiType) {
            is Provider -> oAuthApiType
            else -> error("Unexpected provider type")
        }

        return providerAuthClientFactory.from(
            oAuthApiType = oAuthApiType,
            oAuthConfig = oAuthConfig,
        ).buildAuthorizationRequest(
            state = OAuthState(
                nonce = nonce,
                provider = provider.asApiModel(),
                clientState = clientState,
                state = state,
                targetIdentityId = targetIdentityId?.value,
                skipAccountLinking = skipAccountLinking,
            ).encode().urlSafeBase64Encode(),
            redirectUrl = authRedirectOverrideUrl,
            extraParameters = extraParameters,
        )
    }
}
