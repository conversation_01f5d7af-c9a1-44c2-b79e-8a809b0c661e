package com.nextchaptersoftware.api.auth.services.url.login

import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.converters.SamlIdentityProviderExtensions.asApiProvider
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.config.DeploymentConfig
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.URLBuilder
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.server.util.url
import java.util.UUID

class LoginUrlService(
    private val deploymentConfig: DeploymentConfig,
) {
    fun buildLoginUrl(
        loginSource: LoginSource,
        clientSecret: UUID?,
        agentType: AgentType?,
        authRedirectOverrideUrl: String?,
        clientState: String?,
        orgId: OrgId? = null,
        targetIdentityId: IdentityId? = null,
        skipAccountLinking: Boolean? = null,
    ): Url {
        return build(
            clientSecret = clientSecret,
            agentType = agentType,
            authRedirectOverrideUrl = authRedirectOverrideUrl,
            clientState = clientState,
            targetIdentityId = targetIdentityId,
            skipAccountLinking = skipAccountLinking,
        ) {
            when (loginSource) {
                is DataIntegrationLoginSource -> {
                    orgId?.also { parameters["teamId"] = orgId.toString() }
                    pathSegments = listOf(
                        "api",
                        "login",
                        loginSource.provider.asApiModel().enumValue,
                    )
                }

                is SamlLoginSource -> {
                    parameters["ssoProviderId"] = loginSource.samlIdpMetadata.id.toString()
                    pathSegments = listOf(
                        "api",
                        "login",
                        loginSource.samlIdpMetadata.samlIdentityProvider.asApiProvider().enumValue,
                    )
                }

                is ScmLoginSource -> {
                    if (loginSource.scm is Scm.OnPremise) {
                        parameters["enterpriseProviderId"] = loginSource.scm.enterpriseId.toString()
                    }
                    pathSegments = listOf(
                        "api",
                        "login",
                        loginSource.scm.provider.asApiModel().enumValue,
                    )
                }
            }
        }
    }

    private fun build(
        clientSecret: UUID?,
        agentType: AgentType?,
        authRedirectOverrideUrl: String?,
        clientState: String?,
        targetIdentityId: IdentityId?,
        skipAccountLinking: Boolean?,
        config: URLBuilder.() -> Unit,
    ): Url {
        return url {
            host = deploymentConfig.hostName
            protocol = when (deploymentConfig.enableTLS) {
                true -> URLProtocol.HTTPS
                false -> URLProtocol.HTTP
            }
            port = deploymentConfig.port
            clientSecret?.let { parameters["clientSecret"] = it.toString() }
            agentType?.let { parameters["agentType"] = it.name }
            authRedirectOverrideUrl?.let { parameters["overrideRedirectUrl"] = it }
            clientState?.let { parameters["clientState"] = it }
            targetIdentityId?.let { parameters["targetIdentityId"] = it.value.toString() }
            skipAccountLinking?.let { parameters["skipAccountLinking"] = it.toString() }
            this.config()
        }.asUrl
    }
}
