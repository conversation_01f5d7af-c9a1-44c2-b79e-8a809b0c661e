package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.api.services.PersonUpsertService
import com.nextchaptersoftware.cas.CASKey
import com.nextchaptersoftware.cas.RedisCAS
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.identityStore
import com.nextchaptersoftware.ktor.UnauthorizedException
import io.ktor.http.Url
import java.util.UUID

class SSOIdentityAuthExchangeService(
    private val samlCodeExchangeStore: RedisCAS,
    private val personUpsertService: PersonUpsertService,
) : IdentityAuthExchangeService<Provider> {
    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Provider,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        return samlCodeExchangeStore.retrieve(CASKey(code))?.let(IdentityId::fromString)?.let { identityId ->
            identityStore.findById(identityId = identityId)?.let { identity ->
                personUpsertService.upsertPerson(identity = identity, currentlySignedInPersonId = null)
                identityStore.findById(identityId = identityId)
            } ?: throw UnauthorizedException()
        } ?: throw UnauthorizedException()
    }
}
