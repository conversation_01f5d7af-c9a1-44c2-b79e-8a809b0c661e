package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.Url
import java.util.UUID

class ProviderIdentityAuthExchangeService(
    private val ssoIdentityAuthExchangeService: SSOIdentityAuthExchangeService,
    private val slackIdentityAuthExchangeService: SlackIdentityAuthExchangeService,
) : IdentityAuthExchangeService<Provider> {
    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Provider,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        return when (oAuthApiType) {
            Provider.Slack ->
                slackIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
                    code = code,
                    state = state,
                    signedInPersonId = signedInPersonId,
                    oAuthApiType = oAuthApiType,
                    overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                    orgId = orgId,
                    sessionId = sessionId,
                )

            Provider.Asana,
            Provider.Aws,
            Provider.BitbucketPipelines,
            Provider.Buildkite,
            Provider.CircleCI,
            Provider.Coda,
            Provider.Confluence,
            Provider.ConfluenceDataCenter,
            Provider.CustomIntegration,
            Provider.GitHubActions,
            Provider.GitLabPipelines,
            Provider.GoogleDrive,
            Provider.GoogleDriveWorkspace,
            Provider.Jira,
            Provider.JiraDataCenter,
            Provider.Linear,
            Provider.Notion,
            Provider.StackOverflowTeams,
            Provider.Unblocked,
            Provider.Web,
                -> error("not applicable")

            Provider.AzureDevOps,
            Provider.Bitbucket,
            Provider.BitbucketDataCenter,
            Provider.GitHub,
            Provider.GitHubEnterprise,
            Provider.GitLab,
            Provider.GitLabSelfHosted,
                -> error("Use ScmIdentityAuthExchangeService instead")

            Provider.AwsIdentityCenter,
            Provider.GenericSaml,
            Provider.GoogleWorkspace,
            Provider.MicrosoftEntra,
            Provider.Okta,
            Provider.PingOne,
                ->
                ssoIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
                    code = code,
                    state = state,
                    signedInPersonId = signedInPersonId,
                    oAuthApiType = oAuthApiType,
                    overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                    orgId = orgId,
                    sessionId = sessionId,
                )
        }
    }
}
