package com.nextchaptersoftware.api.auth.services.identity

import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.services.PersonUpsertService
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.years
import com.nextchaptersoftware.log.sensitive.errorSensitiveAsync
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import io.ktor.util.decodeBase64String
import java.util.UUID
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

class ScmIdentityAuthExchangeService(
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    private val identityStore: IdentityStore = Stores.identityStore,
    private val personUpsertService: PersonUpsertService,
    private val restrictedAccessService: RestrictedAccessServiceInterface,
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val scmUserApiFactory: ScmUserApiFactory,
    private val userSecretServiceResolver: UserSecretServiceResolver,
) : IdentityAuthExchangeService<Scm> {

    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Scm,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        val authApi = scmAuthApiFactory.getApi(orgId = orgId, oAuthApiType = oAuthApiType)

        val authUser = run {
            val oauthTokens = authApi.exchangeForToken(
                context = OAuthTokenExchangeContext(
                    code = code,
                    state = state,
                    overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                ),
            ).oAuthTokens
            scmUserApiFactory.getApiFromTokens(
                orgId = orgId,
                tokens = oauthTokens,
                scm = oAuthApiType,
            ).use {
                it.user()
            }
        }

        val enterpriseAuthority = when (oAuthApiType) {
            is Scm.OnPremise -> enterpriseAppConfigStore.getById(oAuthApiType.enterpriseId, oAuthApiType.provider).authority
            else -> null
        }

        restrictedAccessService.checkUserAccess(
            provider = oAuthApiType.provider,
            externalId = authUser.externalId,
            enterpriseAuthority = enterpriseAuthority,
        )

        val identity = upsertIdentity(scm = oAuthApiType, scmAuthUser = authUser).also {
            if (it.primaryEmail == null) {
                LOGGER.errorSensitiveAsync(
                    "identityId" to it.id,
                    sensitiveFields = mapOf("emails" to it.emails.joinToString()),
                ) { "Primary email of sign-in identity is missing" }
            }
        }

        // Skip account linking if the skipAccountLinking flag is set
        if (state?.decodeBase64String()?.decode<OAuthState>()?.skipAccountLinking != true) {
            personUpsertService.upsertPerson(identity = identity, currentlySignedInPersonId = signedInPersonId)
        }

        // Refresh the identity because the person upsert may have changed the identity's personId
        return identityStore.findById(identityId = identity.id).required()
    }

    private suspend fun upsertIdentity(scm: Scm, scmAuthUser: ScmAuthUser): Identity {
        val encryptedTokens = userSecretServiceResolver
            .resolve(scm.provider)
            .encryptTokens(scmAuthUser.oauthTokens)

        return identityStore.upsert(
            provider = scm.provider,
            externalId = scmAuthUser.externalId,
            externalTeamId = scm.uniqueSignature,
            username = scmAuthUser.username,
            displayName = scmAuthUser.displayName,
            primaryEmail = scmAuthUser.primaryEmail,
            avatarUrl = scmAuthUser.avatarUrl,
            htmlUrl = scmAuthUser.htmlUrl,
            emails = scmAuthUser.emails,
            isBot = null,
            rawAccessToken = encryptedTokens.rawAccessToken.value,
            rawRefreshToken = encryptedTokens.rawRefreshToken?.value,
            accessTokenExpiresAt = encryptedTokens.accessTokenExpiresAt,
            refreshTokenExpiresAt = encryptedTokens.refreshTokenExpiresAt ?: Instant.nowWithMicrosecondPrecision().plus(1.years),
            accessTokenScope = encryptedTokens.accessTokenScope,
            promptToReconnect = false,
        )
    }
}
