alarms {
    triageLatency {
        isEnabled: false
        threshold: 30m
        interval: 30m
    }
}

auth {
    tokenIssuer: null
    tokenPublicKey: null
}

buildkite {
    tokenDocumentationUrl: "https://docs.getunblocked.com/data-sources/buildkite"
    webhookUrl: ""
}

cache {
    table {
        tableName: "ciTriageCache"
        region: us-west-2
    }
    ttl: 14d
}

circleci {
    tokenDocumentationUrl: "https://docs.getunblocked.com/data-sources/circleci"
    webhookUrl: ""

    # CircleCI does not expose the workflow status in the job payload
    # To ensure that the triage pipeline receives the correct workflow status
    # a small delay in introduced when processing terminal events
    webhookEventDelay: 15s
}

features {
    enableBitbucketPipelines: true
    enableGitLabPipelines: true
    enableScmBitbucket: true
    enableScmGitLab: true
    enableScmGitLabSelfHosted: true
}

github {
    # By default, only allow these check suites
    checkSuiteAllowList: [
        "CircleCI Checks"
        "GitHub Actions"
    ],

    # By default, only allow these check runs
    checkRunAllowList: [
        "GitHub Actions"
    ]

    # By default, allow every repository. These will be populated for LOCAL
    repoDenyList: null,

    # GitHub needs a bit of extra time to start the workflows, otherwise resolving will silently fail
    webhookEventDelay: 15s
}

triage {
    pipelineTimeout: 6m
    # By default, always delay pipeline exectuion
    pipelineDelay: 30s
    # By default, always abort on minimal logs
    abortWhenLogsEmpty: true
    abortWhenLogsSmall: true
}

webhookName: "Unblocked"
