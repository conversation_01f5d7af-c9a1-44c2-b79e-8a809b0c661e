alarms {
    triageLatency {
        isEnabled: true
    }
}

auth {
    tokenIssuer: "prod.getunblocked.com"
    tokenPublicKey: "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQ0lqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FnOEFNSUlDQ2dLQ0FnRUExT29uckw4NXNiU0sremdiYk5UcwprSituY2tuRVNEZllJTFVjTlJCblRlSjdEc1Q2Vmw1TzB6ckZBRkhOelFzc1NZVkpyaTg4Qlpjcncxb2Q4Z2dqCnNWekxzYkl3WTdyVTdWdDlpTVYwbDdScnIrWDk1a0VQU1ZCS2xHWkNKRFJBOVFta2dUVTRXSnQ5YVVBWVdBRnoKL2ROWG5TeHY0UmdwZjV4Z3o0N2xzd2lydGJGVkZZOTVPMU13YzlBdFZ6VTh4bEo2QjkzVGhkV0dET210OHBFagpGQ1pWU2c4dWFReEIybmYxVDNibWlhbkordnBjV1Nhb3JXbDhwd3ZaN1RNWlRBaUxtbGRKUHFhVHErWFoxclpoCld5MnVDa0ZRMGMyOHBqc1pWbk9uSHVvdnFjQlduRzVXT0t2RHRGY0svRTFNcTRqWmpFNjUzSzhSTGhvaTNrQUsKdFNXZEdabHg0NFdVK1VHWWoxTVRXZ01UTDhERFIvK21kN1diRllBcFZOSG51ZHpPR0txUUhjV2NXMGorSlRpZgptSFFqMjQ1czlhdzFVK0d0U2xqSW1WY0xEMVBubWp3anhKUGQ3OWJJbkk2Z2VTM1QzWTFMMEZQSU4wQ2h3MEVlCjZCQVRaN2dQRjNzMnlWSm5rRkRCVS9pQ0szQmRuNEltZmMxYmxYOHllREJkekNMc2dWSDBrdW11SWFUUmtZcDIKSUpMOUF4WTlSNTVCUURSajFSSU1wbEdpWnd4K2lDR3NqMGVHWG1nMU5UK1pBa05LN1JwcGpleHdxMDdoc0czegpJWkhNRFZOcFhjWFNFOEtNODBLZjRHT05qSnJEaTljdnBmU3ZicnRYN1JoTW5oc1JIcCtFOTc1YTNrV2dCS3lSCm94SW1ySFF4a0VwSFlmZG5CazhqalUwQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo="
}

buildkite {
    webhookUrl: "https://getunblocked.com/api/hooks/buildkite"
}

circleci {
    webhookUrl: "https://getunblocked.com/api/hooks/circleci"
}

features {
    enableBitbucketPipelines: false
    enableGitLabPipelines: false
    enableScmBitbucket: false
    enableScmGitLab: false
    enableScmGitLabSelfHosted: false
}

webhookName: "Unblocked"
