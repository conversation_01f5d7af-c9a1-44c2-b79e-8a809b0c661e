package com.nextchaptersoftware.ci.config

import com.nextchaptersoftware.config.ConfigLoader
import com.nextchaptersoftware.config.DynamoDBConfig
import kotlin.time.Duration
import kotlinx.serialization.Serializable

@Serializable
data class AlarmsConfig(
    val triageLatency: AlarmTriageLatencyConfig,
)

@Serializable
data class AlarmTriageLatencyConfig(
    val isEnabled: Boolean,
    val threshold: Duration,
    val interval: Duration,
)

@Serializable
data class AuthConfig(
    val tokenIssuer: String,
    /** CI-specific RSA public key used to verify that CI tokens are authentic */
    val tokenPublicKey: String,
)

@Serializable
data class BuildkiteConfig(
    val tokenDocumentationUrl: String,
    val webhookUrl: String,
)

@Serializable
data class CacheConfig(
    val table: DynamoDBConfig,
    val ttl: Duration,
)

@Serializable
data class CircleCIConfig(
    val tokenDocumentationUrl: String,
    val webhookEventDelay: Duration?,
    val webhookUrl: String,
)

@Serializable
data class FeatureConfig(
    // CI systems
    val enableBitbucketPipelines: Boolean,
    val enableGitLabPipelines: Boolean,

    // SCM systems
    val enableScmBitbucket: Boolean,
    val enableScmGitLab: Boolean,
    val enableScmGitLabSelfHosted: Boolean,
)

@Serializable
data class GitHubConfig(
    val checkRunAllowList: Set<String>?,
    val checkSuiteAllowList: Set<String>?,
    val repoDenyList: Set<String>?,
    val webhookEventDelay: Duration?,
)

@Serializable
data class TriageConfig(
    val abortWhenLogsEmpty: Boolean,
    val abortWhenLogsSmall: Boolean,
    val pipelineDelay: Duration,
    val pipelineTimeout: Duration,
)

@Serializable
data class CIConfig(
    val alarms: AlarmsConfig,
    val auth: AuthConfig,
    val buildkite: BuildkiteConfig,
    val cache: CacheConfig,
    val circleci: CircleCIConfig,
    val features: FeatureConfig,
    val github: GitHubConfig,
    val triage: TriageConfig,
    val webhookName: String,
) {
    companion object {
        val INSTANCE = ConfigLoader.loadConfig<CIConfig>(scope = "ci")

        fun getTestInstance(
            overrideEnvironment: String? = null,
            overrideUser: String? = null,
        ) = ConfigLoader.loadConfig<CIConfig>(
            scope = "ci",
            overrideEnvironment = overrideEnvironment,
            overrideUser = overrideUser,
        )
    }
}
