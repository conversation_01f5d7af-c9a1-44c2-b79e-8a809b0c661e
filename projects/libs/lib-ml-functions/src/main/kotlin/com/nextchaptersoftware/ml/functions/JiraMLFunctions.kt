package com.nextchaptersoftware.ml.functions

import com.nextchaptersoftware.data.preset.DataSourcePresetConfiguration
import com.nextchaptersoftware.datasources.JiraAccessService
import com.nextchaptersoftware.db.models.IssueStatus
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.JiraProjectStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.filter.DataSourceAccessControlFilterFactory
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.ml.doc.converter.JiraDocConverter
import com.nextchaptersoftware.ml.functions.core.MLFunction
import com.nextchaptersoftware.ml.functions.core.MLFunctionDescription
import com.nextchaptersoftware.ml.functions.core.MLFunctionExecutionContext
import com.nextchaptersoftware.ml.services.MLFunctionMemberService
import com.nextchaptersoftware.repo.RepoAccessResult
import com.nextchaptersoftware.utils.asLocalYearMonthDay
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class JiraMLFunctions(
    private val dsacFilterFactory: DataSourceAccessControlFilterFactory,
    private val jiraAccessService: JiraAccessService,
    private val jiraDocConverter: JiraDocConverter,
    private val memberStore: MemberStore = Stores.memberStore,
    private val jiraProjectStore: JiraProjectStore = Stores.jiraProjectStore,
    private val mlFunctionMemberService: MLFunctionMemberService,
) : MLFunction {

    override fun isIntegrationEnabled(enabledProviders: Set<Provider>): Boolean {
        return enabledProviders.any { it in setOf(Provider.Jira, Provider.JiraDataCenter) }
    }

    @MLFunctionDescription(
        description = """
            Retrieves Jira issues matching specified filter criteria.

            Call this function to find Jira issues based on project, status, assignee, creator, or time-based filters.

            When to use:
            - For queries about multiple Jira issues matching certain criteria
            - When filtering issues by status, assignee, creation date, or update time
            - For questions about recent activity

            Do NOT use:
            - For retrieving a single issue by its key (use getJiraIssueDetailsFromKey() instead)

            Usage guidance:
            - When a user asks about issues completed within a date range, they are referring to the resolved date and *not* the created date
            - When a user says "issues I completed" or "tickets that I completed" they want issues that were resolved by them

            Example queries:
            - "Show open tickets assigned to Sarah"
            - "Find bugs created in the last week"
            - "List all issues I resolved yesterday"
            - "Get jiras created by Tom this week"
            - "What has Jane done in the last month?"
            - "Show me all issues I completed in the month of May"
            - "Can you show me Jira tickets that I completed in the month of May?"
            - "What work is in progress for RELENG right now?"
            - "Show me all tickets in the Release Engineering project that are assigned to me"

            Arguments:
            - projectId: Filter by jira project (JiraProjectId, You MUST ONLY use getJiraProjectIdForNameOrKey() to get the ID)
            - issueStatus: Filter by status (one of: "InProgress", "Completed", "ToDo")
            - assigneeId: Filter by assignee (OrgMemberId, You MUST ONLY use getJiraMemberIdForName() to get the ID)
            - creatorId: Filter by creator/reporter (OrgMemberId, You MUST ONLY use getJiraMemberIdForName() to get the ID)
            - createdAfter/createdBefore: Filter by creation date (format: "YYYY-MM-DDTHH:MM:SSZ")
            - updatedAfter/updatedBefore: Filter by update date (format: "YYYY-MM-DDTHH:MM:SSZ")
            - resolvedAfter/resolvedBefore: Filter by resolution/completion date (format: "YYYY-MM-DDTHH:MM:SSZ")

            Returns a list of matching Jira issues.
        """,
    )
    @Suppress("CyclomaticComplexMethod")
    suspend fun listJiraIssuesWithFilters(
        context: MLFunctionExecutionContext,
        projectId: JiraProjectId?,
        issueStatus: String?,
        assigneeId: OrgMemberId?,
        creatorId: OrgMemberId?,
        createdAfter: String?,
        createdBefore: String?,
        updatedAfter: String?,
        updatedBefore: String?,
        resolvedAfter: String?,
        resolvedBefore: String?,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "issueStatus" to issueStatus,
        "assigneeId" to assigneeId,
        "creatorId" to creatorId,
        "createdAfter" to createdAfter,
        "createdBefore" to createdBefore,
        "updatedAfter" to updatedAfter,
        "updatedBefore" to updatedBefore,
        "resolvedAfter" to resolvedAfter,
        "resolvedBefore" to resolvedBefore,
    ) {
        LOGGER.debugAsync { "JiraMLFunctions::listJiraIssuesWithFilters" }

        val createdAfterInstant = createdAfter?.let { Instant.parse(it) }
        val createdBeforeInstant = createdBefore?.let { Instant.parse(it) }
        val updatedAfterInstant = updatedAfter?.let { Instant.parse(it) }
        val updatedBeforeInstant = updatedBefore?.let { Instant.parse(it) }
        val resolvedAfterInstant = resolvedAfter?.let { Instant.parse(it) }
        val resolvedBeforeInstant = resolvedBefore?.let { Instant.parse(it) }
        val issueStatusType = issueStatus?.let { IssueStatus.fromString(it) }

        val issues = jiraAccessService.findJiraIssuesWithFilters(
            orgId = context.orgId,
            projectId = projectId,
            issueStatus = issueStatusType,
            assigneeId = assigneeId,
            creatorId = creatorId,
            createdAfter = createdAfterInstant,
            createdBefore = createdBeforeInstant,
            updatedAfter = updatedAfterInstant,
            updatedBefore = updatedBeforeInstant,
            resolvedAfter = resolvedAfterInstant,
            resolvedBefore = resolvedBeforeInstant,
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)
            .let { documents ->
                dsacFilterFactory.get(
                    repoAccess = RepoAccessResult.None,
                    dsacContext = context.dsacContext,
                    dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
                ).filterDocuments(
                    orgId = context.orgId,
                    documents = documents,
                )
            }

        val highLevelSummary = """
            |### Jira Issue List
            |There are ${mlDocuments.size} Jira issues
        """.trimMargin().trim().plus(
            listOfNotNull(
                projectId?.let { getJiraProjectNameForId(context.orgId, it) }?.let { "in project '$it'" },
                issueStatusType?.let { "with $it status" },
                assigneeId?.let { getJiraMemberNameForOrgId(it) }?.let { "assigned to $it" },
                creatorId?.let { getJiraMemberNameForOrgId(it) }?.let { "created by $it" },
                createdAfterInstant?.let { "created after ${it.asLocalYearMonthDay}" },
                createdBeforeInstant?.let { "created before ${it.asLocalYearMonthDay}" },
                updatedAfterInstant?.let { "updated after ${it.asLocalYearMonthDay}" },
                updatedBeforeInstant?.let { "updated before ${it.asLocalYearMonthDay}" },
                resolvedAfterInstant?.let { "resolved after ${it.asLocalYearMonthDay}" },
                resolvedBeforeInstant?.let { "resolved before ${it.asLocalYearMonthDay}" },
            ).joinToString(prefix = " ", postfix = "."),
        )

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve the details of a specific Jira issue or epic identified by its key or URL.

            Example of Jira URLs:
            - "https://company.com/jira/browse/PROJ-123"
            - "https://company.atlassian.net/browse/PROJ-456"

            Usage guidance:
            - When a Jira issue key is provided without a URL, such as "PROJ-123" or "PROJ-456", specify the 'issueKey' as the issue key.
            - When a Jira URL is provided, the function can determine the provider from the URL format. For example:
                - If the URL is "https://company.atlassian.net/browse/PROJ-123", set the 'issueKey' to "PROJ-123".

            The user can ask questions like:
            - "What are the details of the issue with the URL https://jira.example.com/browse/PROJ-123?"
            - "What are the details of Jira issue PROJ-123?"
            - "What are the details of issue PROJ-456?"
            - "What are the details of epic PROJ-789?"

            Arguments:
            - issueKey: The Jira issue key or the key extracted from a Jira URL.

            This function returns the details of the Jira issue or epic identified by the issueKey or the URL.
        """,
    )
    suspend fun getJiraIssueDetailsFromKey(
        context: MLFunctionExecutionContext,
        issueKey: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "issueKey" to issueKey,
    ) {
        LOGGER.debugAsync { "JiraMLFunctions::getJiraIssueDetailsFromKey" }

        val orgId = context.orgId

        val issues = jiraAccessService.getJiraIssueByKey(
            orgId = orgId,
            dsacContext = context.dsacContext,
            issueKey = issueKey.trim().uppercase(),
        )?.let { listOf(it) } ?: emptyList()

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = orgId, issues = issues)

        val highLevelSummary = """
            |### Jira Issue Details
            |There are ${mlDocuments.size} Jira issues matching issue key '$issueKey'.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve a list of Jira issues for a given epic identified by its key or URL.
            When a URL is provided, the caller must extract the key from the URL and pass it as the 'epicKey' argument.

            Usage:
            The user may ask questions like:
            - "List Jira issues for the epic PROJ-123"
            - "List Jira issues for the epic URL https://company.atlassian.net/browse/PROJ-123"

            Arguments:
            - epicKey (Required): The key of the epic to filter by, for example, "PROJ-123".

            This function returns a list of Jira issues for a given epic key.
        """,
    )
    suspend fun listJiraIssuesForEpicKey(
        context: MLFunctionExecutionContext,
        epicKey: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "epicKey" to epicKey,
    ) {
        val issues = jiraAccessService.getJiraIssuesByEpicKey(
            orgId = context.orgId,
            dsacContext = context.dsacContext,
            epicKey = epicKey.trim(),
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)

        val highLevelSummary = """
            |### Jira Issues For Epic
            |There are ${mlDocuments.size} Jira issues for Jira epic key '$epicKey'.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve a list of Jira issues for a given Jira board identified by name.

            Usage:
            The user may ask questions like:
            - "List Jira issues for the Release Planning board"
            - "Summarize Jira issues for the Sprint Planning board"
            - "Show me all the Jira issues for the Backlog board"

            Arguments:
            - boardName (Required): The name of the Jira board to filter by, for example, "Release Planning".

            This function returns a list of Jira issues for the given Jira board name.
        """,
    )
    suspend fun listJiraIssuesForBoardName(
        context: MLFunctionExecutionContext,
        boardName: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "boardName" to boardName,
    ) {
        val issues = jiraAccessService.getJiraIssueIdsByBoardName(
            orgId = context.orgId,
            dsacContext = context.dsacContext,
            boardName = boardName.trim(),
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)

        val highLevelSummary = """
            |### Jira Issues For Board
            |There are ${mlDocuments.size} Jira issues for Jira board '$boardName'.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve a list of Jira issues for a given Jira sprint identified by name.

            Usage:
            The user may ask questions like:
            - "List Jira issues for the Sprint 12 sprint"
            - "Summarize Jira issues for the March 2022 sprint"
            - "Show me all the Jira issues for the Sprint 1 sprint"

            Arguments:
            - sprintName (Required): The name of the Jira sprint to filter by, for example, "Sprint 12".

            This function returns a list of Jira issues for the given Jira sprint name.
        """,
    )
    suspend fun listJiraIssuesForSprintName(
        context: MLFunctionExecutionContext,
        sprintName: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "sprintName" to sprintName,
    ) {
        val issues = jiraAccessService.getJiraIssueIdsBySprint(
            orgId = context.orgId,
            dsacContext = context.dsacContext,
            sprintName = sprintName.trim(),
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)

        val highLevelSummary = """
            |### Jira Issues For Sprint
            |There are ${mlDocuments.size} Jira issues for Jira sprint '$sprintName'.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve a list of Jira issues for the current Jira sprint in a board identified by name.

            Usage:
            The user may ask questions like:
            - "List Jira issues for the current sprint on the Integrations board"
            - "List all issues for the current sprint on the UN board"
            - "List Jira issues for the latest sprint on the Features board"
            - "List issues for the active sprint on the Features board"
            - "Summarize issues for the current sprint on the Backlog board"
            - "Summarize Jira issues for the latest sprint on the features board"
            - "Show all issues for the current sprint on the Development board."
            - "Retrieve Jira issues for the ongoing sprint on the API Team board."
            - "Display tasks from the current sprint under the Backend board."
            - "Fetch all issues in the current sprint for the Frontend board."
            - "List all Jira tickets from the current sprint on the Mobile App board."
            - "Pull Jira issues from the ongoing sprint for the Platform board."
            - "Get a list of Jira issues assigned to the current sprint on the UX/UI board."
            - "Find all Jira issues related to the current sprint in the DevOps board."
            - "Load Jira issues from the current sprint onto the Growth Team board."
            - "Retrieve and display all Jira tickets for the current sprint in the Product board."

            Arguments:
            - boardName (Required): The name of the board to filter by, for example, "Integrations", "Development", "Platform".

            This function returns a list of Jira issues in the current sprint for the given Jira board name.
        """,
    )
    suspend fun listJiraIssuesForCurrentSprint(
        context: MLFunctionExecutionContext,
        boardName: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "boardName" to boardName,
    ) {
        val issues = jiraAccessService.getJiraIssueIdsForCurrentSprint(
            orgId = context.orgId,
            dsacContext = context.dsacContext,
            boardName = boardName.trim(),
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)

        val highLevelSummary = """
            |### Jira Issues For Sprint
            |There are ${mlDocuments.size} Jira issues for the current sprint on the '$boardName' board.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to retrieve Jira issues from a Jira filter URL.

            This function handles Jira filter URLs of the format:
            - "https://company.atlassian.net/issues?filter=20012"
            - "https://jira.company.com/issues?filter=12345"

            Usage:
            The user may ask questions like:
            - "What issues are in this filter: https://company.atlassian.net/issues?filter=20012"
            - "Show me the issues from filter https://jira.company.com/issues?filter=12345"
            - "Summarize the issues in this Jira filter URL"

            Arguments:
            - filterUrl (Required): The complete Jira filter URL containing the filter parameter.

            This function extracts the filter ID from the URL and returns all issues in that filter.
        """,
    )
    suspend fun getJiraIssuesFromFilterUrl(
        context: MLFunctionExecutionContext,
        filterUrl: String,
    ): MLFunctionResult = withLoggingContextAsync(
        "orgId" to context.orgId,
        "orgMemberId" to context.orgMemberId,
        "filterUrl" to filterUrl,
    ) {
        LOGGER.debugAsync { "JiraMLFunctions::getJiraIssuesFromFilterUrl" }

        val filterId = extractFilterIdFromUrl(filterUrl.trim())
            ?: return@withLoggingContextAsync MLFunctionResult.renderMLFunctionResult(
                "### Invalid Jira Filter URL\nThe provided URL does not contain a valid filter parameter.",
                emptyList(),
            )

        val issues = jiraAccessService.getJiraIssuesByFilterId(
            orgId = context.orgId,
            dsacContext = context.dsacContext,
            filterId = filterId,
        )

        val mlDocuments = jiraDocConverter.asMLTypedDocuments(orgId = context.orgId, issues = issues)
            .let { documents ->
                dsacFilterFactory.get(
                    repoAccess = RepoAccessResult.None,
                    dsacContext = context.dsacContext,
                    dataSourcePresetConfiguration = DataSourcePresetConfiguration.All,
                ).filterDocuments(
                    orgId = context.orgId,
                    documents = documents,
                )
            }

        val highLevelSummary = """
            |### Jira Filter Results
            |There are ${mlDocuments.size} Jira issues in filter $filterId.
        """.trimMargin()

        MLFunctionResult.renderMLFunctionResult(highLevelSummary, mlDocuments)
    }

    @MLFunctionDescription(
        """
            Call this function to get the Jira member id for a given Jira user name.
            If the user is asking about themselves, using words like "I/me/my" the question, pass "me" as the jiraUserName.

            The resultName of this function MUST be the jiraUserName in camelcase with the suffix "Id".
            For example if the jiraUserName is "John Doe", then the resultName MUST be "johnDoeId".
        """,
    )
    suspend fun getJiraMemberIdForName(context: MLFunctionExecutionContext, jiraUserName: String): OrgMemberId? {
        return mlFunctionMemberService.getMemberIdForNameByProvider(context, jiraUserName, setOf(Provider.Jira, Provider.JiraDataCenter))
    }

    private suspend fun getJiraMemberNameForOrgId(orgMemberId: OrgMemberId): String? {
        return memberStore.getMemberNameForOrgId(orgMemberId, setOf(Provider.Jira))
    }

    @MLFunctionDescription(
        """
            Call this function to get the Jira project id for a given Jira project name or key.

            The resultName of this function MUST be the nameOrKey in camelcase with the suffix "Id".
            For example if the nameOrKey is "My Project", then the resultName MUST be "myProjectId".
        """,
    )
    suspend fun getJiraProjectIdForNameOrKey(context: MLFunctionExecutionContext, nameOrKey: String): JiraProjectId? {
        return jiraProjectStore.getJiraProjectIdByNameOrKeyMatch(orgId = context.orgId, name = nameOrKey)
    }

    private suspend fun getJiraProjectNameForId(orgId: OrgId, projectId: JiraProjectId): String? {
        return jiraProjectStore.findById(orgId = orgId, id = projectId)?.projectName
    }

    /**
     * Extracts filter ID from Jira filter URLs.
     * Supports URLs like: https://company.atlassian.net/issues?filter=20012
     */
    internal fun extractFilterIdFromUrl(url: String): String? = runSuspendCatching {
        val regex = Regex("""[?&]filter=([^&]+)""")
        regex.find(url)?.groupValues?.get(1)?.takeIf { it.isNotBlank() }
    }.getOrNull()
}
