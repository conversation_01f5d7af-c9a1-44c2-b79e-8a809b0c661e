package com.nextchaptersoftware.ml.functions

import com.nextchaptersoftware.datasources.ThreadAccessService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.ml.services.MLFunctionMemberService
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock

class LinearMLFunctionsTest {

    @Test
    fun `isIntegrationEnabled should return true when Linear provider is enabled`() {
        val enabledProviders = setOf(Provider.Linear, Provider.GitHub)

        // Create a minimal instance just for testing this method
        val linearMLFunctions = LinearMLFunctions(
            memberStore = mock<MemberStore>(),
            mlFunctionMemberService = mock<MLFunctionMemberService>(),
            threadAccessService = mock<ThreadAccessService>(),
            threadInsightIndexContentService = mock<ThreadInsightIndexContentService>(),
        )

        assertThat(linearMLFunctions.isIntegrationEnabled(enabledProviders)).isTrue()
    }

    @Test
    fun `isIntegrationEnabled should return false when Linear provider is not enabled`() {
        val enabledProviders = setOf(Provider.GitHub, Provider.Slack)

        // Create a minimal instance just for testing this method
        val linearMLFunctions = LinearMLFunctions(
            memberStore = mock<MemberStore>(),
            mlFunctionMemberService = mock<MLFunctionMemberService>(),
            threadAccessService = mock<ThreadAccessService>(),
            threadInsightIndexContentService = mock<ThreadInsightIndexContentService>(),
        )

        assertThat(linearMLFunctions.isIntegrationEnabled(enabledProviders)).isFalse()
    }

    @Test
    fun `extractIssueKeyFromUrlOrKey should extract issue key from valid Linear URL`() {
        val linearMLFunctions = LinearMLFunctions(
            memberStore = mock<MemberStore>(),
            mlFunctionMemberService = mock<MLFunctionMemberService>(),
            threadAccessService = mock<ThreadAccessService>(),
            threadInsightIndexContentService = mock<ThreadInsightIndexContentService>(),
        )

        // Use reflection to access the private method for testing
        val method = LinearMLFunctions::class.java.getDeclaredMethod("extractIssueKeyFromUrlOrKey", String::class.java)
        method.isAccessible = true

        val testCases = mapOf(
            "https://linear.app/team-name/issue/ISSUE-123/title-of-the-issue" to "ISSUE-123",
            "https://linear.app/company/issue/ABC-456/some-title" to "ABC-456",
            "ISSUE-789" to "ISSUE-789", // Should return as-is for non-URLs
            "https://linear.app/invalid-url" to "https://linear.app/invalid-url", // Should return as-is for invalid URLs
        )

        testCases.forEach { (input, expected) ->
            val result = method.invoke(linearMLFunctions, input) as String
            assertThat(result).isEqualTo(expected)
        }
    }
}
