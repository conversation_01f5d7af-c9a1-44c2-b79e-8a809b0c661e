package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

interface ProviderIdentityCalls {

    suspend fun providerIdentityExchangeAuthCodeForIdentity(
        params: ProviderIdentityExchangeAuthCodeForIdentityParams,
    ): ProviderExchangeAuthCodeForIdentityResult

    @Serializable
    data class ProviderIdentityExchangeAuthCodeForIdentityParams(
        val code: String,
        val state: String?,
        @Contextual
        val signedInPersonId: PersonId?,
        @Contextual
        val orgId: OrgId?,
        val oAuthApiType: OAuthApiTypeSerializable,
        @Contextual
        val overrideOAuthRedirectUrl: Url?,
    )

    @Serializable
    data class ProviderExchangeAuthCodeForIdentityResult(
        @Contextual
        val identityId: IdentityId,
    ) {
        suspend fun asIdentity(): Identity = suspendedTransaction {
            IdentityDAO
                .findById(id = identityId)
                .let(::checkNotNull)
                .asDataModel()
        }
    }
}
