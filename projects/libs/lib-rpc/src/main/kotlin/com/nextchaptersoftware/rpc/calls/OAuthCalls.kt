package com.nextchaptersoftware.rpc.calls

import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.db.models.OrgId
import io.ktor.http.Url
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

/**
 * @see com.nextchaptersoftware.proxy.provider.rpc.handlers.OAuthHandler
 */
interface OAuthCalls {

    suspend fun oauthExchange(
        params: OAuthExchangeParams,
    ): OAuthExchangeResult

    @Serializable
    data class OAuthExchangeParams(
        val orgId: OrgId?,
        val provider: Provider,
        val context: OAuthTokenExchangeContext,
    )

    @Serializable
    data class OAuthExchangeResult(
        @Contextual
        val url: Url?,
    )
}
