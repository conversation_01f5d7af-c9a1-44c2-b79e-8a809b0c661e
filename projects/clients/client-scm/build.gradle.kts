plugins {
    kotlin("jvm")
    kotlin("plugin.serialization")
    id("com.expediagroup.graphql")
}

configurations {
    create("test")
}

dependencies {

    // GraphQL
    implementation(libs.expediagroup.graphql.ktor.client) {
        exclude("com.expediagroup:graphql-kotlin-client-jackson")
    }
    implementation(libs.expediagroup.graphql.ktor.client.serialization)

    // Client
    implementation(libs.bundles.ktor.client)

    testImplementation(testLibs.bundles.test.core)
    testImplementation(testLibs.bundles.test.ktor)

    // Utils Project
    implementation(project(":projects:clients:client-redis", configuration = "default"))
    implementation(project(":projects:libs:lib-auth", configuration = "default"))
    implementation(project(":projects:libs:lib-auth-secret", configuration = "default"))
    implementation(project(":projects:libs:lib-cache", configuration = "default"))
    implementation(project(":projects:libs:lib-ci-config", configuration = "default"))
    implementation(project(":projects:libs:lib-common", configuration = "default"))
    implementation(project(":projects:libs:lib-graphql", configuration = "default"))
    implementation(project(":projects:libs:lib-insider", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor-client-engine", configuration = "default"))
    implementation(project(":projects:libs:lib-ktor-client-org", configuration = "default"))
    implementation(project(":projects:libs:lib-log-sensitive", configuration = "default"))
    implementation(project(":projects:libs:lib-scm-config", configuration = "default"))
    implementation(project(":projects:libs:lib-trace-ktor", configuration = "default"))
    implementation(project(":projects:libs:lib-user-secret", configuration = "default"))
    implementation(project(":projects:models", configuration = "default"))

    testImplementation(testLibs.bundles.test.postgresql)
    testImplementation(testLibs.bundles.test.core)

    testImplementation(project(":projects:libs:lib-common", "test"))
    testImplementation(project(":projects:models", "test"))
}

val graphqlOutputSrcDir = layout.buildDirectory.dir("generated-graphql/src/main/kotlin")
sourceSets.getByName("main") {
    java.srcDir(graphqlOutputSrcDir)
}

tasks {
    compileKotlin {
        dependsOn(graphqlGenerateClient)
        source(graphqlOutputSrcDir)
    }

    // https://github.com/ExpediaGroup/graphql-kotlin/blob/master/website/docs/plugins/gradle-plugin-tasks.mdx
    graphqlGenerateClient {
        serializer.set(com.expediagroup.graphql.plugin.gradle.config.GraphQLSerializer.KOTLINX)
        packageName.set("com.nextchaptersoftware.clients.githubV4")
        val resources = project.layout.projectDirectory.dir("src/main/resources")
        schemaFile.set(resources.dir("github-graphql/schema").file("schema.graphql"))
        queryFileDirectory.set(resources.dir("github-graphql/queries"))
        outputDirectory.set(graphqlOutputSrcDir)
    }

    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlinx.serialization.ExperimentalSerializationApi")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }

    register<Jar>("testArchive") {
        archiveBaseName.set("${project.name}-test")
        from(project.the<SourceSetContainer>()["test"].output)
    }
}

artifacts {
    add("test", tasks["testArchive"])
}

tasks {
    withType<org.jetbrains.kotlin.gradle.tasks.KotlinCompile>().configureEach {
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlinx.coroutines.FlowPreview")
        compilerOptions.freeCompilerArgs.add("-opt-in=kotlin.RequiresOptIn")
    }
}
