package com.nextchaptersoftware.scm.bitbucket

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketBatchStream
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketStream
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketEmail
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketOrgMembership
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPageMeta
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketRepo
import com.nextchaptersoftware.scm.bitbucket.models.Bitbucket<PERSON>ser
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.types.EmailAddress
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList

internal class BitbucketUserApi(
    private val tokenProvider: ScmUserTokenProvider,
    orgId: OrgId?,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmUserApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun user(): ScmAuthUser {
        val user = client.get("user").body<BitbucketUser>()
        val emails = client.bitbucketStream<BitbucketEmail>("user/emails").filter { it.isConfirmed }

        return ScmAuthUser(
            externalId = user.accountId ?: user.uuid,
            avatarUrl = user.links.avatar.href,
            htmlUrl = user.links.html.href,
            username = user.nickname ?: user.displayName,
            displayName = user.displayName,
            primaryEmail = emails.firstOrNull { it.isPrimary }?.email?.let(EmailAddress::of)
                ?: throw IllegalStateException("Expected a primary Bitbucket email"),
            emails = emails.map { EmailAddress.of(it.email) }.toList(),
            oauthTokens = tokenProvider.getTokens(),
            isBot = null,
        )
    }

    override fun accounts(): Flow<ScmAccount> {
        return client.bitbucketStream<BitbucketOrgMembership>(
            initialUri = "user/permissions/workspaces",
        ) {
            parameter("q", """permission="owner"""")
        }.map {
            when (it.isTeamOwned) {
                true -> it.org.asScmOrg.let(ScmAccount::Org)
                false -> it.org.asScmUser.let(ScmAccount::User)
            }
        }
    }

    override suspend fun installation(externalInstallationId: String): ScmInstallationAccount? {
        error("Not applicable")
    }

    override suspend fun accessibleInstallations(externalUserId: String): List<ScmInstallationAccount> {
        error("Not applicable")
    }

    @Suppress("SpreadOperator")
    override fun repos(externalInstallationId: String): Flow<List<ScmRepository>> {
        return client.bitbucketBatchStream<BitbucketRepo>(
            initialUri = "repositories/$externalInstallationId",
        ) {
            parameter("sort", "full_name")
            parameter(
                "fields",
                listOf(
                    *BitbucketPageMeta.FIELD_NAMES,
                    "values.created_on",
                    "values.updated_on",
                    "values.full_name",
                    "values.is_private",
                    "values.links",
                    "values.owner",
                    "values.owner.username",
                    "values.size",
                    "values.slug",
                    "values.uuid",
                    "values.parent.full_name",
                ).joinToString(","),
            )
        }.map { batch ->
            batch.items
                .filterNot { it.isFork }
                .map { it.asScmRepository }
        }
    }

    override suspend fun createIssueComment(owner: String, repoName: String, issueNumber: Int, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun updateIssueComment(owner: String, repoName: String, commentId: String, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun deleteIssueComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updateReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        reviewId: String,
        body: GitHubBody,
    ): ScmPullRequestReview {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        body: GithubReactionBody,
    ): GitHubReaction {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewCommentReaction(owner: String, repoName: String, commentId: String, reactionId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequest(
        owner: String,
        repoName: String,
        prNumber: Int,
        body: GitHubPullRequestUpdateBody,
    ): GitHubPullRequest {
        TODO("Not yet implemented")
    }

    override fun close() {
        client.close()
    }
}
