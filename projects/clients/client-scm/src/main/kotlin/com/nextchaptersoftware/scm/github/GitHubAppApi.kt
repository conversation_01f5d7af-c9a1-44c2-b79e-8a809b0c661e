package com.nextchaptersoftware.scm.github

import com.expediagroup.graphql.client.types.GraphQLClientResponse
import com.nextchaptersoftware.clients.githubV4.DiscussionQuery
import com.nextchaptersoftware.clients.githubV4.DiscussionsQuery
import com.nextchaptersoftware.clients.githubV4.FileCommitHistory
import com.nextchaptersoftware.clients.githubV4.OrgMemberRepoAccessQuery
import com.nextchaptersoftware.clients.githubV4.OrgMembersQuery
import com.nextchaptersoftware.clients.githubV4.PullRequestReviewThreadsQuery
import com.nextchaptersoftware.clients.githubV4.discussionquery.User as DiscussionQueryUser
import com.nextchaptersoftware.clients.githubV4.discussionsquery.User as DiscussionsQueryUser
import com.nextchaptersoftware.clients.githubV4.enums.OrganizationMemberRole
import com.nextchaptersoftware.clients.githubV4.enums.RepositoryVisibility
import com.nextchaptersoftware.clients.githubV4.filecommithistory.Commit
import com.nextchaptersoftware.clients.githubV4.pullrequestreviewthreadsquery.User as PullRequestReviewThreadsQueryUser
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.graphql.client.GraphQLHttpClient
import com.nextchaptersoftware.graphql.client.GraphQLKtorClient
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.ktor.utils.lenientDecodeBase64String
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.error.ScmFileTooLargeException
import com.nextchaptersoftware.scm.github.GitHubPagination.githubBatchStream
import com.nextchaptersoftware.scm.github.GitHubPagination.githubStream
import com.nextchaptersoftware.scm.github.models.GitHubApp
import com.nextchaptersoftware.scm.github.models.GitHubAppInstallAccessToken
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubCommit
import com.nextchaptersoftware.scm.github.models.GitHubCommitCompareResult
import com.nextchaptersoftware.scm.github.models.GitHubCommitMinimal
import com.nextchaptersoftware.scm.github.models.GitHubContributorStats
import com.nextchaptersoftware.scm.github.models.GitHubFileContents
import com.nextchaptersoftware.scm.github.models.GitHubInstallation
import com.nextchaptersoftware.scm.github.models.GitHubIssue
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubMarkdownMode
import com.nextchaptersoftware.scm.github.models.GitHubMarkdownRequest
import com.nextchaptersoftware.scm.github.models.GitHubOrg
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReview
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewCreateBody
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewFile
import com.nextchaptersoftware.scm.github.models.GitHubRateLimit
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GitHubRepo
import com.nextchaptersoftware.scm.github.models.GitHubRepos
import com.nextchaptersoftware.scm.github.models.GitHubUser
import com.nextchaptersoftware.scm.github.models.asScmContributorStats
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmMember
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmRole
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.scm.models.asScmCommitAuthor
import com.nextchaptersoftware.scm.models.transformers.asScmPullRequestReview
import com.nextchaptersoftware.security.token
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.ensureTrailingSlash
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.ResponseException
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.plugins.expectSuccess
import io.ktor.client.request.accept
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import io.ktor.http.contentType
import io.ktor.http.isSuccess
import java.io.Closeable
import kotlin.math.min
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.takeWhile
import kotlinx.datetime.Instant

class GitHubAppApi(
    val scm: Scm,
    private val orgId: OrgId?,
    private val restApiUrl: Url,
    private val graphApiUrl: Url,
    private val appId: String,
    private val appPrivateKey: Secret,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) {
    val commonHttpClient = GraphQLHttpClient.create(
        orgId = orgId,
        clientEngine = clientEngine,
    )

    fun v3App() = V3App()

    private val appTokenProvider by lazy {
        AppTokenProvider(
            appId = appId,
            appPrivateKey = appPrivateKey,
        )
    }

    internal val appInstallTokenProvider by lazy {
        AppInstallTokenProvider(
            appId = appId,
            v3AppApi = v3App(),
            apiV3BaseUrl = restApiUrl,
        )
    }

    fun v3Org(installationId: String) = V3Org(installationId)

    fun v4Org(installationId: String) = V4Org(installationId)

    inner class V3App internal constructor() : Closeable {
        private val client: HttpClient = commonHttpClient.config {
            install(Auth) {
                bearer {
                    loadTokens {
                        BearerTokens(
                            accessToken = appTokenProvider.generateAppToken(),
                            refreshToken = "",
                        )
                    }
                }
            }
            install(HttpRequestRetry)
            expectSuccess = true
            defaultRequest {
                url(restApiUrl.asString.ensureTrailingSlash)
            }
        }

        /** Prefer [orgById] instead, because [orgName] can be changed by the user */
        suspend fun orgByName(orgName: String): GitHubOrg = client.get("orgs/$orgName").body()

        suspend fun orgById(externalId: String): GitHubOrg = client.get("organizations/$externalId").body()

        suspend fun app(): GitHubApp {
            return client.get("app").body()
        }

        fun installations(since: Instant? = null): Flow<GitHubInstallation> {
            return client.githubStream("app/installations") {
                since?.also {
                    // Note: Kotlin instant prints to ISO-8601, which happens to be the format GitHub expects
                    parameter("since", it.toString())
                }
            }
        }

        suspend fun installation(installationId: String): GitHubInstallation {
            return client.get("app/installations/$installationId").body()
        }

        /**
         * Suspends a GitHub App on a user or organization, which blocks the app from accessing the account's resources.
         * When a GitHub App is suspended, the app's access to the GitHub API or webhook events is blocked for that account.
         */
        suspend fun suspendInstallation(installationId: String) {
            client.put("app/installations/$installationId/suspended")
        }

        /**
         * Removes a GitHub App installation suspension.
         */
        suspend fun unsuspendInstallation(installationId: String) {
            client.delete("app/installations/$installationId/suspended")
        }

        suspend fun deleteInstallation(installationId: String) {
            client.delete("app/installations/$installationId") {
                expectSuccess = false
            }.also {
                when (it.status) {
                    HttpStatusCode.NoContent -> return

                    HttpStatusCode.NotFound -> return

                    // not found, assume previously deleted.
                    else -> throw ResponseException(it, "Failed to delete installation")
                }
            }
        }

        // TODO scope this down with [repositories] and [permissions] body arguments
        suspend fun installationAccessToken(installationId: String): GitHubAppInstallAccessToken {
            return client.post("app/installations/$installationId/access_tokens").body()
        }

        override fun close() {
            client.close()
        }
    }

    inner class V3Org internal constructor(private val installationId: String) : Closeable {
        private val client: HttpClient = commonHttpClient.config {
            install(Auth) {
                token {
                    loadToken {
                        appInstallTokenProvider.getInstallToken(installationId).token
                    }
                }
            }
            defaultRequest {
                contentType(ContentType.Application.Json)
                url(restApiUrl.asString.ensureTrailingSlash)
            }
        }

        suspend fun token(scmTokenConfig: ScmTokenConfig? = null): Secret {
            val token = appInstallTokenProvider.getInstallToken(
                installationId = installationId,
                scmTokenConfig = scmTokenConfig,
            ).token
            return Secret(token)
        }

        suspend fun rateLimit(): ScmRateLimit {
            return runSuspendCatching { client.get("rate_limit").body<GitHubRateLimit>() }
                .recoverCatching {
                    when {
                        it is ClientRequestException && it.response.status == HttpStatusCode.NotFound -> GitHubRateLimit.unlimited()
                        else -> throw it
                    }
                }
                .getOrThrow()
                .asScmRateLimit
        }

        suspend fun organization(orgId: String): ScmOrg {
            return client.get("organizations/$orgId").body<GitHubOrg>().asScmOrg
        }

        suspend fun user(userExternalId: String): ScmUser {
            return client.get("user/$userExternalId").body<GitHubUser>().asScmUser
        }

        internal fun membersByRole(orgId: String, role: String): Flow<ScmUser> {
            return client.githubStream<GitHubUser>("organizations/$orgId/members") {
                parameter("role", role)
            }.map { it.asScmUser }
        }

        @Deprecated("Use [membersByRole] instead")
        fun members(orgId: String): Flow<ScmMember> {
            return flow {
                emitAll(membersByRole(orgId, "admin").map { ScmMember(user = it, role = ScmRole.Owner) })
                emitAll(membersByRole(orgId, "member").map { ScmMember(user = it, role = ScmRole.Read) })
            }
        }

        fun repositories(): Flow<List<ScmRepository>> {
            return client.githubBatchStream(
                initialUri = "installation/repositories",
                dataProvider = { it.body<GitHubRepos>().repos },
            ).map { batch ->
                batch.items
                    .map { it.asScmRepository }
                    .filterNot { it.isArchived }
                    .filterNot { it.isDisabled }
            }
        }

        suspend fun repository(repoExternalId: String): ScmRepository {
            return client.get("repositories/$repoExternalId").body<GitHubRepo>().asScmRepository
        }

        suspend fun headCommitSha(repoExternalId: String): Hash {
            return client.get("repositories/$repoExternalId/commits/HEAD").body<GitHubCommitMinimal>().sha.let { Hash.parse(it) }
        }

        suspend fun headCommit(repoExternalId: String): ScmCommit {
            return client.get("repositories/$repoExternalId/commits/HEAD").body<GitHubCommit>().asScmCommit
        }

        suspend fun pullRequest(
            repoExternalId: String,
            pullRequestNumber: Int,
        ): ScmPullRequest {
            return client.get("repositories/$repoExternalId/pulls/$pullRequestNumber").body<GitHubPullRequest>().asScmPullRequest
        }

        suspend fun pullRequestDiff(
            repoExternalId: String,
            pullRequestNumber: Int,
        ): String {
            return client.get("repositories/$repoExternalId/pulls/$pullRequestNumber") {
                accept(GitDiff)
            }.body<String>()
        }

        suspend fun fileContents(
            repoExternalId: String,
            path: String,
            ref: String?,
        ): String {
            // This will only handle up to 1MB of content as a consequence of GitHub's own API limits
            // If we need to handle larger files, we need to do the following:
            //   1. Read the 'size' field from the first response and throw when it exceeds the higher limit
            //   2. Check if contents is an empty string, and if so, set the media type to 'application/vnd.github.raw+json' and retry
            val githubFileContents = client.get("repositories/$repoExternalId/contents/$path") {
                ref?.also { parameter("ref", it) }
            }.body<GitHubFileContents>()

            if (githubFileContents.content.isBlank()) {
                throw ScmFileTooLargeException()
            }

            return githubFileContents.content.lenientDecodeBase64String()
        }

        suspend fun latestPullRequest(
            repoExternalId: String,
        ): ScmPullRequest? {
            return client.get("repositories/$repoExternalId/pulls") {
                parameter("state", "all")
                parameter("sort", "created")
                parameter("direction", "desc")
                parameter("per_page", 1)
            }.body<List<GitHubPullRequest>>().firstOrNull()?.asScmPullRequest
        }

        fun allIssues(
            repoExternalId: String,
            initialBatchUrl: Url?,
            since: Instant?,
        ): Flow<HttpClientBatch<ScmIssue>> {
            return when (initialBatchUrl) {
                null -> client.githubBatchStream<GitHubIssue>("repositories/$repoExternalId/issues") {
                    parameter("state", "all")
                    parameter("direction", "desc")
                    when (since) {
                        null -> parameter("sort", "created")
                        else -> parameter("sort", "updated")
                    }
                }

                else -> client.githubBatchStream<GitHubIssue>(initialBatchUrl.asString)
            }.takeWhile { batch ->
                when (since) {
                    null -> true
                    else -> batch.items.any { it.updatedAt > since }
                }
            }.map { batch ->
                HttpClientBatch(
                    items = batch.items.filterNot { it.isPullRequest }.map { it.asScmIssue }.filter {
                        when (since) {
                            null -> true
                            else -> it.updatedAt > since
                        }
                    },
                    nextCursor = batch.nextCursor,
                )
            }
        }

        private fun streamAllIssueComments(
            repoExternalId: String,
            initialBatchUrl: Url?,
        ): Flow<HttpClientBatch<GitHubIssueComment>> {
            return client.githubBatchStream(initialBatchUrl?.asString ?: "repositories/$repoExternalId/issues/comments")
        }

        fun allIssueComments(
            repoExternalId: String,
            initialBatchUrl: Url?,
        ): Flow<HttpClientBatch<ScmIssueComment>> {
            return streamAllIssueComments(repoExternalId = repoExternalId, initialBatchUrl = initialBatchUrl).map { batch ->
                HttpClientBatch(
                    items = batch.items.filterNot { it.isPullRequestComment }.map { it.asScmIssueComment },
                    nextCursor = batch.nextCursor,
                )
            }
        }

        fun allPullRequests(
            repoExternalId: String,
            initialBatchUrl: Url?,
            since: Instant?,
        ): Flow<HttpClientBatch<ScmPullRequest>> {
            return when (initialBatchUrl) {
                null -> client.githubBatchStream<GitHubPullRequest>("repositories/$repoExternalId/pulls") {
                    parameter("state", "all")
                    parameter("direction", "desc")
                    when (since) {
                        null -> parameter("sort", "created")
                        else -> parameter("sort", "updated")
                    }
                }

                else -> client.githubBatchStream<GitHubPullRequest>(initialBatchUrl.asString)
            }.takeWhile { batch ->
                when (since) {
                    null -> true
                    else -> batch.items.any { it.updatedAt > since }
                }
            }.map { batch ->
                HttpClientBatch(
                    items = batch.items.map { it.asScmPullRequest }.filter {
                        when (since) {
                            null -> true
                            else -> it.updatedAt > since
                        }
                    },
                    nextCursor = batch.nextCursor,
                )
            }
        }

        fun allPullRequestComments(
            repoExternalId: String,
            initialBatchUrl: Url?,
        ): Flow<HttpClientBatch<ScmPrComment>> {
            return streamAllIssueComments(repoExternalId = repoExternalId, initialBatchUrl = initialBatchUrl).map { batch ->
                HttpClientBatch(
                    items = batch.items.filter { it.isPullRequestComment }.map { it.asScmPrComment },
                    nextCursor = batch.nextCursor,
                )
            }
        }

        fun pullRequestReviewsForPR(
            repoExternalId: String,
            pullRequestNumber: Int,
        ): Flow<ScmPullRequestReview> {
            return client.githubStream<GitHubPullRequestReview>("repositories/$repoExternalId/pulls/$pullRequestNumber/reviews")
                .map { it.asScmPullRequestReview }
        }

        fun allPullRequestReviewComments(
            repoExternalId: String,
            initialBatchUrl: Url?,
        ): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
            return when (initialBatchUrl) {
                null -> client.githubBatchStream<GitHubPullRequestReviewComment>("repositories/$repoExternalId/pulls/comments")
                else -> client.githubBatchStream<GitHubPullRequestReviewComment>(initialBatchUrl.asString)
            }.map { batch ->
                HttpClientBatch(
                    items = batch.items.map { it.asScmPrComment },
                    nextCursor = batch.nextCursor,
                )
            }
        }

        fun pullRequestCodeCommentsForPR(
            repoExternalId: String,
            pullRequestNumber: Int,
        ): Flow<ScmPrComment> {
            return client.githubStream<GitHubPullRequestReviewComment>("repositories/$repoExternalId/pulls/$pullRequestNumber/comments")
                .map { it.asScmPrComment }
        }

        fun pullRequestFilesForPR(
            repoExternalId: String,
            pullRequestNumber: Int,
            maxItems: Int? = null,
        ): Flow<ScmPullRequestFile> {
            return client.githubStream<GitHubPullRequestReviewFile>(
                initialUri = "repositories/$repoExternalId/pulls/$pullRequestNumber/files",
                maxItems = maxItems,
            ).map { it.asScmPullRequestFile }
        }

        /**
         * https://docs.github.com/en/rest/pulls/reviews?apiVersion=2022-11-28#create-a-review-for-a-pull-request
         */
        suspend fun pullRequestReviewCreate(
            owner: String,
            repoName: String,
            pullRequestNumber: Int,
            body: GitHubPullRequestReviewCreateBody,
        ): GitHubPullRequestReview {
            return client.post("repos/$owner/$repoName/pulls/$pullRequestNumber/reviews") {
                setBody(body)
            }
                .body()
        }

        /**
         * https://docs.github.com/en/rest/pulls/reviews?apiVersion=2022-11-28#create-a-review-for-a-pull-request
         */
        suspend fun pullRequestReviewUpdate(
            owner: String,
            repoName: String,
            pullRequestNumber: Int,
            reviewId: Long,
            body: GitHubBody,
        ): GitHubPullRequestReview {
            return client.put("repos/$owner/$repoName/pulls/$pullRequestNumber/reviews/$reviewId") {
                setBody(body)
            }
                .body()
        }

        /**
         * https://docs.github.com/en/rest/issues/comments?apiVersion=2022-11-28#create-an-issue-comment
         */
        suspend fun issueCommentCreate(
            owner: String,
            repoName: String,
            issueNumber: Int,
            body: GitHubBody,
        ): GitHubIssueComment {
            return client.post("repos/$owner/$repoName/issues/$issueNumber/comments") {
                setBody(body)
            }
                .body()
        }

        /**
         * https://docs.github.com/en/rest/issues/comments?apiVersion=2022-11-28#update-an-issue-comment
         */
        suspend fun issueCommentUpdate(
            owner: String,
            repoName: String,
            commentId: Long,
            body: GitHubBody,
        ): GitHubIssueComment {
            return client.patch("repos/$owner/$repoName/issues/comments/$commentId") {
                setBody(body)
            }
                .body()
        }

        /**
         * https://docs.github.com/en/rest/issues/comments?apiVersion=2022-11-28#delete-an-issue-comment
         */
        suspend fun issueCommentDelete(
            owner: String,
            repoName: String,
            commentId: Long,
        ): Boolean {
            return client.delete("repos/$owner/$repoName/issues/comments/$commentId")
                .status
                .isSuccess()
        }

        /**
         * https://docs.github.com/en/rest/reactions/reactions?apiVersion=2022-11-28#list-reactions-for-an-issue-comment
         */
        fun issueCommentReactions(
            owner: String,
            repoName: String,
            commentId: String,
        ): Flow<GitHubReaction> {
            return client.githubStream<GitHubReaction>(
                "repos/$owner/$repoName/issues/comments/$commentId/reactions",
            )
        }

        fun commitsBetweenCommits(
            repoExternalId: String,
            base: Hash,
            head: Hash?,
            maxItems: Int?,
        ): Flow<ScmCommit> {
            return client.githubStream<GitHubCommit>(
                initialUri = "repositories/$repoExternalId/compare/${base.asString()}...${head?.asString() ?: "HEAD"}",
                dataProvider = { it.body<GitHubCommitCompareResult>().commits },
                maxItems = maxItems,
            ).map {
                it.asScmCommit
            }
        }

        fun commits(
            repoExternalId: String,
            authorUsername: String?,
            since: Instant?,
            until: Instant?,
            maxItems: Int?,
        ): Flow<ScmCommit> {
            return client.githubStream<GitHubCommit>(
                initialUri = "repositories/$repoExternalId/commits",
                dataProvider = { it.body<List<GitHubCommit>>() },
                maxItems = maxItems,
            ) {
                since?.also { parameter("since", it.toString()) }
                until?.also { parameter("until", it.toString()) }
                authorUsername?.also { parameter("author", it) }
            }
                .map {
                    it.asScmCommit
                }
        }

        fun contributorStats(
            repoExternalId: String,
        ): Flow<ScmContributorStats> {
            return client.githubStream<GitHubContributorStats>(
                initialUri = "repositories/$repoExternalId/stats/contributors",
            ).mapNotNull {
                it.asScmContributorStats
            }
        }

        suspend fun commit(repoExternalId: String, sha: Hash): ScmCommit {
            return client.get("repositories/$repoExternalId/commits/${sha.asString()}").body<GitHubCommit>().asScmCommit
        }

        suspend fun commitDiff(repoExternalId: String, sha: Hash): String {
            return client.get("repositories/$repoExternalId/commits/${sha.asString()}") {
                accept(GitDiff)
            }.body<String>()
        }

        /**
         * https://docs.github.com/en/rest/commits/commits?apiVersion=2022-11-28#compare-two-commits
         */
        suspend fun diff(repoFullName: String, base: String, head: String): String {
            return client.get("repos/$repoFullName/compare/$base...$head") {
                accept(GitDiff)
            }.body<String>()
        }

        override fun close() {
            client.close()
        }

        suspend fun markdown(repoFullName: String, text: String): String {
            return client.post("markdown") {
                setBody(
                    GitHubMarkdownRequest(
                        context = repoFullName,
                        text = text,
                        mode = GitHubMarkdownMode.GFM,
                    ),
                )
            }.body()
        }
    }

    inner class V4Org internal constructor(private val installationId: String) : Closeable {
        private val client: GraphQLKtorClient = GraphQLKtorClient(
            httpClient = commonHttpClient.config {
                install(Auth) {
                    token {
                        loadToken {
                            appInstallTokenProvider.getInstallToken(installationId).token
                        }
                    }
                }
                defaultRequest {
                    url(graphApiUrl.asString)
                }
            },
        )

        suspend fun pullRequestReviewThreads(
            owner: String,
            repoName: String,
            prAfterCursor: String? = null,
            prPageSize: Int,
        ): PullRequestReviewThreadsResult {
            val query = PullRequestReviewThreadsQuery(
                PullRequestReviewThreadsQuery.Variables(
                    owner = owner,
                    name = repoName,
                    prAfterCursor = prAfterCursor,
                    prPageSize = prPageSize,
                ),
            )

            val response = client.execute(query)
            val repository = response.data?.repository ?: throw IllegalStateException("Missing repository")
            val pullRequests = repository.pullRequests.nodes?.filterNotNull().orEmpty()
            val endCursor = repository.pullRequests.pageInfo.endCursor // For the next round of ingestion
            val lastUpdatedAt = pullRequests.maxOfOrNull { Instant.parse(it.updatedAt) }

            return PullRequestReviewThreadsResult(
                pullRequests = pullRequests.filter { it.reviewThreads.totalCount > 0 }.mapNotNull { // Skip PRs with no review threads
                    when (val author = it.author) {
                        null -> null

                        else -> PullRequestReviewThreadsResult.PullRequestAndAuthor(
                            number = it.number,
                            authorLogin = author.login,
                            authorExternalId = if (author is PullRequestReviewThreadsQueryUser) author.databaseId else null,
                        )
                    }
                },
                endCursor = endCursor,
                lastUpdatedAt = lastUpdatedAt,
            )
        }

        suspend fun discussions(
            owner: String,
            repoName: String,
            discussionsAfterCursor: String? = null,
            discussionsPageSize: Int,
        ): DiscussionsResult {
            val query = DiscussionsQuery(
                DiscussionsQuery.Variables(
                    owner = owner,
                    name = repoName,
                    discussionsAfterCursor = discussionsAfterCursor,
                    discussionsPageSize = discussionsPageSize,
                ),
            )

            val response = client.execute(query)
            val repository = response.data?.repository ?: throw IllegalStateException("Missing repository")
            val discussions = repository.discussions.nodes?.filterNotNull().orEmpty()
            val endCursor = repository.discussions.pageInfo.endCursor // For the next round of ingestion

            return DiscussionsResult(
                discussions = discussions.map { discussion ->
                    DiscussionsResult.Discussion(
                        number = discussion.number,
                        title = discussion.title,
                        createdAt = Instant.parse(discussion.createdAt),
                        updatedAt = Instant.parse(discussion.updatedAt),
                        url = discussion.url,
                        body = discussion.body,
                        author = if (discussion.author is DiscussionsQueryUser) discussion.author.name else discussion.author?.login,
                        comments = discussion.comments.nodes?.filterNotNull().orEmpty().map {
                            DiscussionsResult.Discussion.Comment(
                                createdAt = Instant.parse(it.createdAt),
                                body = it.body,
                                author = if (it.author is DiscussionsQueryUser) it.author.name else it.author?.login,
                            )
                        },
                    )
                },
                endCursor = endCursor,
            )
        }

        suspend fun discussion(
            owner: String,
            repoName: String,
            discussionNumber: Int,
        ): DiscussionsResult.Discussion {
            val query = DiscussionQuery(
                DiscussionQuery.Variables(
                    owner = owner,
                    name = repoName,
                    discussionNumber = discussionNumber,
                ),
            )

            val response = client.execute(query)
            val repository = response.data?.repository ?: throw IllegalStateException("Missing repository")
            val discussion = repository.discussion ?: throw IllegalStateException("Missing discussion")

            return DiscussionsResult.Discussion(
                number = discussion.number,
                title = discussion.title,
                createdAt = Instant.parse(discussion.createdAt),
                updatedAt = Instant.parse(discussion.updatedAt),
                url = discussion.url,
                body = discussion.body,
                author = if (discussion.author is DiscussionQueryUser) discussion.author.name else discussion.author?.login,
                comments = discussion.comments.nodes?.filterNotNull().orEmpty().map {
                    DiscussionsResult.Discussion.Comment(
                        createdAt = Instant.parse(it.createdAt),
                        body = it.body,
                        author = if (it.author is DiscussionQueryUser) it.author.name else it.author?.login,
                    )
                },
            )
        }

        fun membersWithRoles(
            owner: String,
        ): Flow<ScmMember> {
            var nextCursor: String? = null

            return flow {
                do {
                    val response: GraphQLClientResponse<OrgMembersQuery.Result> = client.execute(
                        OrgMembersQuery(
                            OrgMembersQuery.Variables(
                                owner = owner,
                                pageSize = 100,
                                afterCursor = nextCursor,
                            ),
                        ),
                    )

                    requireNotNull(response.data?.organization?.membersWithRole).let { page ->
                        nextCursor = when (page.pageInfo.hasNextPage) {
                            true -> page.pageInfo.endCursor
                            false -> null
                        }
                        page.items?.filterNotNull()?.forEach { memberWithRole ->
                            emit(
                                ScmMember(
                                    user = requireNotNull(memberWithRole.user).let { user ->
                                        ScmUser(
                                            externalId = user.databaseId.toString(),
                                            displayName = user.name,
                                            unverifiedNonPrimaryEmail = null,
                                            login = user.login,
                                            avatarUrl = user.avatarUrl.asUrl,
                                            htmlUrl = user.url.asUrl,
                                            isBot = false,
                                        )
                                    },
                                    role = when (memberWithRole.role) {
                                        OrganizationMemberRole.ADMIN -> ScmRole.Owner
                                        else -> ScmRole.Read
                                    },
                                ),
                            )
                        }
                    }
                } while (nextCursor != null)
            }
        }

        fun memberRepoAccess(
            orgName: String,
            username: String,
        ): Flow<String> {
            var nextCursor: String? = null

            return flow {
                do {
                    val response: GraphQLClientResponse<OrgMemberRepoAccessQuery.Result> = client.execute(
                        OrgMemberRepoAccessQuery(
                            OrgMemberRepoAccessQuery.Variables(
                                owner = orgName,
                                username = username,
                                pageSize = 100,
                                afterCursor = nextCursor,
                            ),
                        ),
                    )
                    response.data?.organization?.repositories?.let { page ->
                        nextCursor = when (page.pageInfo.hasNextPage) {
                            true -> page.pageInfo.endCursor
                            false -> null
                        }
                        page.repos
                            ?.filterNotNull()
                            ?.filter { repo ->
                                when (repo.visibility) {
                                    RepositoryVisibility.INTERNAL,
                                    RepositoryVisibility.PUBLIC,
                                        -> true

                                    RepositoryVisibility.PRIVATE ->
                                        repo.collaborators?.users?.firstOrNull()?.user?.userId != null

                                    RepositoryVisibility.__UNKNOWN_VALUE -> error("Unknown repository visibility")
                                }
                            }
                            ?.forEach { repo ->
                                repo.repoId?.toString()?.also {
                                    emit(it)
                                }
                            }
                    }
                } while (nextCursor != null)
            }
        }

        @Suppress("MagicNumber")
        fun fileCommits(
            owner: String,
            repoName: String,
            path: String,
            maxItems: Int?,
        ): Flow<ScmCommit> {
            val maxPageSize = 100
            val max = maxItems ?: maxPageSize
            var nextCursor: String? = null
            var pageSize = maxPageSize
            var numItemsCollected = 0

            return flow {
                do {
                    val response: GraphQLClientResponse<FileCommitHistory.Result> = client.execute(
                        FileCommitHistory(
                            FileCommitHistory.Variables(
                                owner = owner,
                                repoName = repoName,
                                filePath = path,
                                commitPageSize = pageSize,
                                commitAfterCursor = nextCursor,
                            ),
                        ),
                    )

                    requireNotNull(
                        response.data?.repository?.defaultBranchRef?.target?.let { target ->
                            when (target) {
                                is Commit -> {
                                    target.history
                                }

                                else -> {
                                    null
                                }
                            }
                        },
                    ).let { page ->
                        nextCursor = when (page.pageInfo.hasNextPage) {
                            true -> page.pageInfo.endCursor
                            false -> null
                        }
                        val commits = page.commits?.take(max - numItemsCollected)
                        numItemsCollected += commits?.size ?: 0
                        pageSize = min(maxPageSize, max - numItemsCollected)
                        commits?.filterNotNull()?.forEach { commit ->
                            commit.author?.asScmCommitAuthor?.also { author ->
                                if (!author.name.endsWith("[bot]")) {
                                    emit(
                                        ScmCommit(
                                            commitDate = Instant.parse(commit.committedDate),
                                            sha = Hash.parse(commit.sha),
                                            author = author,
                                        ),
                                    )
                                }
                            }
                        }
                    }
                } while (nextCursor != null && numItemsCollected < max)
            }
        }

        override fun close() {
            client.close()
        }
    }
}

private val GitDiff: ContentType
    get() = ContentType("application", "vnd.github.diff")
