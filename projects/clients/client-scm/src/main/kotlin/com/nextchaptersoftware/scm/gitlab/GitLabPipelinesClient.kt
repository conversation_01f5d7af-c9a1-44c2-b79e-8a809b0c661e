package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabStream
import com.nextchaptersoftware.scm.gitlab.models.GitLabJob
import com.nextchaptersoftware.scm.gitlab.models.GitLabPipeline
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import kotlinx.coroutines.flow.Flow

class GitLabPipelinesClient(
    private val projectId: String,
    private val client: HttpClient,
) : AutoCloseable {

    override fun close() {
        client.close()
    }

    suspend fun fetch(
        path: String,
    ): String {
        return client.get(path).bodyAsText()
    }

    /**
     * https://docs.gitlab.com/api/pipelines/#get-a-single-pipeline
     */
    suspend fun pipeline(
        pipelineId: Long,
    ): GitLabPipeline {
        return client.get("projects/$projectId/pipelines/$pipelineId")
            .body<GitLabPipeline>()
    }

    /**
     * https://docs.gitlab.com/api/jobs/#list-pipeline-jobs
     */
    fun pipelineJobs(
        pipelineId: Long,
    ): Flow<GitLabJob> {
        return client.gitLabStream<GitLabJob>(
            "projects/$projectId/pipelines/$pipelineId/jobs",
        )
    }
}
