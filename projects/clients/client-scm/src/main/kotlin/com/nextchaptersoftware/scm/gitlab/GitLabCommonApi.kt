package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabBatchStream
import com.nextchaptersoftware.scm.gitlab.models.GitLabRepo
import io.ktor.client.HttpClient
import io.ktor.client.request.parameter
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow

internal object GitLabCommonApi {

        fun apiBaseUrl(authority: String): Url {
            return "https://$authority/api/v4/".asUrl
        }

    fun HttpClient.getGroupRepos(externalOrgId: String): Flow<HttpClientBatch<GitLabRepo>> {
        return this.gitLabBatchStream<GitLabRepo>("groups/$externalOrgId/projects") {
            parameter("archived", false)
            parameter("include_subgroups", true)
            parameter("with_shared", false)
        }
    }

    /**
     * Keyset pagination is not supported for user repos, so we use the legacy offset pagination.
     */
    fun HttpClient.getUserRepos(externalUserId: String): Flow<HttpClientBatch<GitLabRepo>> {
        return this.gitLabBatchStream<GitLabRepo>(initialUri = "users/$externalUserId/projects", useKeysetPagination = false) {
            parameter("archived", false)
            parameter("owned", true)
        }
    }
}
