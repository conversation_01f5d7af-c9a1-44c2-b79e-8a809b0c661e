package com.nextchaptersoftware.scm.bitbucketdatacenter

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.HostAvailability
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.retry
import io.ktor.client.plugins.timeout
import io.ktor.http.Url
import kotlin.time.Duration.Companion.seconds

internal class BitbucketDataCenterNoAuthApi(
    private val baseApiUrl: Url,
    orgId: OrgId?,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmNoAuthApi {

    companion object {
        fun fromAuthority(orgId: OrgId?, authority: String): BitbucketDataCenterNoAuthApi {
            return BitbucketDataCenterNoAuthApi(
                baseApiUrl = "https://$authority/rest/api/latest".asUrl,
                orgId = orgId,
            )
        }
    }

    private val client = makeScmNoAuthHttpClient(
        orgId = orgId,
        baseApiUrl = baseApiUrl,
        userAgent = "getunblocked",
        clientEngine = clientEngine,
    )

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        TODO()
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        TODO()
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        TODO()
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        TODO()
    }

    override suspend fun getHostAvailability(): HostAvailabilityStatus {
        return HostAvailability.checkAvailability(baseApiUrl.host) {
            isEnterpriseHost()
        }
    }

    private suspend fun isEnterpriseHost(): Boolean {
        return client.bitbucketDataCenterApplication {
            timeout {
                requestTimeoutMillis = 4.seconds.inWholeMilliseconds
            }
            retry {
                maxRetries = 0
            }
        }.let { app ->
            app.displayName == "Bitbucket"
        }
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }
}
