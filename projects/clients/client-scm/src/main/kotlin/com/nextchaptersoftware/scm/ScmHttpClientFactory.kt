@file:Suppress("ktlint:nextchaptersoftware:no-direct-httpclient-constructor-rule")

package com.nextchaptersoftware.scm

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.client.org.orgHttpClient
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import com.nextchaptersoftware.utils.ensureTrailingSlash
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.ContentType
import io.ktor.http.Url
import io.ktor.http.contentType
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

/**
 * Common factory for creating an [HttpClient] for use with SCM APIs.
 */
internal object ScmHttpClientFactory {

    fun makeScmHttpClient(
        orgId: OrgId?,
        tokenProvider: ScmUserTokenProvider,
        baseApiUrl: Url,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
        allowRedirects: Boolean = true,
        timeout: Duration = 30.seconds,
    ): HttpClient {
        // ① Common configuration block reused by both branches
        val configure: HttpClientConfig<*>.() -> Unit = {
            install(ContentNegotiation) { installJsonSerializer() }

            install(Auth) {
                bearer {
                    loadTokens {
                        BearerTokens(
                            accessToken = tokenProvider.getTokens().accessToken.value,
                            refreshToken = "",
                        )
                    }
                }
            }

            defaultRequest {
                contentType(ContentType.Application.Json)
                url(baseApiUrl.asString.ensureTrailingSlash)
            }

            install(HttpRequestRetry)
            install(HttpTimeout) {
                requestTimeoutMillis = timeout.inWholeMilliseconds
                socketTimeoutMillis = timeout.inWholeMilliseconds
            }

            install(KtorClientTracing)

            expectSuccess = true
            followRedirects = allowRedirects
        }

        return if (orgId == null) {
            HttpClient(engine = clientEngine, block = configure)
        } else {
            orgHttpClient(orgId = orgId, engine = clientEngine, configure = configure)
        }
    }
}
