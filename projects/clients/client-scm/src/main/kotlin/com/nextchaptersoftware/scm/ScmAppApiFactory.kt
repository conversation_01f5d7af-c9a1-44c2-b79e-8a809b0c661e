package com.nextchaptersoftware.scm

import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.scm.github.GitHubAppApi
import com.nextchaptersoftware.utils.ensureTrailingSlash
import com.sksamuel.hoplite.Secret
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url
import io.ktor.util.decodeBase64String

class ScmAppApiFactory(
    private val scmConfig: ScmConfig,
    private val scmSecretConfig: ScmSecretConfig = ScmSecretConfig.INSTANCE,
    private val appConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
) {

    private val gheSecretDecryption by lazy {
        AESCryptoSystem.AESDecryption(
            AESCryptoSystem.importKey(scmSecretConfig.githubEnterprise.appSecretsAesKey.value),
        )
    }

    suspend fun getApi(
        scmTeam: ScmTeam,
    ): GitHubAppApi {
        return getApi(
            orgId = scmTeam.orgId,
            scm = Scm.fromProvider(scmTeam.provider, scmTeam.providerEnterpriseId),
        )
    }

    suspend fun getApi(
        scm: Scm,
        scmTeam: ScmTeam,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): GitHubAppApi {
        return getApiOrNull(
            scm = scm,
            orgId = scmTeam.orgId,
            clientEngine = clientEngine,
        )
            ?: error("Not applicable")
    }

    suspend fun getApi(
        scm: Scm,
        orgId: OrgId?,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): GitHubAppApi {
        return getApiOrNull(
            scm = scm,
            orgId = orgId,
            clientEngine = clientEngine,
        )
            ?: error("Not applicable")
    }

    suspend fun getApiOrNull(
        scm: Scm,
        orgId: OrgId?,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): GitHubAppApi? {
        return when (scm) {
            Scm.GitHub -> {
                val config = expectScmConfig(scmConfig.githubCloud)
                val secretConfig = expectScmConfig(scmSecretConfig.githubCloud)
                GitHubAppApi(
                    scm = Scm.GitHub,
                    orgId = orgId,
                    restApiUrl = Url(config.apiBaseUrl.ensureTrailingSlash),
                    graphApiUrl = Url(config.apiBaseUrl.ensureTrailingSlash.plus("graphql")),
                    appId = config.appId,
                    appPrivateKey = Secret(secretConfig.appKey.value.decodeBase64String()),
                    clientEngine = clientEngine,
                )
            }

            is Scm.GitHubEnterprise -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider) as EnterpriseAppConfig.GitHub
                getGitHubEnterpriseApi(
                    orgId = orgId,
                    enterpriseConfig = enterpriseConfig,
                )
            }

            is Scm.GitLabSelfHosted -> {
                null
            }

            is Scm.AzureDevOps -> {
                null
            }

            is Scm.BitbucketDataCenter -> {
                null
            }

            Scm.Bitbucket -> {
                null
            }

            Scm.GitLab -> {
                null
            }
        }
    }

    suspend fun getAllApis(): List<GitHubAppApi> {
        return buildList {
            add(getApi(orgId = null, scm = Scm.GitHub))
            addAll(
                appConfigStore.list().filterIsInstance<EnterpriseAppConfig.GitHub>().map { enterpriseConfig ->
                    getGitHubEnterpriseApi(orgId = null, enterpriseConfig = enterpriseConfig)
                },
            )
        }
    }

    private fun getGitHubEnterpriseApi(
        orgId: OrgId?,
        enterpriseConfig: EnterpriseAppConfig.GitHub,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): GitHubAppApi {
        return GitHubAppApi(
            orgId = orgId,
            scm = Scm.GitHubEnterprise(enterpriseConfig.id),
            restApiUrl = Url("https://${enterpriseConfig.hostAndPort}/api/v3/"),
            graphApiUrl = Url("https://${enterpriseConfig.hostAndPort}/api/graphql"),
            appId = enterpriseConfig.externalAppId,
            appPrivateKey = gheSecretDecryption.decrypt(Ciphertext(enterpriseConfig.privateKeyPemEncrypted)),
            clientEngine = clientEngine,
        )
    }
}
