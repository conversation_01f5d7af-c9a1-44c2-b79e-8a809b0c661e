package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi.getGroupRepos
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi.getUserRepos
import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabStream
import com.nextchaptersoftware.scm.gitlab.models.GitLabMember
import com.nextchaptersoftware.scm.gitlab.models.GitLabOrg
import com.nextchaptersoftware.scm.gitlab.models.GitLabRole
import com.nextchaptersoftware.scm.gitlab.models.GitLabUser
import com.nextchaptersoftware.scm.gitlab.models.GitLabWebhook
import com.nextchaptersoftware.scm.gitlab.models.GitLabWebhookEvents.Companion.createGitLabWebhookRequest
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmMember
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.scm.models.ScmWebhook
import com.nextchaptersoftware.scm.models.ScmWebhookManagementLevel
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

internal class GitLabTeamApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val scmTeam: ScmTeam,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmTeamApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun account(): ScmAccount {
        return when (scmTeam.providerIsPersonalAccount) {
            true -> user().let(ScmAccount::User)
            false -> org().let(ScmAccount::Org)
        }
    }

    suspend fun org(): ScmOrg {
        return client.get("groups/${scmTeam.providerExternalId}") {
            parameter("with_projects", false)
        }.body<GitLabOrg>()
            .asScmOrg
    }

    suspend fun user(): ScmUser {
        return client.get("users/${scmTeam.providerExternalId}").body<GitLabUser>()
            .asScmUser
    }

    override fun membersForOrg(): Flow<ScmMember> = suspendedFlow {
        when (scmTeam.providerIsPersonalAccount) {
            true -> flowOf(ScmMember(user = user(), role = GitLabRole.Owner.asScmRole))

            false -> client.gitLabStream<GitLabMember>("groups/${scmTeam.providerExternalId}/members/all")
                .map { it.asScmMember }
        }
    }

    override fun membersForRepo(repoExternalId: String): Flow<ScmMember> {
        return client.gitLabStream<GitLabMember>("projects/$repoExternalId/members/all")
            .map { it.asScmMember }
    }

    override fun repos(): Flow<List<ScmRepository>> {
        return when (scmTeam.providerIsPersonalAccount) {
            true -> client.getUserRepos(scmTeam.providerExternalId)
            false -> client.getGroupRepos(scmTeam.providerExternalId)
        }.map { batch ->
            batch.items
                .map { it.asScmRepository }
                .filterNot { it.isArchived }
                .filterNot { it.isDisabled }
        }
    }

    override suspend fun getWebhookCapability(scmTeam: ScmTeam): ScmWebhookManagementLevel {
        // FIXME this is not correct; and has nothing to do with personal groups.
        //  Hooks should be installed at the org level iff GitLab pricing plan supports it.
        //  Otherwise hooks should be installed at the repo level.
        //  We can use the namespace API to get the plan for a group, then use the plan to determine if group-level webhooks are supported.
        //  https://docs.gitlab.com/ee/api/namespaces.html
        return when (scmTeam.providerIsPersonalAccount) {
            true -> ScmWebhookManagementLevel.Repo
            false -> ScmWebhookManagementLevel.Org
        }
    }

    override fun listWebhooks(description: String, webhookUrl: Url): Flow<ScmWebhook> {
        return client.gitLabStream<GitLabWebhook>("groups/${scmTeam.providerExternalId}/hooks")
            .map { it.getAsScmWebhook(description) }
    }

    override suspend fun createWebhook(description: String, webhookUrl: Url) {
        client.post("groups/${scmTeam.providerExternalId}/hooks") {
            setBody(
                createGitLabWebhookRequest(
                    url = webhookUrl,
                ),
            )
        }
    }

    override suspend fun deleteWebhook(webhookId: String) {
        client.delete("groups/${scmTeam.providerExternalId}/hooks/$webhookId")
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun apiToken(): Secret {
        return tokenProvider.getTokens().accessToken
    }

    override fun close() {
        client.close()
    }
}
