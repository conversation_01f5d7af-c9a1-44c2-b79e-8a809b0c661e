package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.security.Hashing.asSha1Hash
import com.nextchaptersoftware.types.Hash
import io.ktor.http.Url
import kotlinx.datetime.Instant
import kotlinx.serialization.Contextual
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

// https://docs.gitlab.com/ee/api/merge_requests.html
@Serializable
internal data class GitLabMergeRequest(
    @SerialName("id")
    val id: Long,

    @SerialName("iid")
    val number: Int,

    @SerialName("title")
    val title: String,

    @SerialName("description")
    val description: String? = null,

    @SerialName("merge_commit_sha")
    val mergeCommitSha: String? = null,

    @SerialName("squash_commit_sha")
    val squashCommitSha: String? = null,

    @SerialName("source_branch")
    val sourceBranch: String? = null,

    @SerialName("sha")
    val sha: String? = null,

    @SerialName("author")
    val author: GitLabUser? = null,

    @SerialName("merged_at")
    val mergedAt: Instant?,

    @SerialName("closed_at")
    val closedAt: Instant?,

    @SerialName("updated_at")
    val updatedAt: Instant,

    @SerialName("created_at")
    val createdAt: Instant,

    @SerialName("assignees")
    val assignees: List<GitLabUser>,

    @SerialName("reviewers")
    val reviewers: List<GitLabUser>,

    @Contextual
    @SerialName("web_url")
    val webUrl: Url,

    @SerialName("state")
    private val mergeRequestState: String,
) {
    val state: PullRequestState
        get() = when (mergeRequestState) {
            "opened" -> PullRequestState.Open

            "merged" -> PullRequestState.Merged

            "closed" -> PullRequestState.Closed

            else -> when {
                mergedAt != null -> PullRequestState.Merged
                closedAt != null -> PullRequestState.Closed
                else -> PullRequestState.Open
            }
        }

    val descriptionHash: Hash?
        get() = description?.asSha1Hash()

    val asScmPullRequest
        get() = ScmPullRequest(
            id = id,
            number = number,
            title = title,
            body = if (description.isNullOrBlank()) null else description,
            headBranch = sourceBranch,
            headSha = sha,
            mergeCommitSha = when (state) {
                PullRequestState.Open, PullRequestState.Closed -> null
                PullRequestState.Merged -> mergeCommitSha ?: squashCommitSha ?: sha
            },
            user = author?.asScmUser,
            closedAt = closedAt,
            mergedAt = mergedAt,
            updatedAt = updatedAt,
            createdAt = createdAt,
            assignees = assignees.map { it.asScmUser },
            requestedReviewers = reviewers.map { it.asScmUser },
            htmlUrl = webUrl,
            state = state,
        )
}
