@file:Suppress("UnusedPrivateMember")

package com.nextchaptersoftware.scm.azuredevops

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory.makeScmHttpClient
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull

internal class AzureDevOpsUserApi(
    private val tokenProvider: ScmUserTokenProvider,
    orgId: OrgId?,
    identityId: IdentityId? = null,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmUserApi {

    private val client = AzureDevOpsClient(
        client = makeScmHttpClient(
            orgId = orgId,
            tokenProvider = tokenProvider,
            baseApiUrl = baseApiUrl,
            allowRedirects = false,
            clientEngine = clientEngine,
        ),
        identityId = identityId,
    )

    override suspend fun user(): ScmAuthUser {
        val profile = client.azureDevOpsProfile(fullDetails = true)
        return client.azureDevOpsAuthenticatedIdentity()
            .asScmAuthUser(
                oauthTokens = tokenProvider.getTokens(),
                avatar = profile.avatar.required(),
            )
    }

    override suspend fun installation(externalInstallationId: String): ScmInstallationAccount? {
        error("Not applicable")
    }

    override suspend fun accessibleInstallations(externalUserId: String): List<ScmInstallationAccount> {
        error("Not applicable")
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun accounts(): Flow<ScmAccount> {
        return client.azureDevOpsAccounts()
            .asFlatItemsFlow()
            .filter { account ->
                client.azureDevOpsUserCallerIsAdmin(
                    organization = account.accountName,
                )
            }
            .flatMapConcat { account ->
                client.azureDevOpsProjects(
                    organization = account.accountName,
                    includeDefaultTeamImageUrl = true,
                )
                    .asFlatItemsFlow()
                    .map { project ->
                        project.asScmAccount(
                            account = account,
                        )
                    }
            }
    }

    override fun repos(externalInstallationId: String): Flow<List<ScmRepository>> = suspendedFlow fn@{
        val project = client.azureDevOpsAccounts()
            .asFlatItemsFlow()
            .mapNotNull { account ->
                client.azureDevOpsProjectOrNullIfNotFound(
                    organization = account.accountName,
                    project = externalInstallationId,
                )
            }
            .firstOrNull()
            ?: return@fn emptyFlow()

        return@fn client.azureDevOpsRepositories(
            project = project,
        )
            .map { page ->
                page.items
                    .filterNot { it.isDisabled }
                    .map { it.asScmRepository }
            }
    }

    override suspend fun createIssueComment(owner: String, repoName: String, issueNumber: Int, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun updateIssueComment(owner: String, repoName: String, commentId: String, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun deleteIssueComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updateReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        reviewId: String,
        body: GitHubBody,
    ): ScmPullRequestReview {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        body: GithubReactionBody,
    ): GitHubReaction {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewCommentReaction(owner: String, repoName: String, commentId: String, reactionId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequest(owner: String, repoName: String, prNumber: Int, body: GitHubPullRequestUpdateBody): GitHubPullRequest {
        TODO("Not yet implemented")
    }

    override fun close() {
        client.close()
    }
}
