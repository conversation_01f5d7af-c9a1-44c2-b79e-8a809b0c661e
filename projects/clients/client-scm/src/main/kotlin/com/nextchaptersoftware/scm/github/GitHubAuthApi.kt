package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthGrant
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokenResponse
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.HttpExtensions.logCatchingDeserialize
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmAuthHttpClientFactory.makeScmAuthApiHttpClient
import com.nextchaptersoftware.scm.ScmAuthHttpClientFactory.makeScmAuthHttpClient
import com.nextchaptersoftware.scm.ScmWeb
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.basicAuth
import io.ktor.client.request.delete
import io.ktor.client.request.forms.submitForm
import io.ktor.client.request.setBody
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.Url
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

internal class GitHubAuthApi(
    private val oauthClientId: String,
    private val oauthClientSecret: Secret,
    private val redirectUrl: Url,
    private val scmWeb: ScmWeb,
    private val baseApiUrl: Url,
    orgId: OrgId?,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : OAuthApi {

    private val client: HttpClient = makeScmAuthHttpClient(
        orgId = orgId,
        clientEngine = clientEngine,
    )

    private val apiClient: HttpClient = makeScmAuthApiHttpClient(
        orgId = orgId,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_secret", oauthClientSecret.value)
                append("refresh_token", refreshToken.value)
                append("grant_type", OAuthGrant.RefreshToken.value)
            },
        ).logCatchingDeserialize<OAuthTokenResponse>().asOAuthTokens
    }

    override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_secret", oauthClientSecret.value)
                append("code", context.code)
                append("grant_type", OAuthGrant.AuthorizationCode.value)
                append("redirect_uri", redirectUrl.asString)
            },
        ).logCatchingDeserialize<OAuthTokenResponse>().asOAuthTokenExchange(redirectUrl = null)
    }

    /**
     * Deleting an application's grant will delete all OAuth tokens associated with the application for the user.
     * Once deleted, the application will have no access to the user's account and will no longer be listed on
     * the application authorizations settings screen within GitHub.
     *
     * https://docs.github.com/en/rest/apps/oauth-applications#delete-an-app-authorization
     */
    override suspend fun revokeToken(accessToken: Secret) {
        apiClient.delete("applications/$oauthClientId/grant") {
            basicAuth(oauthClientId, oauthClientSecret.value)
            setBody(RevokeTokenBody(accessToken = accessToken.value))
        }.also {
            when (it.status) {
                HttpStatusCode.NoContent -> return

                HttpStatusCode.NotFound -> return

                // not found, assume previously revoked.
                else -> throw ResponseException(it, "Failed to revoke authorization")
            }
        }
    }
}

@Serializable
private data class RevokeTokenBody(
    @SerialName("access_token")
    val accessToken: String,
)
