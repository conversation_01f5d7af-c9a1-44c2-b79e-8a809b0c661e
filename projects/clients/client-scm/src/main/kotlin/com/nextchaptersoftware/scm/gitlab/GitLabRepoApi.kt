package com.nextchaptersoftware.scm.gitlab

import arrow.fx.coroutines.parMapNotNull
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.kotlinx.coroutines.flow.asFlatItemsFlow
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.lenientDecodeBase64String
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.error.ScmFileTooLargeException
import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabBatchStream
import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabStream
import com.nextchaptersoftware.scm.gitlab.models.GitLabCommit
import com.nextchaptersoftware.scm.gitlab.models.GitLabDiff
import com.nextchaptersoftware.scm.gitlab.models.GitLabDiscussion
import com.nextchaptersoftware.scm.gitlab.models.GitLabFileContent
import com.nextchaptersoftware.scm.gitlab.models.GitLabMergeRequest
import com.nextchaptersoftware.scm.gitlab.models.GitLabMergeRequestVersion
import com.nextchaptersoftware.scm.gitlab.models.GitLabMergeRequestVersionDiff
import com.nextchaptersoftware.scm.gitlab.models.GitLabRepo
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmGitCredentials
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.asISO8601
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.get
import io.ktor.client.request.head
import io.ktor.client.request.parameter
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.encodeURLParameter
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.reduce
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

@OptIn(kotlinx.coroutines.ExperimentalCoroutinesApi::class)
internal class GitLabRepoApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val repo: Repo,
    scmTeam: ScmTeam,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmRepoApi {

    companion object {
        private const val MAX_GITLAB_FILE_SIZE = 1024L * 1024L // 1MB
    }

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun repo(): ScmRepository {
        return client.get("projects/${repo.externalId}").body<GitLabRepo>()
            .asScmRepository
    }

    override suspend fun headCommitShaOrNull(): Hash? {
        return runSuspendCatching {
            client.get("projects/${repo.externalId}/repository/commits/HEAD").body<GitLabCommit>().id
                .let(Hash.Companion::parse)
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.NotFound) {
                null
            } else {
                throw it
            }
        }
    }

    override suspend fun headCommitOrNull(): ScmCommit? {
        return runSuspendCatching {
            client.get("projects/${repo.externalId}/repository/commits/HEAD").body<GitLabCommit>().asScmCommit
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.NotFound) {
                null
            } else {
                throw it
            }
        }
    }

    override suspend fun pullRequest(pullRequestNumber: Int): ScmPullRequest {
        return client.get("projects/${repo.externalId}/merge_requests/$pullRequestNumber").body<GitLabMergeRequest>()
            .asScmPullRequest
    }

    override suspend fun pullRequestDiff(
        pullRequestNumber: Int,
        base: String?,
        head: String?,
    ): String {
        // FIXME: implement support for base...head
        return client.gitLabStream<GitLabDiff>("projects/${repo.externalId}/merge_requests/$pullRequestNumber/diffs")
            .map { it.gitDiff }
            .reduce { acc, s -> "$acc\n\n$s" }
    }

    override suspend fun latestPullRequest(): ScmPullRequest? {
        return client.get("projects/${repo.externalId}/merge_requests") {
            parameter("order_by", "created_at")
            parameter("sort", "desc")
            parameter("state", "all")
            parameter("per_page", 1)
        }.body<List<GitLabMergeRequest>>().firstOrNull()?.asScmPullRequest
    }

    override fun allIssues(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssue>> {
        return emptyFlow()
    }

    override fun allIssueComments(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssueComment>> {
        return emptyFlow()
    }

    override fun allPullRequests(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmPullRequest>> {
        return when (initialBatchUrl) {
            null -> client.gitLabBatchStream<GitLabMergeRequest>("projects/${repo.externalId}/merge_requests") {
                parameter("state", "all")
                since?.also {
                    parameter("updated_after", since.asISO8601)
                }
            }

            else -> client.gitLabBatchStream<GitLabMergeRequest>(initialBatchUrl.asString)
        }.map { batch ->
            HttpClientBatch(
                items = batch.items.map { it.asScmPullRequest },
                nextCursor = batch.nextCursor,
            )
        }
    }

    override fun allPullRequestComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment>> {
        return emptyFlow()
    }

    override fun allPullRequestCodeComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
        return emptyFlow()
    }

    override fun fileCommits(path: String, maxItems: Int?): Flow<ScmCommit> {
        return client.gitLabStream<GitLabCommit>(
            initialUri = "projects/${repo.externalId}/repository/commits",
            maxItems = maxItems,
        ) {
            parameter("path", path)
        }.map {
            it.asScmCommit
        }
    }

    override fun commitsBetweenCommits(base: Hash, head: Hash?, maxItems: Int?): Flow<ScmCommit> {
        return emptyFlow()
    }

    override fun commitsBetweenDates(
        since: Instant,
        until: Instant?,
        maxItems: Int?,
        authorUsername: String?,
    ): Flow<ScmCommit> {
        return client.gitLabStream<GitLabCommit>(
            initialUri = "projects/${repo.externalId}/repository/commits",
            maxItems = maxItems,
        ) {
            parameter("since", since.asISO8601)
            until?.also { until ->
                parameter("until", until.asISO8601)
            }
            authorUsername?.also { authorUsername ->
                parameter("author", authorUsername)
            }
        }.map {
            it.asScmCommit
        }
    }

    override fun commits(author: String?, includeDiffs: Boolean, maxItems: Int?): Flow<ScmCommit> = suspendedFlow {
        client.gitLabStream<GitLabCommit>(
            initialUri = "projects/${repo.externalId}/repository/commits",
            maxItems = maxItems,
        ) {
            author?.also {
                parameter("author", it)
            }
        }.map {
            it.asScmCommit
        }.let { commits ->
            if (includeDiffs) {
                commits.toList().parMapNotNull(context = Dispatchers.IO) {
                    it.sha?.let { sha ->
                        it.copy(diff = commitDiff(sha))
                    }
                }.asFlow()
            } else {
                commits
            }
        }
    }

    override fun contributorStats(): Flow<ScmContributorStats> {
        return emptyFlow()
    }

    override suspend fun commit(sha: Hash): ScmCommit {
        return client.get("projects/${repo.externalId}/repository/commits/${sha.asString()}").body<GitLabCommit>().asScmCommit
    }

    override suspend fun commitDiff(sha: Hash): String {
        return client.gitLabStream<GitLabDiff>("projects/${repo.externalId}/repository/commits/${sha.asString()}/diff")
            .map { it.gitDiff }
            .reduce { acc, s -> "$acc\n\n$s" }
    }

    override suspend fun fileContents(path: String, ref: String?): String {
        val encodedPath = path.encodeURLParameter()

        val fileSize = client.head("projects/${repo.externalId}/repository/files/$encodedPath") {
            parameter("ref", ref ?: "HEAD")
        }.headers["X-Gitlab-Size"]?.toLongOrNull()

        LOGGER.debugAsync(
            "path" to path,
            "fileSize" to fileSize.toString(),
        ) {
            "GitLabRepoApi::fileContents"
        }

        if (fileSize == null || fileSize > MAX_GITLAB_FILE_SIZE) {
            throw ScmFileTooLargeException()
        }

        return client.get("projects/${repo.externalId}/repository/files/$encodedPath") {
            parameter("ref", ref ?: "HEAD")
        }.body<GitLabFileContent>().content.lenientDecodeBase64String()
    }

    override suspend fun markdown(text: String): String {
        TODO("Not applicable")
    }

    // TODO make this more efficient by fetching diffs only for versions that we care about
    override fun pullRequestFiles(pullRequestNumber: Int, maxItems: Int?): Flow<ScmPullRequestFile> {
        return mergeRequestVersions(
            pullRequestNumber = pullRequestNumber,
            maxItems = maxItems,
        )
            .flatMapConcat { version ->
                mergeRequestVersionDiff(pullRequestNumber, version.id)
            }
    }

    private fun mergeRequestVersions(
        pullRequestNumber: Int,
        maxItems: Int?,
    ): Flow<GitLabMergeRequestVersion> {
        return client.gitLabStream<GitLabMergeRequestVersion>(
            initialUri = "projects/${repo.externalId}/merge_requests/$pullRequestNumber/versions",
            maxItems = maxItems,
        )
    }

    private fun mergeRequestVersionDiff(pullRequestNumber: Int, versionId: Int): Flow<ScmPullRequestFile> = suspendedFlow {
        client.get("projects/${repo.externalId}/merge_requests/$pullRequestNumber/versions/$versionId")
            .body<GitLabMergeRequestVersionDiff>()
            .let { versionDiff ->
                versionDiff.diffs.map { fileDiff ->
                    ScmPullRequestFile(
                        fileSha = null,
                        newFilePath = fileDiff.newFilePath,
                        oldFilePath = fileDiff.oldFilePath,
                        commitSha = versionDiff.headCommitSha,
                        diff = fileDiff.diff,
                    )
                }
            }
            .asFlow()
    }

    override fun pullRequestReviews(pullRequestNumber: Int): Flow<ScmPullRequestReview> {
        return emptyFlow()
    }

    override fun pullRequestAllComments(pullRequestNumber: Int): Flow<ScmPrComment> {
        val mergeRequestHtmlUrl: Url = URLBuilder(repo.repoUrl.webHtmlUrl).apply {
            pathSegments += listOf("-", "merge_requests", pullRequestNumber.toString())
        }.build()

        return client.gitLabStream<GitLabDiscussion>("projects/${repo.externalId}/merge_requests/$pullRequestNumber/discussions")
            // Filter out system notes
            .filterNot {
                it.notes.isEmpty() || it.notes.first().system
            }
            // Facilitate threaded comments by generating an inReplyToId group key
            .map { discussion ->
                val firstNote = discussion.notes.first().asScmPrComment(null, mergeRequestHtmlUrl)
                val inReplyToId = firstNote.id
                val replyNotes = discussion.notes.drop(1).map { it.asScmPrComment(inReplyToId, mergeRequestHtmlUrl) }
                listOf(firstNote) + replyNotes
            }
            .asFlatItemsFlow()
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun gitCredentials(scmTokenConfig: ScmTokenConfig?): ScmGitCredentials {
        return ScmGitCredentials.Basic(
            username = "oauth2",
            password = tokenProvider.getTokens(leeway = scmTokenConfig?.leeway).accessToken,
        )
    }

    override fun close() {
        client.close()
    }
}
