package com.nextchaptersoftware.scm.bitbucket

import arrow.fx.coroutines.parMapNotNull
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketBatchStream
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketStream
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketComment
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketCommit
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketFileContent
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPageData
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPageMeta
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPullRequest
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketPullRequestCommentBody
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketRepo
import com.nextchaptersoftware.scm.error.ScmFileTooLargeException
import com.nextchaptersoftware.scm.models.ScmCommentComponent
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmGitCredentials
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmReaction
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.asISO8601
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.head
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.http.Url
import io.ktor.http.isSuccess
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant

internal class BitbucketRepoApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val repo: Repo,
    scmTeam: ScmTeam,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmRepoApi {

    companion object {
        private const val MAX_FILE_SIZE = 1024L * 1024L // 1MB
    }

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun repo(): ScmRepository {
        return client.get("repositories/${repo.externalId}").body<BitbucketRepo>()
            .asScmRepository
    }

    override suspend fun headCommitShaOrNull(): Hash? {
        return getOrNullIfNotFound {
            client.get("repositories/${repo.fullName}/commit/HEAD").body<BitbucketCommit>()
                .hash.let { Hash.parse(it) }
        }
    }

    override suspend fun headCommitOrNull(): ScmCommit? {
        return getOrNullIfNotFound {
            client.get("repositories/${repo.fullName}/commit/HEAD").body<BitbucketCommit>().asScmCommit
        }
    }

    override suspend fun pullRequest(pullRequestNumber: Int): ScmPullRequest {
        return client.get("repositories/${repo.fullName}/pullrequests/$pullRequestNumber").body<BitbucketPullRequest>()
            .asScmPullRequest
    }

    override suspend fun pullRequestDiff(
        pullRequestNumber: Int,
        base: String?,
        head: String?,
    ): String {
        // FIXME: implement support for base...head
        return client.get("repositories/${repo.fullName}/pullrequests/$pullRequestNumber/diff").body<String>()
    }

    private fun pullRequestCommentUrl(
        pullRequestNumber: Int,
    ) = "repositories/${repo.fullName}/pullrequests/$pullRequestNumber/comments"

    private fun pullRequestCommentUrl(
        pullRequestNumber: Int,
        commentId: String,
    ) = "repositories/${repo.fullName}/pullrequests/$pullRequestNumber/comments/$commentId"

    override suspend fun pullRequestCommentCreate(
        pullRequestNumber: Int,
        text: String,
    ): ScmCommentComponent.Comment {
        val path = pullRequestCommentUrl(pullRequestNumber)
        return client.post(path) {
            setBody(
                BitbucketPullRequestCommentBody(
                    content = BitbucketPullRequestCommentBody.Content(raw = text),
                ),
            )
        }
            .body<BitbucketComment>()
            .asScmCommentComponent()
    }

    override suspend fun pullRequestCommentUpdate(
        pullRequestNumber: Int,
        commentId: String,
        text: String,
    ): ScmCommentComponent.Comment {
        val path = pullRequestCommentUrl(
            pullRequestNumber = pullRequestNumber,
            commentId = commentId,
        )

        // bitbucket allows deleted comments to be silently re-created
        // so performing a HEAD will validate that the comment actually exists
        client.head(path)

        return client.put(path) {
            setBody(
                BitbucketPullRequestCommentBody(
                    content = BitbucketPullRequestCommentBody.Content(raw = text),
                ),
            )
        }
            .body<BitbucketComment>()
            .asScmCommentComponent()
    }

    override suspend fun pullRequestCommentDelete(pullRequestNumber: Int, commentId: String): Boolean {
        val path = pullRequestCommentUrl(
            pullRequestNumber = pullRequestNumber,
            commentId = commentId,
        )
        return client.delete(path)
            .status.isSuccess()
    }

    override fun pullRequestCommentReactions(commentId: String): Flow<ScmReaction> {
        // bitbucket only allows comments to be liked, there's API no support for reading these
        return emptyFlow()
    }

    override suspend fun latestPullRequest(): ScmPullRequest? {
        return client.get("repositories/${repo.fullName}/pullrequests") {
            parameter("sort", "-created_on")
            parameter("state", "OPEN")
            parameter("state", "MERGED")
            parameter("state", "DECLINED")
            parameter("state", "SUPERCEDED")
            parameter("pagelen", 1)
        }.body<BitbucketPageData<BitbucketPullRequest>>().values.firstOrNull()?.asScmPullRequest
    }

    override fun allIssues(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssue>> {
        return emptyFlow()
    }

    override fun allIssueComments(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssueComment>> {
        return emptyFlow()
    }

    override fun allPullRequests(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmPullRequest>> {
        return when (initialBatchUrl) {
            null -> client.bitbucketBatchStream<BitbucketPullRequest>("repositories/${repo.fullName}/pullrequests") {
                parameter("state", "OPEN")
                parameter("state", "MERGED")
                parameter("state", "DECLINED")
                parameter("sort", "-created_on")
                since?.also {
                    parameter("q", "updated_on > ${since.asISO8601}")
                }
            }

            else -> client.bitbucketBatchStream<BitbucketPullRequest>(initialBatchUrl.asString)
        }.map { batch ->
            HttpClientBatch(
                items = batch.items.map { it.asScmPullRequest },
                nextCursor = batch.nextCursor,
            )
        }
    }

    override fun allPullRequestComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment>> {
        return emptyFlow()
    }

    override fun allPullRequestCodeComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
        return emptyFlow()
    }

    override fun pullRequestFiles(pullRequestNumber: Int, maxItems: Int?): Flow<ScmPullRequestFile> {
        return emptyFlow()
    }

    override fun pullRequestReviews(pullRequestNumber: Int): Flow<ScmPullRequestReview> {
        return emptyFlow()
    }

    override fun fileCommits(path: String, maxItems: Int?): Flow<ScmCommit> {
        return client.bitbucketStream<BitbucketCommit>(
            initialUri = "repositories/${repo.fullName}/commits?path=$path",
            maxItems = maxItems,
        ).map { it.asScmCommit }
    }

    /**
     * https://developer.atlassian.com/cloud/bitbucket/rest/api-group-source/#api-repositories-workspace-repo-slug-src-commit-path-get
     */
    override suspend fun fileContents(path: String, ref: String?): String {
        val fileMeta = client.get(
            "repositories/${repo.fullName}/src/${ref ?: "HEAD"}/$path?format=meta",
        ).body<BitbucketFileContent>()

        if (fileMeta.attributes.isNotEmpty() || fileMeta.size > MAX_FILE_SIZE) {
            throw ScmFileTooLargeException()
        }

        return client.get("repositories/${repo.fullName}/src/${ref ?: "HEAD"}/$path").bodyAsText()
    }

    override fun commitsBetweenCommits(base: Hash, head: Hash?, maxItems: Int?): Flow<ScmCommit> {
        return client.bitbucketStream<BitbucketCommit>(
            initialUri = "repositories/${repo.fullName}/commits/${head?.asShortString() ?: "HEAD"}?exclude=${base.asShortString()}",
            maxItems = maxItems,
        ).map { it.asScmCommit }
    }

    override fun commitsBetweenDates(
        since: Instant,
        until: Instant?,
        maxItems: Int?,
        authorUsername: String?,
    ): Flow<ScmCommit> {
        return emptyFlow()
    }

    override fun commits(author: String?, includeDiffs: Boolean, maxItems: Int?): Flow<ScmCommit> = suspendedFlow fn@{
        if (author != null) {
            // Author filter not supported by Bitbucket API
            return@fn emptyFlow()
        }
        return@fn client.bitbucketStream<BitbucketCommit>(
            initialUri = "repositories/${repo.fullName}/commits",
            maxItems = maxItems,
        ).map {
            it.asScmCommit
        }.let { commits ->
            if (includeDiffs) {
                commits.toList().parMapNotNull(context = Dispatchers.IO) {
                    it.sha?.let { sha ->
                        it.copy(diff = commitDiff(sha))
                    }
                }.asFlow()
            } else {
                commits
            }
        }
    }

    override fun contributorStats(): Flow<ScmContributorStats> {
        return emptyFlow()
    }

    override suspend fun commit(sha: Hash): ScmCommit? {
        return getOrNullIfNotFound {
            client.get("repositories/${repo.fullName}/commit/${sha.asShortString()}").body<BitbucketCommit>().asScmCommit
        }
    }

    override suspend fun commitDiff(sha: Hash): String? {
        return getOrNullIfNotFound {
            client.get("repositories/${repo.fullName}/diff/${sha.asShortString()}").body<String>()
        }
    }

    override suspend fun markdown(text: String): String {
        TODO("Not applicable")
    }

    private suspend fun <T> getOrNullIfNotFound(body: suspend () -> T): T? {
        return runSuspendCatching {
            body()
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.NotFound) {
                null
            } else {
                throw it
            }
        }
    }

    // https://developer.atlassian.com/cloud/bitbucket/rest/api-group-pullrequests/#api-repositories-workspace-repo-slug-pullrequests-pull-request-id-comments-get
    @Suppress("SpreadOperator")
    override fun pullRequestAllComments(pullRequestNumber: Int): Flow<ScmPrComment> {
        return client.bitbucketStream<BitbucketComment>("repositories/${repo.fullName}/pullrequests/$pullRequestNumber/comments") {
            parameter("q", "deleted = false")
            parameter(
                "fields",
                listOf(
                    *BitbucketPageMeta.FIELD_NAMES,
                    "values.content",
                    "values.created_on",
                    "values.id",
                    "values.inline",
                    "values.links",
                    "values.parent.id",
                    "values.pullrequest.source.commit.hash",
                    "values.updated_on",
                    "values.user",
                ).joinToString(","),
            )
        }
            // filter out outdated code comments
            .filter {
                when (it.shortSha) {
                    // not a code comment
                    null -> true

                    // code comment commit matches the PR head commit
                    it.prHeadShortSha -> true

                    // outdated code comment
                    else -> false
                }
            }
            // filter out left-side code comments
            .filterNot { it.inline?.from != null }
            .map {
                val rawDiff = when (it.parent?.id) {
                    null -> it.links.code?.href?.let { codeUrl -> client.get(codeUrl).bodyAsText() } ?: ""
                    else -> ""
                }
                it.getAsScmPrComment(pullRequestNumber, rawDiff)
            }
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun gitCredentials(scmTokenConfig: ScmTokenConfig?): ScmGitCredentials {
        return ScmGitCredentials.Basic(
            username = "x-token-auth",
            password = tokenProvider.getTokens(leeway = scmTokenConfig?.leeway).accessToken,
        )
    }

    override fun close() {
        client.close()
    }
}
