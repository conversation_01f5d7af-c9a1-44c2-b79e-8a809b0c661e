package com.nextchaptersoftware.scm.bitbucketdatacenter

import arrow.fx.coroutines.parMapNotNull
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.kotlinx.coroutines.flow.asFlatFlow
import com.nextchaptersoftware.kotlinx.coroutines.flow.asFlatItemsFlow
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.client.HttpClientBatch
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmRepoApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterPagination.bitbucketDataCenterPaginatedStream
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterChange
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterCommit
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterMarkup
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPullRequest
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPullRequestActivity
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPullRequestOrder
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPullRequestState
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterRepo
import com.nextchaptersoftware.scm.models.ScmCommit
import com.nextchaptersoftware.scm.models.ScmContributorStats
import com.nextchaptersoftware.scm.models.ScmGitCredentials
import com.nextchaptersoftware.scm.models.ScmIssue
import com.nextchaptersoftware.scm.models.ScmIssueComment
import com.nextchaptersoftware.scm.models.ScmPrComment
import com.nextchaptersoftware.scm.models.ScmPullRequest
import com.nextchaptersoftware.scm.models.ScmPullRequestFile
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.FlowExtensions.takeOnNotNull
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.ResponseException
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLBuilder
import io.ktor.http.Url
import io.ktor.http.parameters
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.take
import kotlinx.coroutines.flow.toList
import kotlinx.datetime.Instant

internal class BitbucketDataCenterRepoApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val repo: Repo,
    private val baseApiUrl: Url,
    scmTeam: ScmTeam,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmRepoApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    private val repoBasePath: String
        get() = "projects/${repo.externalOwner}/repos/${repo.externalAltName ?: repo.externalName}"

    private val pullRequestBasePath: String
        get() = "$repoBasePath/pull-requests"

    private fun pullRequestBasePath(id: Int): String = "$pullRequestBasePath/$id"

    override suspend fun repo(): ScmRepository {
        return client
            .get(repoBasePath)
            .body<BitbucketDataCenterRepo>()
            .asScmRepository
    }

    override suspend fun headCommitShaOrNull(): Hash? {
        return getOrNullIfNotFound {
            client
                .get("$repoBasePath/commits/HEAD")
                .body<BitbucketDataCenterCommit>()
                .sha
        }
    }

    override suspend fun headCommitOrNull(): ScmCommit? {
        return getOrNullIfNotFound {
            client
                .get("$repoBasePath/commits/HEAD")
                .body<BitbucketDataCenterCommit>()
                .asScmCommit
        }
    }

    private suspend fun getPullRequest(pullRequestNumber: Int): BitbucketDataCenterPullRequest {
        return client
            .get(pullRequestBasePath(pullRequestNumber))
            .body<BitbucketDataCenterPullRequest>()
    }

    override suspend fun pullRequest(pullRequestNumber: Int): ScmPullRequest {
        return getPullRequest(pullRequestNumber)
            .asScmPullRequest
    }

    override suspend fun pullRequestDiff(
        pullRequestNumber: Int,
        base: String?,
        head: String?,
    ): String {
        TODO("Not yet implemented")
    }

    override suspend fun latestPullRequest(): ScmPullRequest? {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterPullRequest>(
                path = pullRequestBasePath,
                pageSize = 1,
            ) {
                parameter("state", BitbucketDataCenterPullRequestState.ALL)
                parameter("order", BitbucketDataCenterPullRequestOrder.NEWEST)
            }
            .take(1)
            .asFlatItemsFlow()
            .firstOrNull()
            ?.asScmPullRequest
    }

    override fun allIssues(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssue>> {
        // Bitbucket Data Center does not have issues
        return emptyFlow()
    }

    override fun allIssueComments(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmIssueComment>> {
        // Bitbucket Data Center does not have issues
        return emptyFlow()
    }

    override fun allPullRequests(initialBatchUrl: Url?, since: Instant?): Flow<HttpClientBatch<ScmPullRequest>> {
        val pageStartFrom = initialBatchUrl?.parameters?.get("start")?.toInt()

        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterPullRequest>(
                path = pullRequestBasePath,
                pageStartFrom = pageStartFrom,
            ) {
                parameter("state", BitbucketDataCenterPullRequestState.ALL)
                parameter("order", BitbucketDataCenterPullRequestOrder.NEWEST)
            }
            .map { page ->
                HttpClientBatch(
                    items = page.values.map { it.asScmPullRequest },
                    nextCursor = page.nextPageStart?.let { nextPageStart ->
                        URLBuilder("$baseApiUrl/$pullRequestBasePath").apply {
                            parameters {
                                set("start", nextPageStart.toString())
                            }
                        }
                            .build()
                    },
                )
            }
    }

    override fun allPullRequestComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment>> {
        // Bitbucket Data Center requires a PR for getting comments
        return emptyFlow()
    }

    override fun allPullRequestCodeComments(initialBatchUrl: Url?): Flow<HttpClientBatch<ScmPrComment.CodeLevel>> {
        // Bitbucket Data Center requires a PR for getting code comments
        return emptyFlow()
    }

    override fun pullRequestFiles(pullRequestNumber: Int, maxItems: Int?): Flow<ScmPullRequestFile> {
        return getPullRequestCommits(pullRequestNumber)
            .map(::getCommitChanges)
            .asFlatFlow()
            .takeOnNotNull(maxItems)
            .map { it.asScmPullRequestFile }
    }

    private fun getPullRequestCommits(pullRequestNumber: Int): Flow<BitbucketDataCenterCommit> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterCommit>(
                "${pullRequestBasePath(pullRequestNumber)}/commits",
            )
            .asFlatItemsFlow()
    }

    override fun pullRequestReviews(pullRequestNumber: Int): Flow<ScmPullRequestReview> {
        /**
         * Bitbucket Data Center publish reviews as activities, however the relevant pieces
         * of data will be exposed as [BitbucketDataCenterPullRequestActivity.Action.COMMENTED]
         *
         * @see BitbucketDataCenterPullRequestActivity.Action for more info
         */
        return emptyFlow()
    }

    override suspend fun fileContents(path: String, ref: String?): String {
        TODO("Not yet implemented")
    }

    override fun pullRequestAllComments(pullRequestNumber: Int): Flow<ScmPrComment> = flow {
        val pullRequest = getPullRequest(pullRequestNumber)
        client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterPullRequestActivity>("${pullRequestBasePath(pullRequestNumber)}/activities")
            .asFlatItemsFlow()
            .filter { it.action == BitbucketDataCenterPullRequestActivity.Action.COMMENTED }
            .map { it.toFlatScmPrCommentList(pullRequest) }
            .asFlatItemsFlow()
            .collect(this::emit)
    }

    override fun fileCommits(path: String, maxItems: Int?): Flow<ScmCommit> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterCommit>(
                "$repoBasePath/commits",
            ) {
                parameter("path", path)
                parameter("followRenames", true)
            }
            .asFlatItemsFlow()
            .takeOnNotNull(maxItems)
            .map { it.asScmCommit }
    }

    override fun commitsBetweenCommits(base: Hash, head: Hash?, maxItems: Int?): Flow<ScmCommit> {
        // Somewhat counterintuitively, the `since` parameter is the base commit and the `until` parameter is the head commit
        return client.bitbucketDataCenterPaginatedStream<BitbucketDataCenterCommit>(
            path = "$repoBasePath/commits",
        ) {
            parameter("since", base.asString())
            head?.let { parameter("until", it.asString()) }
        }
            .asFlatItemsFlow()
            .takeOnNotNull(maxItems)
            .map { it.asScmCommit }
    }

    override fun commitsBetweenDates(
        since: Instant,
        until: Instant?,
        maxItems: Int?,
        authorUsername: String?,
    ): Flow<ScmCommit> {
        return emptyFlow()
    }

    private suspend fun getCommit(sha: Hash): BitbucketDataCenterCommit {
        return client
            .get("$repoBasePath/commits/$sha")
            .body<BitbucketDataCenterCommit>()
    }

    private fun commitBasePath(sha: Hash): String = commitBasePath(sha.asString())

    private fun commitBasePath(commitId: String): String {
        return "$repoBasePath/commits/$commitId"
    }

    override suspend fun commit(sha: Hash): ScmCommit? {
        return getOrNullIfNotFound {
            getCommit(sha).asScmCommit
        }
    }

    override fun commits(author: String?, includeDiffs: Boolean, maxItems: Int?): Flow<ScmCommit> = suspendedFlow fn@{
        if (author != null) {
            // Bitbucket Data Center does not support filtering by author
            return@fn emptyFlow()
        }

        return@fn client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterCommit>(
                path = "$repoBasePath/commits",
            )
            .asFlatItemsFlow()
            .takeOnNotNull(maxItems)
            .map { it.asScmCommit }
            .let { commits ->
                if (includeDiffs) {
                    commits.toList().parMapNotNull(context = Dispatchers.IO) {
                        it.sha?.let { sha ->
                            it.copy(diff = commitDiff(sha))
                        }
                    }.asFlow()
                } else {
                    commits
                }
            }
    }

    private fun getCommitChanges(commit: BitbucketDataCenterCommit): Flow<BitbucketDataCenterChange> = getCommitChanges(commit.sha)

    private fun getCommitChanges(sha: Hash): Flow<BitbucketDataCenterChange> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterChange>(
                path = """${commitBasePath(sha)}/changes""",
            )
            .asFlatItemsFlow()
    }

    override fun contributorStats(): Flow<ScmContributorStats> {
        return emptyFlow()
    }

    override suspend fun commitDiff(sha: Hash): String? {
        return null
    }

    override suspend fun markdown(text: String): String {
        return client
            .post("/markup/preview") {
                setBody(text)
            }
            .body<BitbucketDataCenterMarkup>()
            .html
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun gitCredentials(scmTokenConfig: ScmTokenConfig?): ScmGitCredentials {
        return ScmGitCredentials.Bearer(
            token = tokenProvider.getTokens(leeway = scmTokenConfig?.leeway).accessToken,
        )
    }

    override fun close() {
        client.close()
    }

    private suspend fun <T> getOrNullIfNotFound(body: suspend () -> T): T? {
        return runSuspendCatching {
            body()
        }.getOrElse {
            if (it is ResponseException && it.response.status == HttpStatusCode.NotFound) {
                null
            } else {
                throw it
            }
        }
    }
}
