package com.nextchaptersoftware.scm.bitbucket

import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.expectScmConfig
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver

class BitbucketPipelinesClientProvider(
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val userSecretServiceResolver: UserSecretServiceResolver,
) {
    suspend fun create(
        externalInstallationId: String,
        externalRepositoryId: String,
    ): BitbucketPipelinesClient {
        val scmTeam = scmTeamStore.findByProviderExternalId(
            provider = Provider.Bitbucket,
            providerEnterpriseId = null,
            providerExternalId = externalInstallationId,
        )
            ?: error("ScmTeam not found: $externalInstallationId")

        val repo = repoStore.findByExternalId(
            teamId = scmTeam.id,
            provider = Provider.Bitbucket,
            providerExternalId = externalRepositoryId,
        )
            ?: error("Repo not found: $externalRepositoryId")

        return create(
            scmTeam = scmTeam,
            repo = repo,
        )
    }

    suspend fun create(
        scmTeam: ScmTeam,
        repo: Repo,
    ): BitbucketPipelinesClient {
        val tokenProvider = run {
            val userSecretService = userSecretServiceResolver.resolve(
                provider = Provider.Bitbucket,
            )

            val authApi = scmAuthApiFactory.getApi(orgId = scmTeam.orgId, oAuthApiType = Scm.Bitbucket)

            ScmUserTokenProvider.RepoOAuthRefresh(
                oauthRefreshService = UserSecretOAuthRefreshService(
                    tokenRefresher = authApi,
                    tokenPersistence = EncryptedTokenPersistence(userSecretService),
                ),
                teamId = scmTeam.id,
                repoId = repo.id,
            )
        }

        return BitbucketPipelinesClient(
            client = createHttpClient(
                scmTeam = scmTeam,
                tokenProvider = tokenProvider,
            ),
        )
    }

    internal fun createHttpClient(
        scmTeam: ScmTeam,
        tokenProvider: ScmUserTokenProvider,
    ) = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = expectScmConfig(ScmConfig.INSTANCE.bitbucketCloud).apiBaseUrl.asUrl,
    )
}
