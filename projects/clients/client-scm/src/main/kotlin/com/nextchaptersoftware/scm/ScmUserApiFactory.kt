package com.nextchaptersoftware.scm

import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.azuredevops.AzureDevOpsUserApi
import com.nextchaptersoftware.scm.bitbucket.BitbucketUserApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterCommonApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterUserApi
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.github.GitHubUserApi
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi
import com.nextchaptersoftware.scm.gitlab.GitLabUserApi
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.utils.ensureTrailingSlash
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url

class ScmUserApiFactory(
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val scmConfig: ScmConfig,
    private val appConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    private val userSecretServiceResolver: UserSecretServiceResolver,
) {
    suspend fun getApiFromIdentity(
        orgId: OrgId?,
        identityId: IdentityId,
        scm: Scm,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): ScmUserApi {
        val userSecretService = userSecretServiceResolver.resolve(
            provider = scm.provider,
        )

        val authApi = scmAuthApiFactory.getApi(orgId = orgId, oAuthApiType = scm)
        val tokenProvider = ScmUserTokenProvider.IdentityOAuthRefresh(
            oauthRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = authApi,
                tokenPersistence = EncryptedTokenPersistence(userSecretService),
            ),
            identityId = identityId,
        )
        return getApi(
            orgId = orgId,
            tokenProvider = tokenProvider,
            scm = scm,
            clientEngine = clientEngine,
        )
    }

    suspend fun getApiFromTokens(
        orgId: OrgId?,
        tokens: OAuthTokens,
        scm: Scm,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): ScmUserApi {
        val tokenProvider = ScmUserTokenProvider.FixedToken(tokens)
        return getApi(
            orgId = orgId,
            tokenProvider = tokenProvider,
            scm = scm,
            clientEngine = clientEngine,
        )
    }

    private suspend fun getApi(
        orgId: OrgId?,
        tokenProvider: ScmUserTokenProvider,
        scm: Scm,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): ScmUserApi {
        return when (scm) {
            Scm.AzureDevOps -> {
                val config = expectScmConfig(scmConfig.azureDevOps)
                AzureDevOpsUserApi(
                    orgId = orgId,
                    baseApiUrl = Url(config.apiBaseUrl),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                    identityId = tokenProvider.getIdentityId(),
                )
            }

            Scm.Bitbucket -> {
                val config = expectScmConfig(scmConfig.bitbucketCloud)
                BitbucketUserApi(
                    orgId = orgId,
                    baseApiUrl = Url(config.apiBaseUrl),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            Scm.GitHub -> {
                val config = expectScmConfig(scmConfig.githubCloud)
                GitHubUserApi(
                    orgId = orgId,
                    restApiUrl = Url(config.apiBaseUrl),
                    graphApiUrl = Url(config.apiBaseUrl.ensureTrailingSlash.plus("graphql")),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            Scm.GitLab -> {
                val config = expectScmConfig(scmConfig.gitlabCloud)
                GitLabUserApi(
                    orgId = orgId,
                    baseApiUrl = Url(config.apiBaseUrl),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            is Scm.GitHubEnterprise -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider)
                return GitHubUserApi(
                    orgId = orgId,
                    restApiUrl = Url("https://${enterpriseConfig.authority}/api/v3/"),
                    graphApiUrl = Url("https://${enterpriseConfig.authority}/api/graphql"),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            is Scm.GitLabSelfHosted -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider)
                return GitLabUserApi(
                    orgId = orgId,
                    baseApiUrl = GitLabCommonApi.apiBaseUrl(enterpriseConfig.authority),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            is Scm.BitbucketDataCenter -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider)
                return BitbucketDataCenterUserApi(
                    orgId = orgId,
                    baseApiUrl = BitbucketDataCenterCommonApi.apiBaseUrl(enterpriseConfig.authority),
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }
        }
    }
}
