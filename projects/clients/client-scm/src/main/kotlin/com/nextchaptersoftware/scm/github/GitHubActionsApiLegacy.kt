package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.scm.CiApiLegacy
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestCheckRuns
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestRef
import com.nextchaptersoftware.scm.models.CiBuildRunJobLegacy
import com.nextchaptersoftware.scm.models.CiBuildRunLegacy
import com.nextchaptersoftware.scm.models.CiPullRequestStatus
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import io.ktor.client.call.body
import io.ktor.client.request.get
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.map

internal class GitHubActionsApiLegacy(
    scmTeam: ScmTeam,
    gitHubActionsClientProvider: GitHubActionsClientProvider,
    enterpriseAppConfigId: EnterpriseAppConfigId?,
    providerInstallationId: String,
) : CiApiLegacy {
    private val client = gitHubActionsClientProvider.create(
        scmTeam = scmTeam,
        enterpriseAppConfigId = enterpriseAppConfigId,
        providerInstallationId = providerInstallationId,
    )

    override suspend fun buildRun(
        scmTeam: ScmTeam,
        repoExternalId: String,
        runId: String,
    ): CiBuildRunLegacy {
        suspend fun logs() = client.workflowRunLogs(
            repositoryId = repoExternalId,
            runId = runId,
        )

        return client.workflowRun(
            repositoryId = repoExternalId,
            runId = runId,
        )
            .asCiBuildRunLegacy(
                output = logs(),
            )
    }

    override suspend fun buildRunJob(
        scmTeam: ScmTeam,
        repoExternalId: String,
        jobId: String,
    ): CiBuildRunJobLegacy {
        suspend fun logs() = client.workflowJobLogs(
            repositoryId = repoExternalId,
            jobId = jobId,
        )

        return client.workflowJob(
            repositoryId = repoExternalId,
            jobId = jobId,
        )
            .asCiBuildRunJobLegacy(
                output = logs(),
            )
    }

    override fun pullRequestStatus(
        scmTeam: ScmTeam,
        repoExternalId: String,
        pullRequestNumber: Int,
    ): Flow<CiPullRequestStatus> = suspendedFlow fn@{
        // FIXME: using the underlying http client
        val client = client.http

        return@fn client.get("repositories/$repoExternalId/pulls/$pullRequestNumber").body<GitHubPullRequestRef>().head.sha.let { sha ->
            client.get("repositories/$repoExternalId/commits/$sha/check-runs").body<GitHubPullRequestCheckRuns>().checkRuns.asFlow()
                .filterNot { it.isSkipped }
                .map { it.asCiPullRequestStatus }
        }
    }

    override fun close() {
        client.close()
    }
}
