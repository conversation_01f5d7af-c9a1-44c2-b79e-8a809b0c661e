package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi.getGroupRepos
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi.getUserRepos
import com.nextchaptersoftware.scm.gitlab.GitLabPagination.gitLabStream
import com.nextchaptersoftware.scm.gitlab.models.GitLabOrg
import com.nextchaptersoftware.scm.gitlab.models.GitLabUser
import com.nextchaptersoftware.scm.gitlab.models.GitLabUserEmail
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map

internal class GitLabUserApi(
    private val tokenProvider: ScmUserTokenProvider,
    orgId: OrgId?,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmUserApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun user(): ScmAuthUser {
        val user = getUser()
        val userEmails: List<GitLabUserEmail> = client.get("user/emails").body()

        return ScmAuthUser(
            externalId = user.id.toString(),
            avatarUrl = user.avatarUrl,
            htmlUrl = user.webUrl,
            username = user.username,
            displayName = user.name,
            primaryEmail = requireNotNull(user.email) { "Expected GitLab '/user' endpoint to have a primary email" }.let(EmailAddress::of),
            emails = userEmails.filter { it.confirmedAt != null }.map { it.email }.plus(user.email).distinct().map(EmailAddress::of),
            isBot = user.bot,
            oauthTokens = tokenProvider.getTokens(),
        )
    }

    override fun accounts(): Flow<ScmAccount> = flow {
        emitAll(orgs())
        emit(getUser().asScmUser.let(ScmAccount::User))
    }

    private fun orgs(): Flow<ScmAccount> {
        return client.gitLabStream<GitLabOrg>("groups") {
            parameter("top_level_only", true)
        }.map { it.asScmOrg.let(ScmAccount::Org) }
    }

    private suspend fun getUser(): GitLabUser {
        return client.get("user").body()
    }

    override suspend fun installation(externalInstallationId: String): ScmInstallationAccount? {
        error("Not applicable")
    }

    override suspend fun accessibleInstallations(externalUserId: String): List<ScmInstallationAccount> {
        error("Not applicable")
    }

    override fun repos(externalInstallationId: String): Flow<List<ScmRepository>> = suspendedFlow fn@{
        val isScmUser = getUser().id.toString() == externalInstallationId
        return@fn when {
            isScmUser -> client.getUserRepos(externalUserId = externalInstallationId)
            else -> client.getGroupRepos(externalOrgId = externalInstallationId)
        }.map { batch ->
            batch.items
                .map { it.asScmRepository }
                .filterNot { it.isArchived }
                .filterNot { it.isDisabled }
        }
    }

    override suspend fun createIssueComment(owner: String, repoName: String, issueNumber: Int, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun updateIssueComment(owner: String, repoName: String, commentId: String, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun deleteIssueComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updateReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        reviewId: String,
        body: GitHubBody,
    ): ScmPullRequestReview {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        body: GithubReactionBody,
    ): GitHubReaction {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewCommentReaction(owner: String, repoName: String, commentId: String, reactionId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequest(
        owner: String,
        repoName: String,
        prNumber: Int,
        body: GitHubPullRequestUpdateBody,
    ): GitHubPullRequest {
        TODO("Not yet implemented")
    }

    override fun close() {
        client.close()
    }
}
