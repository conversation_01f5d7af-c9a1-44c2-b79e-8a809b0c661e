package com.nextchaptersoftware.scm

import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.azuredevops.AzureDevOpsTeamApi
import com.nextchaptersoftware.scm.bitbucket.BitbucketTeamApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterCommonApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterTeamApi
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.github.GitHubTeamApi
import com.nextchaptersoftware.scm.gitlab.GitLabCommonApi
import com.nextchaptersoftware.scm.gitlab.GitLabTeamApi
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url

class ScmTeamApiFactory(
    private val appConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
    private val scmAppApiFactory: ScmAppApiFactory,
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val scmConfig: ScmConfig,
    private val userSecretServiceResolver: UserSecretServiceResolver,
) {

    suspend fun getApiFromTeam(
        scmTeam: ScmTeam,
        scm: Scm,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): ScmTeamApi {
        val userSecretService = userSecretServiceResolver.resolve(
            provider = scm.provider,
        )

        val authApi = scmAuthApiFactory.getApi(orgId = scmTeam.orgId, oAuthApiType = scm)
        val tokenProvider = ScmUserTokenProvider.TeamOAuthRefresh(
            oauthRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = authApi,
                tokenPersistence = EncryptedTokenPersistence(userSecretService),
            ),

            teamId = scmTeam.id,
        )
        return getApi(tokenProvider, scm, scmTeam, clientEngine)
    }

    private suspend fun getApi(
        tokenProvider: ScmUserTokenProvider,
        scm: Scm,
        scmTeam: ScmTeam,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    ): ScmTeamApi {
        return when (scm) {
            Scm.AzureDevOps -> {
                val config = expectScmConfig(scmConfig.azureDevOps)
                AzureDevOpsTeamApi(
                    baseApiUrl = Url(config.apiBaseUrl),
                    scmTeam = scmTeam,
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                    identityId = tokenProvider.getIdentityId(),
                )
            }

            Scm.Bitbucket -> {
                val config = expectScmConfig(scmConfig.bitbucketCloud)
                BitbucketTeamApi(
                    baseApiUrl = Url(config.apiBaseUrl),
                    scmTeam = scmTeam,
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            Scm.GitLab -> {
                val config = expectScmConfig(scmConfig.gitlabCloud)
                GitLabTeamApi(
                    baseApiUrl = Url(config.apiBaseUrl),
                    scmTeam = scmTeam,
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            Scm.GitHub,
            is Scm.GitHubEnterprise,
                -> {
                val appApi = scmAppApiFactory.getApi(scmTeam = scmTeam, scm = scm)
                val orgV3Api = scmTeam.providerExternalInstallationId?.let { appApi.V3Org(it) }
                    ?: throw ScmTeamNotInstalledException(scmTeam.id.toString())
                val orgV4Api = scmTeam.providerExternalInstallationId?.let { appApi.V4Org(it) }
                    ?: throw ScmTeamNotInstalledException(scmTeam.id.toString())
                GitHubTeamApi(scmTeam, appApi, orgV3Api, orgV4Api)
            }

            is Scm.GitLabSelfHosted -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider) as EnterpriseAppConfig.GitLab
                GitLabTeamApi(
                    baseApiUrl = GitLabCommonApi.apiBaseUrl(enterpriseConfig.authority),
                    scmTeam = scmTeam,
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }

            is Scm.BitbucketDataCenter -> {
                val enterpriseConfig = appConfigStore.getById(scm.enterpriseId, scm.provider)
                BitbucketDataCenterTeamApi(
                    baseApiUrl = BitbucketDataCenterCommonApi.apiBaseUrl(enterpriseConfig.authority),
                    scmTeam = scmTeam,
                    clientEngine = clientEngine,
                    tokenProvider = tokenProvider,
                )
            }
        }
    }
}
