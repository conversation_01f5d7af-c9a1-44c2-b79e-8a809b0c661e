package com.nextchaptersoftware.scm.gitlab.models

import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitLabJob(
    val id: Long,
    val name: String,
    val stage: String,

    val pipeline: GitLabPipelineRef,

    @SerialName("web_url")
    val webUrl: String,

    val status: GitLabStatus,

    @SerialName("created_at")
    val createdAt: Instant,

    @SerialName("started_at")
    val startedAt: Instant? = null,

    @SerialName("finished_at")
    val finishedAt: Instant? = null,

    @SerialName("retries_count")
    val retriesCount: Long? = null,
)
