package com.nextchaptersoftware.scm.bitbucket

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmNoAuthApi
import com.nextchaptersoftware.scm.ScmNoAuthHttpClientFactory.makeScmNoAuthHttpClient
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketOrg
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketUser
import com.nextchaptersoftware.scm.github.models.AppConfig
import com.nextchaptersoftware.scm.models.HostAvailabilityStatus
import com.nextchaptersoftware.scm.models.ScmOrg
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmUser
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.get
import io.ktor.http.Url

internal class BitbucketNoAuthApi(
    private val baseApiUrl: Url,
    orgId: OrgId?,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmNoAuthApi {

    private val client = makeScmNoAuthHttpClient(
        orgId = orgId,
        baseApiUrl = baseApiUrl,
        userAgent = "getunblocked",
        clientEngine = clientEngine,
    )

    override suspend fun getOrgByLogin(login: String): ScmOrg {
        return client.get("workspaces/$login").body<BitbucketOrg>().asScmOrg
    }

    override suspend fun getUserByLogin(login: String): ScmUser {
        return client.get("users/$login").body<BitbucketUser>().asScmUser
    }

    override suspend fun getPublicRepo(orgName: String, repoName: String): ScmRepository {
        error("Not applicable")
    }

    override suspend fun getPublicRepoLanguages(orgName: String, repoName: String): Map<String, Int> {
        error("Not applicable")
    }

    override suspend fun getHostAvailability(): HostAvailabilityStatus {
        error("Not applicable")
    }

    override suspend fun completeAppManifest(code: String): AppConfig {
        error("Not applicable")
    }
}
