package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.graphql.client.GraphQLHttpClient
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.expectScmConfig
import com.nextchaptersoftware.security.token
import com.nextchaptersoftware.utils.ensureTrailingSlash
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.defaultRequest

class GitHubActionsClientProvider(
    private val clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    private val scmAppApiFactory: ScmAppApiFactory,
) {
    private val restApiUrl by lazy {
        val config = expectScmConfig(ScmConfig.INSTANCE.githubCloud)
        config.apiBaseUrl.ensureTrailingSlash.asUrl
    }

    fun create(
        orgId: OrgId,
        enterpriseAppConfigId: EnterpriseAppConfigId?,
        providerInstallationId: String,
    ) = GitHubActionsClient(
        baseUrl = restApiUrl.asString.ensureTrailingSlash,
        client = createHttpClient(
            orgId = orgId,
            enterpriseAppConfigId = enterpriseAppConfigId,
            providerInstallationId = providerInstallationId,
        ),
    )

    fun create(
        scmTeam: ScmTeam,
        enterpriseAppConfigId: EnterpriseAppConfigId?,
        providerInstallationId: String,
    ) = GitHubActionsClient(
        baseUrl = restApiUrl.asString.ensureTrailingSlash,
        client = createHttpClient(
            orgId = scmTeam.orgId,
            enterpriseAppConfigId = enterpriseAppConfigId,
            providerInstallationId = providerInstallationId,
        ),
    )

    internal fun createHttpClient(
        orgId: OrgId,
        enterpriseAppConfigId: EnterpriseAppConfigId?,
        providerInstallationId: String,
    ) = GraphQLHttpClient.create(
        orgId = orgId,
        clientEngine = clientEngine,
    ).config {
        install(Auth) {
            token {
                loadToken {
                    val scm = when (enterpriseAppConfigId) {
                        null -> Scm.GitHub

                        else -> Scm.fromProvider(
                            provider = Provider.GitHubEnterprise,
                            enterpriseId = enterpriseAppConfigId,
                        )
                    }
                    scmAppApiFactory.getApi(orgId = orgId, scm = scm)
                        .appInstallTokenProvider
                        .getInstallToken(installationId = providerInstallationId)
                        .token
                }
            }
        }
        install(HttpRequestRetry)
        expectSuccess = true
        defaultRequest {
            url(restApiUrl.asString.ensureTrailingSlash)
        }
    }
}
