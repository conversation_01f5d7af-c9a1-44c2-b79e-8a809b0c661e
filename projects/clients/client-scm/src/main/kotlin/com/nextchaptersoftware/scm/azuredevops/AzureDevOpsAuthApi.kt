@file:Suppress("UnusedPrivateMember")

package com.nextchaptersoftware.scm.azuredevops

import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokenResponse
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.HttpExtensions.logCatchingDeserialize
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmAuthHttpClientFactory.makeScmAuthHttpClient
import com.nextchaptersoftware.scm.ScmWeb
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.forms.submitForm
import io.ktor.http.Parameters
import io.ktor.http.Url

internal class AzureDevOpsAuthApi(
    private val oauthClientId: String,
    private val oauthClientSecret: Secret,
    private val redirectUrl: Url,
    private val scmWeb: ScmWeb,
    orgId: OrgId?,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : OAuthApi {
    private val client: HttpClient = makeScmAuthHttpClient(
        orgId = orgId,
        clientEngine = clientEngine,
    )

    override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer")
                append("client_assertion", oauthClientSecret.value)
                append("grant_type", "urn:ietf:params:oauth:grant-type:jwt-bearer")
                append("assertion", context.code)
                append("redirect_uri", redirectUrl.asString)
            },
        )
            .logCatchingDeserialize<OAuthTokenResponse>()
            .asOAuthTokenExchange(redirectUrl = null)
    }

    override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_assertion_type", "urn:ietf:params:oauth:client-assertion-type:jwt-bearer")
                append("client_assertion", oauthClientSecret.value)
                append("grant_type", "refresh_token")
                append("assertion", refreshToken.value)
                append("redirect_uri", redirectUrl.asString)
            },
        )
            .logCatchingDeserialize<OAuthTokenResponse>()
            .asOAuthTokens
    }
}
