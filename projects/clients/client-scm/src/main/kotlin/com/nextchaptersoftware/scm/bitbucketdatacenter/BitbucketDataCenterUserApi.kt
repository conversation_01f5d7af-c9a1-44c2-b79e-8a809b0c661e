package com.nextchaptersoftware.scm.bitbucketdatacenter

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterCommonApi.avatarUrl
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterPagination.bitbucketDataCenterPaginatedStream
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPermission
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterProject
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterRepo
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterRepoState
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterUser
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmRole
import com.nextchaptersoftware.types.EmailAddress
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map

internal class BitbucketDataCenterUserApi(
    private val tokenProvider: ScmUserTokenProvider,
    orgId: OrgId?,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmUserApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun user(): ScmAuthUser {
        val user = getUser()
        return ScmAuthUser(
            externalId = user.id.toString(),
            avatarUrl = avatarUrl(user),
            htmlUrl = user.links.href,
            username = user.slug,
            displayName = user.displayName,
            primaryEmail = requireNotNull(user.emailAddress).let(EmailAddress::of),
            emails = listOfNotNull(user.emailAddress).map(EmailAddress::of),
            isBot = null,
            oauthTokens = tokenProvider.getTokens(),
        )
    }

    private suspend fun getUser(): BitbucketDataCenterUser {
        val userName = getCurrentUserName()
        return getUser(userName)
    }

    private suspend fun getUser(userName: String): BitbucketDataCenterUser {
        return client
            .get("users/$userName")
            .body()
    }

    private suspend fun getCurrentUserName(): String {
        return client
            .get("/plugins/servlet/applinks/whoami")
            .body<String>()
    }

    /**
     * @param namePrefix match projects with this prefix
     * @return list of projects where this user is an admin
     */
    private fun getUserProjects(
        namePrefix: String? = null,
        permission: BitbucketDataCenterPermission = BitbucketDataCenterPermission.PROJECT_ADMIN,
    ): Flow<BitbucketDataCenterProject> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterProject>("projects") {
                parameter("permission", permission)
                namePrefix?.also {
                    parameter("name", namePrefix)
                }
            }
            .asFlatItemsFlow()
    }

    override suspend fun installation(externalInstallationId: String): ScmInstallationAccount? {
        error("Not applicable")
    }

    override suspend fun accessibleInstallations(externalUserId: String): List<ScmInstallationAccount> {
        error("Not applicable")
    }

    /**
     * Determine the role of the user in the organization.
     * Or return null if the user is not a member of the organization.
     */
    override suspend fun orgRole(orgName: String): ScmRole? {
        suspend fun getRole(permission: BitbucketDataCenterPermission): ScmRole? {
            return getUserProjects(namePrefix = orgName, permission = permission)
                .firstOrNull { it.name == orgName }
                ?.let { permission.asScmRole }
        }

        return getRole(BitbucketDataCenterPermission.PROJECT_ADMIN)
            ?: getRole(BitbucketDataCenterPermission.PROJECT_READ)
    }

    override fun accounts(): Flow<ScmAccount> = flow {
        emitAll(
            getUserProjects().map { project ->
                project.asScmAccount
            },
        )
        emit(getUser().asScmAccount)
    }

    /**
     * @see [ScmUserApi.repos]
     */
    override fun repos(externalInstallationId: String): Flow<List<ScmRepository>> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterRepo>(
                path = "/repos",
            ) {
                parameter("state", BitbucketDataCenterRepoState.AVAILABLE)
            }
            .map { page ->
                page.values
                    .map { it.asScmRepository }
            }
    }

    override suspend fun createIssueComment(owner: String, repoName: String, issueNumber: Int, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun updateIssueComment(owner: String, repoName: String, commentId: String, body: GitHubBody): GitHubIssueComment {
        TODO("Not yet implemented")
    }

    override suspend fun deleteIssueComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updateReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        reviewId: String,
        body: GitHubBody,
    ): ScmPullRequestReview {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewComment(owner: String, repoName: String, commentId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun createPullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        body: GithubReactionBody,
    ): GitHubReaction {
        TODO("Not yet implemented")
    }

    override suspend fun deletePullRequestReviewCommentReaction(owner: String, repoName: String, commentId: String, reactionId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun updatePullRequest(owner: String, repoName: String, prNumber: Int, body: GitHubPullRequestUpdateBody): GitHubPullRequest {
        TODO("Not yet implemented")
    }

    override fun close() {
        client.close()
    }
}
