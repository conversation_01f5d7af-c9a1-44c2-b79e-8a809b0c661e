package com.nextchaptersoftware.scm

import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.scm.github.GitHubActionsApiLegacy
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider

class CiApiLegacyFactory(
    private val insiderService: InsiderServiceInterface,
    private val gitHubActionsClientProvider: GitHubActionsClientProvider,
) {
    fun getApi(
        scmTeam: ScmTeam,
    ): CiApiLegacy? = takeIf { insiderService.isInsiderOrg(orgId = scmTeam.orgId) }?.let {
        getApiStrict(scmTeam)
    }

    private fun getApiStrict(
        scmTeam: ScmTeam,
    ): CiApiLegacy = when (scmTeam.provider) {
        Provider.GitHub -> {
            GitHubActionsApiLegacy(
                scmTeam = scmTeam,
                gitHubActionsClientProvider = gitHubActionsClientProvider,
                enterpriseAppConfigId = scmTeam.providerEnterpriseId,
                providerInstallationId = scmTeam.providerExternalInstallationId ?: throw ScmTeamNotInstalledException(scmTeam.id.toString()),
            )
        }

        Provider.GitHubEnterprise -> {
            TODO("Support for GitHub Enterprise is not implemented yet")
        }

        Provider.GitHubActions -> {
            TODO("Support for GitHub Actions is not implemented yet")
        }

        Provider.GitLabPipelines -> {
            TODO("Support for GitLab Pipelines is not implemented yet")
        }

        Provider.Asana,
        Provider.Aws,
        Provider.AwsIdentityCenter,
        Provider.AzureDevOps,
        Provider.Bitbucket,
        Provider.BitbucketDataCenter,
        Provider.BitbucketPipelines,
        Provider.Buildkite,
        Provider.CircleCI,
        Provider.Coda,
        Provider.Confluence,
        Provider.ConfluenceDataCenter,
        Provider.CustomIntegration,
        Provider.GenericSaml,
        Provider.GitLab,
        Provider.GitLabSelfHosted,
        Provider.GoogleDrive,
        Provider.GoogleDriveWorkspace,
        Provider.GoogleWorkspace,
        Provider.Jira,
        Provider.JiraDataCenter,
        Provider.Linear,
        Provider.MicrosoftEntra,
        Provider.Notion,
        Provider.Okta,
        Provider.PingOne,
        Provider.Slack,
        Provider.StackOverflowTeams,
        Provider.Unblocked,
        Provider.Web,
            -> error("Not implemented")
    }
}
