package com.nextchaptersoftware.scm.gitlab.models

import io.ktor.http.Url

enum class GitLabWebhookEvents(
    val apiLabel: String,
    val hookLabel: String,
    val subscribe: Boolean = false,
) {
    // Events that we subscribe to
    MergeRequest(apiLabel = "merge_requests_events", hookLabel = "Merge Request Hook", subscribe = true),
    Pipeline(apiLabel = "pipeline_events", hookLabel = "Pipeline Hook", subscribe = true),
    Subgroup(apiLabel = "subgroup_events", hookLabel = "Subgroup Hook", subscribe = true),

    // Events that we don't subscribe to
    ConfidentialIssue(apiLabel = "confidential_issues_events", hookLabel = "Confidential Issue Hook"),
    ConfidentialNote(apiLabel = "confidential_note_events", hookLabel = "Confidential Note Hook"),
    Deployment(apiLabel = "deployment_events", hookLabel = "Deployment Hook"),
    Issue(apiLabel = "issues_events", hookLabel = "Issue Hook"),
    Job(apiLabel = "job_events", hookLabel = "Job Hook"),
    Note(apiLabel = "note_events", hookLabel = "Note Hook"),
    Push(apiLabel = "push_events", hookLabel = "Push Hook"),
    Release(apiLabel = "releases_events", hookLabel = "Release Hook"),
    RepositoryUpdate(apiLabel = "repository_update_events", hookLabel = "Repository Update Hook"),
    TagPush(apiLabel = "tag_push_events", hookLabel = "Tag Push Hook"),
    WikiPage(apiLabel = "wiki_page_events", hookLabel = "Wiki Page Hook"),
    ;

    companion object {
        fun fromHookLabel(value: String) = entries.first { it.hookLabel == value }

        fun createGitLabWebhookRequest(
            url: Url,
        ) = GitLabWebhookRequest(
            url = url,
            enableSslVerification = true,
            confidentialIssuesEvents = ConfidentialIssue.subscribe,
            confidentialNoteEvents = ConfidentialNote.subscribe,
            deploymentEvents = Deployment.subscribe,
            issuesEvents = Issue.subscribe,
            jobEvents = Job.subscribe,
            mergeRequestsEvents = MergeRequest.subscribe,
            noteEvents = Note.subscribe,
            pipelineEvents = Pipeline.subscribe,
            pushEvents = Push.subscribe,
            releasesEvents = Release.subscribe,
            repositoryUpdateEvents = RepositoryUpdate.subscribe,
            subgroupEvents = Subgroup.subscribe,
            tagPushEvents = TagPush.subscribe,
            wikiPageEvents = WikiPage.subscribe,
        )
    }
}
