package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.graphql.client.GraphQLKtorClient
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.github.GitHubPagination.githubBatchStream
import com.nextchaptersoftware.scm.github.GitHubPagination.githubStream
import com.nextchaptersoftware.scm.github.models.GitHubBody
import com.nextchaptersoftware.scm.github.models.GitHubInstallations
import com.nextchaptersoftware.scm.github.models.GitHubIssueComment
import com.nextchaptersoftware.scm.github.models.GitHubOrg
import com.nextchaptersoftware.scm.github.models.GitHubPullRequest
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReview
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestReviewComment
import com.nextchaptersoftware.scm.github.models.GitHubPullRequestUpdateBody
import com.nextchaptersoftware.scm.github.models.GitHubReaction
import com.nextchaptersoftware.scm.github.models.GitHubRepos
import com.nextchaptersoftware.scm.github.models.GitHubUser
import com.nextchaptersoftware.scm.github.models.GitHubUserEmail
import com.nextchaptersoftware.scm.github.models.GithubReactionBody
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.transformers.asScmPullRequestReview
import com.nextchaptersoftware.security.token
import com.nextchaptersoftware.types.EmailAddress
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

internal class GitHubUserApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val orgId: OrgId?,
    restApiUrl: Url,
    graphApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmUserApi {

    private val client: HttpClient by lazy {
        ScmHttpClientFactory.makeScmHttpClient(
            orgId = orgId,
            tokenProvider = tokenProvider,
            baseApiUrl = restApiUrl,
            clientEngine = clientEngine,
        ).config {
            install(Auth) {
                token {
                    loadToken {
                        tokenProvider.getTokens().also {
                            check(!it.accessTokenExpired(null)) { "Expired access token ${it.accessTokenExpiresAt}" }
                            check(!it.refreshTokenExpired()) { "Expired refresh token ${it.refreshTokenExpiresAt}" }
                        }.accessToken.value
                    }
                }
            }
        }
    }

    private val gqlClient: GraphQLKtorClient by lazy {
        GraphQLKtorClient(
            httpClient = client.config {
                defaultRequest {
                    url(graphApiUrl.asString)
                }
            },
        )
    }

    override suspend fun user(): ScmAuthUser {
        val user = getUser()
        val userEmails = client.get("user/emails")
            .body<List<GitHubUserEmail>>()
            .filter { it.isVerified }

        val primaryEmail = userEmails
            .firstOrNull { it.isPrimary }
            ?.email
            ?.let { EmailAddress.of(it) }
            ?: throw GitHubUnverifiedEmailException()

        return ScmAuthUser(
            externalId = user.id.toString(),
            avatarUrl = user.avatarUrl,
            htmlUrl = user.htmlUrl,
            username = user.login,
            displayName = user.name,
            primaryEmail = primaryEmail,
            emails = userEmails.map { EmailAddress.of(it.email) },
            isBot = user.isBot,
            oauthTokens = tokenProvider.getTokens(),
        )
    }

    override fun accounts(): Flow<ScmAccount> = flow {
        emitAll(orgs())
        emit(getUser().asScmUser.let(ScmAccount::User))
    }

    private fun orgs(): Flow<ScmAccount.Org> {
        return client.githubStream<GitHubOrg>("user/orgs").map { ScmAccount.Org(it.asScmOrg) }
    }

    private suspend fun getUser(): GitHubUser {
        return client.get("user").body()
    }

    private suspend fun getOrgById(externalId: String): GitHubOrg {
        return client.get("organizations/$externalId").body()
    }

    override suspend fun installation(externalInstallationId: String): ScmInstallationAccount? {
        return client.githubStream(
            initialUri = "user/installations",
            dataProvider = { it.body<GitHubInstallations>().installations },
        )
            .toList()
            .also { installations ->
                LOGGER.infoAsync(
                    "expectedInstallationId" to externalInstallationId,
                    "installationCount" to installations.size,
                    "installations" to installations.encode(),
                ) { "GitHub installations for user" }
            }
            .filter { it.installationId.toString() == externalInstallationId }
            .map { it.asScmInstallationAccount }
            .firstOrNull()
    }

    override suspend fun accessibleInstallations(externalUserId: String): List<ScmInstallationAccount> {
        return client.githubStream(
            initialUri = "user/installations",
            dataProvider = { it.body<GitHubInstallations>().installations },
        )
            .map { it.asScmInstallationAccount }
            .filter {
                when (it.account) {
                    is ScmAccount.Org -> true
                    is ScmAccount.User -> it.account.externalId == externalUserId
                }
            }
            .map {
                // Fetch the display name for the account if it's not already set
                if (it.account.displayName == null) {
                    val account = when (it.account) {
                        is ScmAccount.Org -> ScmAccount.Org(getOrgById(it.account.externalId).asScmOrg)
                        is ScmAccount.User -> ScmAccount.User(getUser().asScmUser)
                    }
                    it.copy(account = account)
                } else {
                    it
                }
            }
            .toList()
    }

    /**
     * @see [ScmUserApi.repos]
     */
    override fun repos(externalInstallationId: String): Flow<List<ScmRepository>> {
        return client.githubBatchStream(
            initialUri = "user/installations/$externalInstallationId/repositories",
            dataProvider = { it.body<GitHubRepos>().repos },
        ).map { batch ->
            batch.items.map { it.asScmRepository }
        }
    }

    override fun repoExternalIds(externalInstallationId: String, owner: String, isPersonalAccount: Boolean): Flow<String> {
        error("Use GitHubTeamApi.repoIds instead")
    }

    override suspend fun createIssueComment(
        owner: String,
        repoName: String,
        issueNumber: Int,
        body: GitHubBody,
    ): GitHubIssueComment {
        return client.post("repos/$owner/$repoName/issues/$issueNumber/comments") {
            setBody(body)
        }.body()
    }

    override suspend fun updateIssueComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubIssueComment {
        return client.patch("repos/$owner/$repoName/issues/comments/$commentId") {
            setBody(body)
        }.body()
    }

    override suspend fun deleteIssueComment(
        owner: String,
        repoName: String,
        commentId: String,
    ) {
        client.delete("repos/$owner/$repoName/issues/comments/$commentId")
    }

    override suspend fun updateReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        reviewId: String,
        body: GitHubBody,
    ): ScmPullRequestReview {
        return client.put("repos/$owner/$repoName/pulls/$pullRequestNumber/reviews/$reviewId") {
            setBody(body)
        }.body<GitHubPullRequestReview>().asScmPullRequestReview
    }

    override suspend fun createPullRequestReviewComment(
        owner: String,
        repoName: String,
        pullRequestNumber: Int,
        commentId: String, // Comment ID of first comment in thread
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        return client.post("repos/$owner/$repoName/pulls/$pullRequestNumber/comments/$commentId/replies") {
            setBody(body)
        }.body()
    }

    override suspend fun updatePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
        body: GitHubBody,
    ): GitHubPullRequestReviewComment {
        return client.patch("repos/$owner/$repoName/pulls/comments/$commentId") {
            setBody(body)
        }.body()
    }

    override suspend fun deletePullRequestReviewComment(
        owner: String,
        repoName: String,
        commentId: String,
    ) {
        client.delete("repos/$owner/$repoName/pulls/comments/$commentId")
    }

    override suspend fun createPullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        body: GithubReactionBody,
    ): GitHubReaction {
        return client.post("repos/$owner/$repoName/pulls/comments/$commentId/reactions") {
            setBody(body)
        }.body()
    }

    override suspend fun deletePullRequestReviewCommentReaction(
        owner: String,
        repoName: String,
        commentId: String,
        reactionId: String,
    ) {
        client.delete("repos/$owner/$repoName/pulls/comments/$commentId/reactions/$reactionId")
    }

    override suspend fun updatePullRequest(
        owner: String,
        repoName: String,
        prNumber: Int,
        body: GitHubPullRequestUpdateBody,
    ): GitHubPullRequest {
        return client.patch("repos/$owner/$repoName/pulls/$prNumber") {
            setBody(body)
        }.body()
    }

    override fun close() {
        client.close()
        gqlClient.close()
    }
}

class GitHubUnverifiedEmailException : Exception()
