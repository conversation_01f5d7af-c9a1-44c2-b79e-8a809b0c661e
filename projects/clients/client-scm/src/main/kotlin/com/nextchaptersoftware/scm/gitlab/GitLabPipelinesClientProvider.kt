package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.config.GitLabCloudConfig
import com.nextchaptersoftware.scm.expectScmConfig
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver

class GitLabPipelinesClientProvider(
    private val gitLabCloudConfig: GitLabCloudConfig?,
    private val scmAuthApiFactory: ScmAuthApiFactory,
    private val userSecretServiceResolver: UserSecretServiceResolver,
) {
    suspend fun create(
        providerInstallationId: String,
        externalRepositoryId: String,
    ): GitLabPipelinesClient {
        val scmTeam = scmTeamStore.findByProviderExternalId(
            provider = Provider.GitLab,
            providerEnterpriseId = null,
            providerExternalId = providerInstallationId,
        )
            ?: error("ScmTeam not found: $providerInstallationId")

        val repo = repoStore.findByExternalId(
            teamId = scmTeam.id,
            provider = Provider.GitLab,
            providerExternalId = externalRepositoryId,
        )
            ?: error("Repo not found: $externalRepositoryId")

        return create(
            scmTeam = scmTeam,
            repo = repo,
        )
    }

    suspend fun create(
        scmTeam: ScmTeam,
        repo: Repo,
    ): GitLabPipelinesClient {
        check(scmTeam.provider == Provider.GitLab) { "scmTeam.provider is not GitLab: given ${scmTeam.provider}" }
        check(repo.provider == Provider.GitLab) { "repo.provider is not GitLab: given ${repo.provider}" }

        val tokenProvider = run {
            val userSecretService = userSecretServiceResolver.resolve(
                provider = Provider.GitLab,
            )

            val authApi = scmAuthApiFactory.getApi(orgId = scmTeam.orgId, oAuthApiType = Scm.GitLab)

            ScmUserTokenProvider.RepoOAuthRefresh(
                oauthRefreshService = UserSecretOAuthRefreshService(
                    tokenRefresher = authApi,
                    tokenPersistence = EncryptedTokenPersistence(userSecretService),
                ),
                teamId = scmTeam.id,
                repoId = repo.id,
            )
        }

        return GitLabPipelinesClient(
            projectId = repo.externalId,
            client = createHttpClient(
                scmTeam = scmTeam,
                tokenProvider = tokenProvider,
            ),
        )
    }

    internal fun createHttpClient(
        scmTeam: ScmTeam,
        tokenProvider: ScmUserTokenProvider,
    ) = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = gitLabCloudConfig.let(::expectScmConfig).apiBaseUrl.asUrl,
    )
}
