package com.nextchaptersoftware.scm

import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthApiFactory
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSAClientServerCryptoSystem
import com.nextchaptersoftware.crypto.types.Ciphertext
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.azuredevops.AzureDevOpsAuthApi
import com.nextchaptersoftware.scm.bitbucket.BitbucketAuthApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterAuthApi
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.scm.github.GitHubAuthApi
import com.nextchaptersoftware.scm.gitlab.GitLabAuthApi
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url

class ScmAuthApiFactory(
    private val clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
    private val authenticationConfig: AuthenticationConfig,
    private val scmConfig: ScmConfig,
    private val scmWebFactory: ScmWebFactory,
    private val scmSecretConfig: ScmSecretConfig = ScmSecretConfig.INSTANCE,
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
) : OAuthApiFactory<OrgId, Scm> {

    private val bitbucketDataCenterSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(scmSecretConfig.bitbucketDataCenter.appSecretsPrivateKey.value)
    }

    private val gitLabSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(scmSecretConfig.gitlabSelfHosted.appSecretsPrivateKey.value)
    }

    private val gheSecretDecryption by lazy {
        AESCryptoSystem.AESDecryption(
            AESCryptoSystem.importKey(scmSecretConfig.githubEnterprise.appSecretsAesKey.value),
        )
    }

    override suspend fun getApi(
        orgId: OrgId?,
        oAuthApiType: Scm,
    ): OAuthApi {
        return when (oAuthApiType) {
            Scm.AzureDevOps -> {
                val config = expectScmConfig(scmConfig.azureDevOps)
                AzureDevOpsAuthApi(
                    orgId = orgId,
                    oauthClientId = config.oauth.clientId,
                    oauthClientSecret = expectScmConfig(scmSecretConfig.azureDevOps).clientSecret,
                    redirectUrl = config.overrideAuthRedirectUrl?.asUrl
                        ?: authenticationConfig.authRedirectUrl.asUrl,
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    clientEngine = clientEngine,
                )
            }

            Scm.Bitbucket -> {
                BitbucketAuthApi(
                    orgId = orgId,
                    oauthClientId = expectScmConfig(scmConfig.bitbucketCloud).oauth.clientId,
                    oauthClientSecret = expectScmConfig(scmSecretConfig.bitbucketCloud).clientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    clientEngine = clientEngine,
                )
            }

            Scm.GitHub -> {
                val config = expectScmConfig(scmConfig.githubCloud)
                GitHubAuthApi(
                    orgId = orgId,
                    oauthClientId = config.oauth.clientId,
                    oauthClientSecret = expectScmConfig(scmSecretConfig.githubCloud).clientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    baseApiUrl = Url(config.apiBaseUrl),
                    clientEngine = clientEngine,
                )
            }

            Scm.GitLab -> {
                val config = expectScmConfig(scmConfig.gitlabCloud)
                GitLabAuthApi(
                    orgId = orgId,
                    oauthClientId = config.oauth.clientId,
                    oauthClientSecret = expectScmConfig(scmSecretConfig.gitlabCloud).clientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    clientEngine = clientEngine,
                )
            }

            is Scm.GitHubEnterprise -> {
                val appConfig = enterpriseAppConfigStore.getById(oAuthApiType.enterpriseId, oAuthApiType.provider)
                val oauthClientSecret = gheSecretDecryption.decrypt(Ciphertext(appConfig.oauthClientSecretEncrypted))
                return GitHubAuthApi(
                    orgId = orgId,
                    oauthClientId = appConfig.oauthClientId,
                    oauthClientSecret = oauthClientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    baseApiUrl = Url("https://${appConfig.authority}/api/v3/"),
                    clientEngine = clientEngine,
                )
            }

            is Scm.GitLabSelfHosted -> {
                val appConfig = enterpriseAppConfigStore.getById(oAuthApiType.enterpriseId, oAuthApiType.provider)
                val oauthClientSecret = gitLabSecretDecryption.decrypt(Ciphertext(appConfig.oauthClientSecretEncrypted))
                return GitLabAuthApi(
                    orgId = orgId,
                    oauthClientId = appConfig.oauthClientId,
                    oauthClientSecret = oauthClientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    clientEngine = clientEngine,
                )
            }

            is Scm.BitbucketDataCenter -> {
                val appConfig = enterpriseAppConfigStore.getById(oAuthApiType.enterpriseId, oAuthApiType.provider)
                val oauthClientSecret = bitbucketDataCenterSecretDecryption.decrypt(
                    Ciphertext(appConfig.oauthClientSecretEncrypted),
                )
                return BitbucketDataCenterAuthApi(
                    orgId = orgId,
                    oauthClientId = appConfig.oauthClientId,
                    oauthClientSecret = oauthClientSecret,
                    redirectUrl = Url(authenticationConfig.authRedirectUrl),
                    scmWeb = scmWebFactory.from(oAuthApiType),
                    clientEngine = clientEngine,
                )
            }
        }
    }
}
