@file:Suppress("ktlint:nextchaptersoftware:no-direct-httpclient-constructor-rule")

package com.nextchaptersoftware.scm

import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.client.org.orgHttpClient
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.trace.ktor.KtorClientTracing
import com.nextchaptersoftware.utils.ensureTrailingSlash
import io.ktor.client.HttpClient
import io.ktor.client.HttpClientConfig
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.UserAgent
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.ContentType
import io.ktor.http.Url
import io.ktor.http.contentType
import kotlin.time.Duration.Companion.seconds

internal object ScmNoAuthHttpClientFactory {
    fun makeScmNoAuthHttpClient(
        orgId: OrgId?,
        baseApiUrl: Url,
        userAgent: String,
        clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
        additionalConfigure: HttpClientConfig<*>.() -> Unit = {},
    ): HttpClient {
        val configure: HttpClientConfig<*>.() -> Unit = {
            install(ContentNegotiation) {
                installJsonSerializer()
            }
            install(UserAgent) {
                agent = userAgent
            }
            install(HttpRequestRetry)
            install(HttpTimeout) {
                requestTimeoutMillis = 10.seconds.inWholeMilliseconds
            }
            install(KtorClientTracing)
            expectSuccess = true
            defaultRequest {
                url(baseApiUrl.asString.ensureTrailingSlash)
                contentType(ContentType.Application.Json)
            }
            additionalConfigure()
        }

        return if (orgId == null) {
            HttpClient(engine = clientEngine, block = configure)
        } else {
            orgHttpClient(orgId = orgId, engine = clientEngine, configure = configure)
        }
    }
}
