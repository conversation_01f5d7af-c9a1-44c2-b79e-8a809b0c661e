package com.nextchaptersoftware.scm.gitlab.models

import kotlinx.datetime.Instant
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class GitLabPipeline(
    val id: Long,
    val iid: Long,
    val name: String? = null,
    val status: GitLabStatus,

    @SerialName("web_url")
    val webUrl: String?,

    val ref: String,
    val sha: String,

    @SerialName("before_sha")
    val beforeSha: String,

    @SerialName("created_at")
    val createdAt: Instant,

    @SerialName("started_at")
    val startedAt: Instant? = null,

    @SerialName("finished_at")
    val finishedAt: Instant? = null,

    val duration: Long? = null,
)
