package com.nextchaptersoftware.scm.gitlab

import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthGrant
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokenResponse
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.HttpExtensions.logCatchingDeserialize
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmAuthHttpClientFactory.makeScmAuthHttpClient
import com.nextchaptersoftware.scm.ScmWeb
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.forms.submitForm
import io.ktor.http.Parameters
import io.ktor.http.Url

internal class GitLabAuthApi(
    private val oauthClientId: String,
    private val oauthClientSecret: Secret,
    private val redirectUrl: Url,
    private val scmWeb: ScmWeb,
    orgId: OrgId?,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : OAuthApi {

    private val client: HttpClient = makeScmAuthHttpClient(
        orgId = orgId,
        clientEngine = clientEngine,
    )

    override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_secret", oauthClientSecret.value)
                append("refresh_token", refreshToken.value)
                append("grant_type", OAuthGrant.RefreshToken.value)
                append("redirect_uri", redirectUrl.asString)
            },
        ).logCatchingDeserialize<OAuthTokenResponse>().asOAuthTokens
    }

    override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
        return client.submitForm(
            url = scmWeb.oauthTokenExchangeUrl.asString,
            formParameters = Parameters.build {
                append("client_id", oauthClientId)
                append("client_secret", oauthClientSecret.value)
                append("code", context.code)
                append("grant_type", OAuthGrant.AuthorizationCode.value)
                append("redirect_uri", redirectUrl.asString)
                append("redirect_uri", (context.overrideOAuthRedirectUrl ?: redirectUrl).asString)
            },
        ).logCatchingDeserialize<OAuthTokenResponse>().asOAuthTokenExchange(redirectUrl = null)
    }
}
