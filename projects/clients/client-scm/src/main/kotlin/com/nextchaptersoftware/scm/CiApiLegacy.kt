package com.nextchaptersoftware.scm

import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.scm.models.CiBuildRunJobLegacy
import com.nextchaptersoftware.scm.models.CiBuildRunLegacy
import com.nextchaptersoftware.scm.models.CiPullRequestStatus
import io.ktor.utils.io.core.Closeable
import kotlinx.coroutines.flow.Flow

interface CiApiLegacy : Closeable {

    suspend fun buildRun(
        scmTeam: ScmTeam,
        repoExternalId: String,
        runId: String,
    ): CiBuildRunLegacy

    suspend fun buildRunJob(
        scmTeam: ScmTeam,
        repoExternalId: String,
        jobId: String,
    ): CiBuildRunJobLegacy

    fun pullRequestStatus(
        scmTeam: ScmTeam,
        repoExternalId: String,
        pullRequestNumber: Int,
    ): Flow<CiPullRequestStatus>
}
