@file:Suppress("UnusedPrivateMember")

package com.nextchaptersoftware.scm.azuredevops

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory.makeScmHttpClient
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.azuredevops.models.AzureDevOpsGraphUserEntitlement
import com.nextchaptersoftware.scm.azuredevops.models.ScmTeamExtensions.accountName
import com.nextchaptersoftware.scm.azuredevops.models.ScmTeamExtensions.projectId
import com.nextchaptersoftware.scm.azuredevops.models.ScmTeamExtensions.projectName
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmMember
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmRole
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.scm.models.ScmWebhook
import com.nextchaptersoftware.scm.models.ScmWebhookManagementLevel
import com.nextchaptersoftware.utils.FlowExtensions.suspendedFlow
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.kilobytes
import com.sksamuel.hoplite.Secret
import io.ktor.client.engine.HttpClientEngine
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull

internal class AzureDevOpsTeamApi(
    private val tokenProvider: ScmUserTokenProvider,
    scmTeam: ScmTeam,
    identityId: IdentityId?,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmTeamApi {

    private val client = AzureDevOpsClient(
        client = makeScmHttpClient(
            orgId = scmTeam.orgId,
            tokenProvider = tokenProvider,
            baseApiUrl = baseApiUrl,
            allowRedirects = false,
            clientEngine = clientEngine,
        ),
        identityId = identityId,
    )

    private val accountName = scmTeam.accountName()
    private val projectName = scmTeam.projectName()
    private val projectId = scmTeam.projectId()

    override suspend fun account(): ScmAccount {
        val account = client.azureDevOpsAccount(
            accountName = accountName,
        ).required()

        val project = client.azureDevOpsProject(
            organization = accountName,
            project = projectId,
        )

        return project.asScmAccount(
            account = account,
        )
    }

    override fun membersForOrg(): Flow<ScmMember> = suspendedFlow fn@{
        //
        suspend fun AzureDevOpsGraphUserEntitlement.toScmUserWithAvatar(): ScmUser {
            val avatar = client.azureDevOpsGraphAvatarWithinThreshold(
                organization = accountName,
                descriptor = user.descriptor,
                maxSize = 7.kilobytes,
            )
            return asScmUser(
                avatar = avatar,
            )
        }

        check(client.azureDevOpsUserCallerIsAdmin(organization = accountName)) {
            "Auth user is not an organization administrator"
        }

        /*
            ADO entitlement api can be easily overload, and times out. Additionally, admins does not
            need project entitlement to been able to access the projects.

            So instead of listing all the user entitlements in a single pass, we will do a two pass process:
                1. list all the users
                2. for each user, get their project entitlements
         */

        val identity = client.azureDevOpsAuthenticatedIdentity()

        return@fn client.azureDevOpsUserEntitlements(
            organization = accountName,
        )
            .asFlatItemsFlow()
            .mapNotNull { userEntitlement ->

                // when authenticated identity is a system-wide admin mark as owner
                userEntitlement
                    .takeIf { it.user.descriptor == identity.subjectDescriptor }
                    ?.let {
                        ScmMember(
                            user = userEntitlement.toScmUserWithAvatar(),
                            role = ScmRole.Owner,
                        )
                    }

                // otherwise fallback to entitlement
                ?: client.azureDevOpsUserEntitlement(
                        organization = accountName,
                        entitlementId = userEntitlement.id,
                    )
                    .projectEntitlements
                    .firstOrNull { projectEntitlement ->
                        projectEntitlement.projectRef.id == projectId
                    }
                    ?.let { projectEntitlement ->
                        ScmMember(
                            user = userEntitlement.toScmUserWithAvatar(),
                            role = projectEntitlement.asScmRole(),
                        )
                    }
                    // FIXME: in this branch the user is either:
                    //   1. a regular organization member that's not part of the project
                    //   2. an organization admin that has implicit access to the project
            }
    }

    override fun membersForRepo(repoExternalId: String): Flow<ScmMember> {
        TODO("Not yet implemented")
    }

    override fun repos(): Flow<List<ScmRepository>> {
        return client.azureDevOpsRepositories(
            organization = accountName,
            project = projectName,
        )
            .map { page ->
                page.items
                    .filterNot { it.isDisabled }
                    .map { it.asScmRepository }
            }
    }

    override suspend fun getWebhookCapability(scmTeam: ScmTeam): ScmWebhookManagementLevel {
        return ScmWebhookManagementLevel.Org
    }

    override fun listWebhooks(description: String, webhookUrl: Url): Flow<ScmWebhook> {
        // FIXME: mrtn
        return emptyFlow()
    }

    override suspend fun createWebhook(description: String, webhookUrl: Url) {
        // FIXME: mrtn, azure devops
    }

    override suspend fun deleteWebhook(webhookId: String) {
        TODO("Not yet implemented")
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun apiToken(): Secret {
        return tokenProvider.getTokens().accessToken
    }

    override fun close() {
        client.close()
    }
}
