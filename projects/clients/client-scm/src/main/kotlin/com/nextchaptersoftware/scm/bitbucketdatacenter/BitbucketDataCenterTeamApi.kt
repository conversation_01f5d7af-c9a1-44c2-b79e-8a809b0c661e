package com.nextchaptersoftware.scm.bitbucketdatacenter

import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.kotlinx.coroutines.flow.asFlatFlow
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterPagination.bitbucketDataCenterPaginatedStream
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPermission
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPermittedGroup
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPermittedProject
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterPermittedUser
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterProject
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterRepo
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterUser
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterWebhook
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterWebhookRequest
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmMember
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmRole
import com.nextchaptersoftware.scm.models.ScmUser
import com.nextchaptersoftware.scm.models.ScmWebhook
import com.nextchaptersoftware.scm.models.ScmWebhookManagementLevel
import com.nextchaptersoftware.utils.asSemver
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach

internal class BitbucketDataCenterTeamApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val scmTeam: ScmTeam,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmTeamApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    private val projectBasePath: String
        get() = when (scmTeam.providerIsPersonalAccount) {
            true -> "projects/~${scmTeam.providerLogin}"
            false -> "projects/${scmTeam.providerLogin}"
        }

    override suspend fun account(): ScmAccount {
        return getProject().asScmAccount
    }

    private suspend fun getProject(): BitbucketDataCenterProject {
        return client
            .get(projectBasePath)
            .body<BitbucketDataCenterProject>()
    }

    private val collectorsForMembersForOrg: List<() -> Flow<ScmMember>> = listOf(
        // members with the default project permission
        ::getMembersFromOrgDefaultRole,
        // members directly added to the org
        ::getMembersForOrg,
        // members indirectly added via user groups
        ::getMembersFromGroups,
    )

    override fun membersForOrg(): Flow<ScmMember> = flow {
        val userRoles = mutableMapOf<ScmUser, ScmRole>()

        collectorsForMembersForOrg.forEach { collector ->
            collector
                .invoke()
                .onEach { member ->
                    val userRole = userRoles[member.user] ?: ScmRole.None
                    userRoles[member.user] = userRole.max(member.role)
                }
                .collect()
        }

        userRoles.forEach { (user, role) ->
            emit(ScmMember(user = user, role = role))
        }
    }

    /**
     * @see membersForOrg
     */
    private fun getMembersFromOrgDefaultRole(): Flow<ScmMember> = flow {
        when (val defaultRole = getOrgDefaultRole()) {
            ScmRole.None -> return@flow

            else -> getAllUsers()
                .collect { user ->
                    emit(
                        ScmMember(
                            user = user,
                            role = defaultRole,
                        ),
                    )
                }
        }
    }

    /**
     * @see membersForOrg
     */
    private fun getMembersForOrg(): Flow<ScmMember> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterPermittedUser>("$projectBasePath/permissions/users")
            .asFlatItemsFlow()
            .map { permittedUser ->
                permittedUser.asScmMember
            }
    }

    /**
     * @see membersForOrg
     */
    private fun getMembersFromGroups(): Flow<ScmMember> {
        return getPermittedGroupsForOrg()
            .map(::getMembersForPermittedGroup)
            .asFlatFlow()
    }

    private fun getMembersForPermittedGroup(permittedGroup: BitbucketDataCenterPermittedGroup): Flow<ScmMember> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterUser>("/admin/groups/more-members") {
                parameter("context", permittedGroup.group.name)
            }
            .asFlatItemsFlow()
            .map { user ->
                ScmMember(
                    user = user.asScmUser,
                    role = permittedGroup.scmRole,
                )
            }
    }

    private fun getPermittedGroupsForOrg(): Flow<BitbucketDataCenterPermittedGroup> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterPermittedGroup>("$projectBasePath/permissions/groups")
            .asFlatItemsFlow()
    }

    private suspend fun getOrgDefaultRole(): ScmRole {
        var defaultPermission = ScmRole.None

        BitbucketDataCenterPermission.grantablePermissionsForProjects.forEach { permission ->
            if (defaultPermission < permission.asScmRole) {
                return@forEach
            }

            client
                .get("$projectBasePath/permissions/${permission.name}/all")
                .body<BitbucketDataCenterPermittedProject>()
                .also {
                    if (it.permitted) {
                        defaultPermission = defaultPermission.max(permission.asScmRole)
                    }
                }
        }

        return defaultPermission
    }

    private fun getAllUsers(): Flow<ScmUser> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterUser>("/admin/users")
            .asFlatItemsFlow()
            .filter { it.type == BitbucketDataCenterUser.UserType.NORMAL }
            .map { it.asScmUser }
    }

    override fun membersForRepo(repoExternalId: String): Flow<ScmMember> {
        TODO("Not yet implemented")
    }

    override fun repos(): Flow<List<ScmRepository>> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterRepo>("$projectBasePath/repos")
            .map { page ->
                page.values
                    .map { it.asScmRepository }
                    .filterNot { it.isArchived }
                    .filterNot { it.isDisabled }
            }
    }

    override suspend fun getWebhookCapability(scmTeam: ScmTeam): ScmWebhookManagementLevel {
        val appVersion = client.bitbucketDataCenterApplication().version.asSemver()

        return when (scmTeam.providerIsPersonalAccount) {
            true -> ScmWebhookManagementLevel.Repo

            false -> when {
                appVersion.isGreaterThanOrEqualTo("8.0.0") -> ScmWebhookManagementLevel.Org
                else -> ScmWebhookManagementLevel.Repo
            }
        }
    }

    override fun listWebhooks(description: String, webhookUrl: Url): Flow<ScmWebhook> {
        return client
            .bitbucketDataCenterPaginatedStream<BitbucketDataCenterWebhook>("$projectBasePath/webhooks")
            .asFlatItemsFlow()
            .map { it.asScmWebhook }
    }

    override suspend fun createWebhook(description: String, webhookUrl: Url) {
        client.post("$projectBasePath/webhooks") {
            setBody(
                BitbucketDataCenterWebhookRequest(
                    url = webhookUrl.asString,
                    name = description,
                    events = BitbucketDataCenterWebhook.subscribeEvents,
                    isActive = true,
                    sslVerificationRequired = true,
                ),
            )
        }
    }

    override suspend fun deleteWebhook(webhookId: String) {
        client.delete("$projectBasePath/webhooks/$webhookId")
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun apiToken(): Secret {
        return tokenProvider.getTokens().accessToken
    }

    override fun close() {
        client.close()
    }
}
