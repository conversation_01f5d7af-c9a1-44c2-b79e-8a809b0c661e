package com.nextchaptersoftware.scm.gitlab.models

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
enum class GitLabStatus {
    /**
     * Job was manually canceled or automatically aborted
     */
    @SerialName("canceled")
    Canceled,

    /**
     * Job is being canceled but after_script is running
     */
    @SerialName("canceling")
    Canceling,

    /**
     * Job has been created but not yet processed
     */
    @SerialName("created")
    Created,

    /**
     * Job execution failed
     */
    @SerialName("failed")
    Failed,

    /**
     * Job requires manual action to start
     */
    @SerialName("manual")
    Manual,

    /**
     * Job is in the queue waiting for a runner
     */
    @SerialName("pending")
    Pending,

    /**
     * Runner is preparing the execution environment
     */
    @SerialName("preparing")
    Preparing,

    /**
     * Job is executing on a runner
     */
    @SerialName("running")
    Running,

    /**
     * Job has been scheduled but execution hasn’t started
     */
    @SerialName("scheduled")
    Scheduled,

    /**
     * Job was skipped due to conditions or dependencies
     */
    @SerialName("skipped")
    Skipped,

    /**
     * Job completed successfully
     */
    @SerialName("success")
    Success,

    /**
     * Job is waiting for resources to become available
     */
    @SerialName("waiting_for_resource")
    WaitingForResource,
    ;

    val isComplete get() = when (this) {
        Canceling,
        Created,
        Manual,
        Pending,
        Preparing,
        Running,
        Scheduled,
        WaitingForResource,
            -> false

        Canceled,
        Failed,
        Skipped,
        Success,
            -> true
    }
}
