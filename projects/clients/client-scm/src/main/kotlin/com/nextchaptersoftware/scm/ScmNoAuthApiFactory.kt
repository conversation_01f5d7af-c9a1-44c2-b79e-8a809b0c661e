package com.nextchaptersoftware.scm

import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.scm.azuredevops.AzureDevOpsNoAuthApi
import com.nextchaptersoftware.scm.bitbucket.BitbucketNoAuthApi
import com.nextchaptersoftware.scm.bitbucketdatacenter.BitbucketDataCenterNoAuthApi
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.github.GitHubNoAuthApi
import com.nextchaptersoftware.scm.gitlab.GitLabNoAuthApi
import io.ktor.http.Url

class ScmNoAuthApiFactory(
    private val scmConfig: ScmConfig,
    private val enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
) {
    suspend fun from(orgId: OrgId?, scm: Scm): ScmNoAuthApi {
        return when (scm) {
            Scm.AzureDevOps -> {
                val config = expectScmConfig(scmConfig.azureDevOps)
                AzureDevOpsNoAuthApi(baseApiUrl = Url(config.apiBaseUrl))
            }

            Scm.Bitbucket -> {
                val config = expectScmConfig(scmConfig.bitbucketCloud)
                BitbucketNoAuthApi(orgId = orgId, baseApiUrl = Url(config.apiBaseUrl))
            }

            Scm.GitHub -> {
                val config = expectScmConfig(scmConfig.githubCloud)
                GitHubNoAuthApi(orgId = orgId, baseApiUrl = Url(config.apiBaseUrl))
            }

            Scm.GitLab -> {
                val config = expectScmConfig(scmConfig.gitlabCloud)
                GitLabNoAuthApi(orgId = orgId, baseApiUrl = Url(config.apiBaseUrl))
            }

            is Scm.GitHubEnterprise -> {
                val enterpriseConfig = enterpriseAppConfigStore.getById(scm.enterpriseId, scm.provider)
                GitHubNoAuthApi.fromAuthority(orgId = orgId, authority = enterpriseConfig.authority)
            }

            is Scm.GitLabSelfHosted -> {
                val enterpriseConfig = enterpriseAppConfigStore.getById(scm.enterpriseId, scm.provider)
                GitLabNoAuthApi.fromAuthority(orgId = orgId, authority = enterpriseConfig.authority)
            }

            is Scm.BitbucketDataCenter -> {
                val enterpriseConfig = enterpriseAppConfigStore.getById(scm.enterpriseId, scm.provider)
                BitbucketDataCenterNoAuthApi.fromAuthority(orgId = orgId, authority = enterpriseConfig.authority)
            }
        }
    }

    /**
     * Create a [ScmNoAuthApi] from an [authority] (host and optionally port) string.
     */
    fun fromAuthority(
        orgId: OrgId?,
        authority: String,
        provider: Provider,
    ): ScmNoAuthApi {
        return when (provider) {
            Provider.GitHubEnterprise -> GitHubNoAuthApi.fromAuthority(orgId = orgId, authority = authority)
            Provider.GitLabSelfHosted -> GitLabNoAuthApi.fromAuthority(orgId = orgId, authority = authority)
            Provider.BitbucketDataCenter -> BitbucketDataCenterNoAuthApi.fromAuthority(orgId = orgId, authority = authority)
            else -> error("Not supported")
        }
    }
}
