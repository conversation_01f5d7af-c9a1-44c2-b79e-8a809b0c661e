package com.nextchaptersoftware.scm.bitbucket

import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.ktor.client.engine.KtorClientEngineFactory
import com.nextchaptersoftware.scm.ScmHttpClientFactory
import com.nextchaptersoftware.scm.ScmTeamApi
import com.nextchaptersoftware.scm.auth.ScmUserTokenProvider
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketBatchStream
import com.nextchaptersoftware.scm.bitbucket.BitbucketPagination.bitbucketStream
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketMember
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketOrg
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketRepo
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketWebhook
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketWebhookRequest
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketWebhookSubjectType
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmMember
import com.nextchaptersoftware.scm.models.ScmRateLimit
import com.nextchaptersoftware.scm.models.ScmRepository
import com.nextchaptersoftware.scm.models.ScmWebhook
import com.nextchaptersoftware.scm.models.ScmWebhookManagementLevel
import com.sksamuel.hoplite.Secret
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.engine.HttpClientEngine
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.http.Url
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

internal class BitbucketTeamApi(
    private val tokenProvider: ScmUserTokenProvider,
    private val scmTeam: ScmTeam,
    baseApiUrl: Url,
    clientEngine: HttpClientEngine = KtorClientEngineFactory.createOnIODispatcher(),
) : ScmTeamApi {

    private val client: HttpClient = ScmHttpClientFactory.makeScmHttpClient(
        orgId = scmTeam.orgId,
        tokenProvider = tokenProvider,
        baseApiUrl = baseApiUrl,
        clientEngine = clientEngine,
    )

    override suspend fun account(): ScmAccount {
        return when (scmTeam.providerIsPersonalAccount) {
            true -> org().asScmUser.let(ScmAccount::User)
            false -> org().asScmOrg.let(ScmAccount::Org)
        }
    }

    suspend fun org(): BitbucketOrg {
        return client.get("workspaces/${scmTeam.providerExternalId}").body<BitbucketOrg>()
    }

    override fun membersForOrg(): Flow<ScmMember> {
        return client.bitbucketStream<BitbucketMember>("workspaces/${scmTeam.providerExternalId}/permissions")
            .map { it.asScmMember }
    }

    override fun membersForRepo(repoExternalId: String): Flow<ScmMember> {
        return client.bitbucketStream<BitbucketMember>("workspaces/${scmTeam.providerExternalId}/permissions/repositories/$repoExternalId")
            .map { it.asScmMember }
    }

    override fun repos(): Flow<List<ScmRepository>> {
        return client.bitbucketBatchStream<BitbucketRepo>("repositories/${scmTeam.providerExternalId}")
            .map { batch ->
                batch.items
                    .map { it.asScmRepository }
                    .filterNot { it.isArchived }
                    .filterNot { it.isDisabled }
            }
    }

    override suspend fun getWebhookCapability(scmTeam: ScmTeam): ScmWebhookManagementLevel {
        return ScmWebhookManagementLevel.Org
    }

    override fun listWebhooks(description: String, webhookUrl: Url): Flow<ScmWebhook> {
        return client.bitbucketStream<BitbucketWebhook>("workspaces/${scmTeam.providerExternalId}/hooks")
            .map { it.asScmWebhook }
    }

    override suspend fun createWebhook(
        description: String,
        webhookUrl: Url,
    ) {
        client.post("workspaces/${scmTeam.providerExternalId}/hooks") {
            setBody(
                BitbucketWebhookRequest(
                    description = description,
                    url = webhookUrl,
                    subjectType = BitbucketWebhookSubjectType.Workspace,
                    active = true,
                    events = BitbucketWebhook.EXPECTED_EVENTS,
                ),
            )
        }
    }

    override suspend fun deleteWebhook(webhookId: String) {
        client.delete("workspaces/${scmTeam.providerExternalId}/hooks/$webhookId")
    }

    override suspend fun rateLimit(): ScmRateLimit? {
        return null
    }

    override suspend fun apiToken(): Secret {
        return tokenProvider.getTokens().accessToken
    }

    override fun close() {
        client.close()
    }
}
