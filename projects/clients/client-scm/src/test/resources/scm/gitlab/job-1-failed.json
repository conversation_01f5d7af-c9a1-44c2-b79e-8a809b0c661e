{"id": 10313327525, "status": "failed", "stage": "test", "name": "test1", "ref": "mrtn/foo-2", "tag": false, "coverage": null, "allow_failure": false, "created_at": "2025-06-11T05:45:14.354Z", "started_at": "2025-06-11T05:45:15.016Z", "finished_at": "2025-06-11T05:45:44.987Z", "erased_at": null, "duration": 29.971195, "queued_duration": 0.224394, "user": {"id": 21278870, "username": "martin-ncs", "public_email": null, "name": "<PERSON>", "state": "active", "locked": false, "avatar_url": "https://secure.gravatar.com/avatar/1e2cec2d157877863e6e290ad79feb08feb2ce96a0d7fb885e73fce184b8dbbe?s=80&d=identicon", "web_url": "https://gitlab.com/martin-ncs", "created_at": "2024-05-15T01:41:45.269Z", "bio": "", "location": "", "skype": "", "linkedin": "", "twitter": "", "discord": "", "website_url": "", "github": "", "organization": "", "job_title": "", "pronouns": null, "bot": false, "work_information": null, "followers": 0, "following": 0, "local_time": null}, "commit": {"id": "9884b8e6aa698b8a8543ef2849545e2b2eb2ed19", "short_id": "9884b8e6", "created_at": "2025-06-09T20:32:40.000+00:00", "parent_ids": ["5611da01d69b4b30909335911f684c6aa426ae11"], "title": "qsdsada", "message": "qsdsada", "author_name": "<PERSON>", "author_email": "<EMAIL>", "authored_date": "2025-06-09T20:32:40.000+00:00", "committer_name": "<PERSON>", "committer_email": "<EMAIL>", "committed_date": "2025-06-09T20:32:40.000+00:00", "trailers": {}, "extended_trailers": {}, "web_url": "https://gitlab.com/martin-ncs/gitlab-test/-/commit/9884b8e6aa698b8a8543ef2849545e2b2eb2ed19"}, "pipeline": {"id": 1861159502, "iid": 13, "project_id": 70675702, "sha": "9884b8e6aa698b8a8543ef2849545e2b2eb2ed19", "ref": "mrtn/foo-2", "status": "failed", "source": "push", "created_at": "2025-06-09T20:32:42.227Z", "updated_at": "2025-06-11T05:45:45.268Z", "web_url": "https://gitlab.com/martin-ncs/gitlab-test/-/pipelines/1861159502"}, "failure_reason": "script_failure", "web_url": "https://gitlab.com/martin-ncs/gitlab-test/-/jobs/10313327525", "project": {"ci_job_token_scope_enabled": false}, "artifacts": [{"file_type": "trace", "size": 3244, "filename": "job.log", "file_format": null}], "runner": {"id": 32976645, "description": "6-green.saas-linux-small-amd64.runners-manager.gitlab.com/default", "ip_address": null, "active": true, "paused": false, "is_shared": true, "runner_type": "instance_type", "name": null, "online": true, "status": "online"}, "runner_manager": {"id": 71074453, "system_id": "s_a201ab37b78a", "version": "18.1.0~pre.317.g2147fb44", "revision": "2147fb44", "platform": "linux", "architecture": "amd64", "created_at": "2025-05-27T13:17:35.357Z", "contacted_at": "2025-06-11T06:01:07.718Z", "ip_address": "**************", "status": "online"}, "artifacts_expire_at": null, "archived": false, "tag_list": []}