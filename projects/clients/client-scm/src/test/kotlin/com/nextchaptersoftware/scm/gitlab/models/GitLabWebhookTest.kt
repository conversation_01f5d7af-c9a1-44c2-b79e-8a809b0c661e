package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabWebhookTest {

    private fun test(
        file: String,
        test: (GitLabWebhook) -> Unit = {},
    ) {
        testData(
            file = file,
            decoder = { it.decode<GitLabWebhook>() },
        ) {
            assertThat(it)
                .isNotNull
                .satisfies(test)
        }
    }

    @Test
    fun `webhook-1 -- ci-disabled`() = test("/scm/gitlab/webhook-1-ci-disabled.json") {
        assertThat(it.isSubscribedTo(GitLabWebhookEvents.Pipeline)).isFalse
        assertThat(it.isSubscribedTo(GitLabWebhookEvents.Job)).isFalse
        assertThat(it.getAsScmWebhook(description = "").isConfigurationValid).isFalse()
    }

    @Test
    fun `webhook-2 -- ci-enabled`() = test("/scm/gitlab/webhook-2-ci-enabled.json") {
        assertThat(it.isSubscribedTo(GitLabWebhookEvents.Pipeline)).isTrue
        assertThat(it.isSubscribedTo(GitLabWebhookEvents.Job)).isFalse
        assertThat(it.getAsScmWebhook(description = "").isConfigurationValid).isTrue()
    }
}
