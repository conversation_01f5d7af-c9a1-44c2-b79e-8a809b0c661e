package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabJobTest {

    private fun test(
        file: String,
        test: (GitLabJob) -> Unit = {},
    ) {
        testData(
            file = file,
            decoder = { it.decode<GitLabJob>() },
        ) {
            assertThat(it)
                .isNotNull
                .satisfies(test)
        }
    }

    @Test
    fun `job-1 -- failed`() = test("/scm/gitlab/job-1-failed.json")
}
