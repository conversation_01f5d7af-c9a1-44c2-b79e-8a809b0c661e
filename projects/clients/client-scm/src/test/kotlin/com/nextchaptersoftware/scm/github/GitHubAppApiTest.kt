@file:Suppress("ktlint:nextchaptersoftware:no-test-delay-expression-rule")

package com.nextchaptersoftware.scm.github

import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.scm.github.models.GitHubAccountType.Organization
import com.nextchaptersoftware.scm.models.ScmPullRequestReview
import com.nextchaptersoftware.scm.models.ScmTokenConfig
import com.nextchaptersoftware.scm.utils.TestUtils.testMockEngine
import com.nextchaptersoftware.test.utils.TestUtils.getResource
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.date
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLProtocol
import io.ktor.http.headersOf
import io.ktor.server.util.url
import io.ktor.utils.io.ByteReadChannel
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.test.runTest
import kotlinx.coroutines.time.delay
import kotlinx.datetime.Clock.System.now
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

@Suppress("LargeClass")
class GitHubAppApiTest {
    private val apiFactory: ScmAppApiFactory = ScmAppApiFactory(
        scmConfig = ScmConfig.INSTANCE,
        scmSecretConfig = ScmSecretConfig.INSTANCE,
    )

    @Test
    fun `retrieve installations succeeds`() = runTest {
        val mockEngine = MockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("app", "installations")
                    parameters["per_page"] = "100"
                    parameters["since"] = "2022-02-03T06:45:48Z"
                },
            ).isEqualTo(
                request.url.asString,
            )
            val authHeader = request.headers[HttpHeaders.Authorization]
            assertThat(authHeader).isNotNull
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/Installations.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }
        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3App()

        val installations = client.installations(since = Instant.parse("2022-02-03T06:45:48Z")).toList()

        assertThat(23185793).isEqualTo(installations[0].installationId)
        assertThat(166219).isEqualTo(installations[0].appId)
    }

    @Test
    fun `retrieve installation access token succeeds`() = runTest {
        val mockEngine = MockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("app", "installations", "123", "access_tokens")
                },
            ).isEqualTo(
                request.url.asString,
            )
            val authHeader = request.headers[HttpHeaders.Authorization]
            assertThat(authHeader).isNotNull
            assertThat(HttpMethod.Post).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(
                    getResource(
                        this,
                        "/scm/github/InstallationToken.json",
                    ),
                ),
                status = HttpStatusCode.Created,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3App()

        val accessTokenResponse = client.installationAccessToken("123")
        assertThat("****************************************").isEqualTo(accessTokenResponse.token)
        assertThat(Instant.parse("2022-03-23T04:16:12Z").date).isEqualTo(accessTokenResponse.expiry)
    }

    @Test
    fun `access token installation cache returns same token within expiry range`() = runTest {
        val mockEngine = MockEngine {
            val expiresLater = now() + 1.days
            val token = UUID.randomUUID()
            respond(
                content = ByteReadChannel("""{"token":"$token","expires_at":"$expiresLater"}"""),
                status = HttpStatusCode.Created,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val api = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine)

        val firstToken = api.appInstallTokenProvider.getInstallToken("123")
        delay(2.seconds.toJavaDuration())
        val secondToken = api.appInstallTokenProvider.getInstallToken("123")
        delay(2.seconds.toJavaDuration())
        val thirdToken = api.appInstallTokenProvider.getInstallToken("321")

        assertThat(firstToken).isEqualTo(secondToken)
        assertThat(thirdToken).isNotEqualTo(secondToken)
    }

    @Disabled("Flaky")
    @Test
    fun `access token installation cache is not used when leeway is set`() = runTest {
        val mockEngine = MockEngine {
            val expiresLater = now() + 5.minutes
            val token = UUID.randomUUID()
            respond(
                content = ByteReadChannel("""{"token":"$token","expires_at":"$expiresLater"}"""),
                status = HttpStatusCode.Created,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val api = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine)

        val firstToken = api.appInstallTokenProvider.getInstallToken("123", scmTokenConfig = ScmTokenConfig(leeway = 10.minutes))
        val secondToken = api.appInstallTokenProvider.getInstallToken("123", scmTokenConfig = ScmTokenConfig(leeway = 10.minutes))

        assertThat(firstToken.token).isNotEqualTo(secondToken.token)
    }

    @Test
    fun `retrieve org by name succeeds`() = runTest {
        val mockEngine = MockEngine { request ->
            assertThat(
                request.url.asString,
            ).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("orgs", "NextChapterSoftware")
                },
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(
                    getResource(
                        this,
                        "/scm/github/Organization.json",
                    ),
                ),
                status = HttpStatusCode.Created,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3App()

        val org = client.orgByName(orgName = "NextChapterSoftware")
        assertThat(91906527).isEqualTo(org.id)
    }

    @Test
    fun `retrieve org by id succeeds`() = runTest {
        val mockEngine = MockEngine { request ->
            assertThat(
                request.url.asString,
            ).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("organizations", "91906527")
                },
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(
                    getResource(
                        this,
                        "/scm/github/Organization.json",
                    ),
                ),
                status = HttpStatusCode.Created,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3App()

        val org = client.orgById(externalId = "91906527")
        assertThat(91906527).isEqualTo(org.id)
    }

    @Test
    fun `retrieve pull request`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls", "15")
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequest.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val pullRequest = client.pullRequest(repoExternalId = "repoExternalId", pullRequestNumber = 15)
        assertThat(pullRequest.mergeCommitSha).isEqualTo("86f142318894fbb324c175586784a25482ff219c")
    }

    @Test
    fun `retrieve issues`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "issues")
                    parameters["per_page"] = "100"
                    parameters["state"] = "all"
                    parameters["direction"] = "desc"
                    parameters["sort"] = "created"
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            val body = getResource(this, "/scm/github/issues.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(Pair(HttpHeaders.ContentType, listOf("application/json"))),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        client.allIssues(repoExternalId = "repoExternalId", initialBatchUrl = null, since = null).toList().also { batches ->
            val issues = batches.flatMap { it.items }
            assertThat(issues).hasSize(1)
            assertThat(issues.single().id).isEqualTo(1701077365)
            assertThat(batches.last().nextCursor).isNull()
        }
    }

    @Test
    fun `retrieve issue comments for issues`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "issues", "comments")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/IssueComments.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        client.allIssueComments(repoExternalId = "repoExternalId", initialBatchUrl = null).toList().also { batches ->
            val comments = batches.flatMap { it.items }

            assertThat(comments).hasSize(1)
            assertThat(comments.single().id).isEqualTo(1540739354)
            assertThat(comments.single().issueNumber).isEqualTo(6)
            assertThat(batches.last().nextCursor).isNull()
        }
    }

    @Test
    fun `retrieve pull requests`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(request.url.asString).isEqualTo(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls")
                    parameters["per_page"] = "100"
                    parameters["state"] = "all"
                    parameters["direction"] = "desc"
                    parameters["sort"] = "created"
                },
            )
            assertThat(request.method).isEqualTo(HttpMethod.Get)
            val body = getResource(this, "/scm/github/PullRequests.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(Pair(HttpHeaders.ContentType, listOf("application/json"))),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        client.allPullRequests(repoExternalId = "repoExternalId", initialBatchUrl = null, since = null).toList().also { batches ->
            assertThat(batches.flatMap { it.items }).hasSize(3)
            assertThat(batches.last().nextCursor).isNull()
        }
    }

    @Test
    fun `retrieve pull request review comments for repo`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls", "comments")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequestReviewComments.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        client.allPullRequestReviewComments(repoExternalId = "repoExternalId", initialBatchUrl = null).toList()
            .also { batches ->
                assertThat(batches.flatMap { it.items }).hasSize(14)
                assertThat(batches.last().nextCursor).isNull()
            }
    }

    @Test
    fun `retrieve issue comments for pull requests`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "issues", "comments")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequestComments.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        client.allPullRequestComments(repoExternalId = "repoExternalId", initialBatchUrl = null).toList().also { batches ->
            val comments = batches.flatMap { it.items }

            assertThat(comments).hasSize(100)
            assertThat(comments.count { it.user != null }).isEqualTo(78)
            assertThat(batches.last().nextCursor).isNull()
        }
    }

    @Test
    fun `retrieve pull request reviews`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls", "2608", "reviews")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequestReviews.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val reviews = client.pullRequestReviewsForPR(repoExternalId = "repoExternalId", 2608).toList()
        assertThat(reviews).hasSize(11)
        assertThat(reviews.filter { it.state == ScmPullRequestReview.State.COMMENTED }).hasSize(8)
        assertThat(reviews.filter { it.state == ScmPullRequestReview.State.APPROVED }).hasSize(2)
        assertThat(reviews.filter { it.state == ScmPullRequestReview.State.CHANGES_REQUESTED }).hasSize(1)
    }

    @Test
    fun `retrieve pull request review comments`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls", "15", "comments")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequestReviewComments.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val comments = client.pullRequestCodeCommentsForPR(repoExternalId = "repoExternalId", pullRequestNumber = 15).toList()
        assertThat(comments).hasSize(14)
    }

    @Test
    fun `retrieve pull request files`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("repositories", "repoExternalId", "pulls", "260", "files")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/PullRequestReviewFiles.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val comments = client.pullRequestFilesForPR(repoExternalId = "repoExternalId", pullRequestNumber = 260)
        assertThat(comments.toList()).hasSize(18)
    }

    @Test
    fun `rate limit`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("rate_limit")
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            val body = getResource(this, "/scm/github/RateLimit.json")
            respond(
                content = ByteReadChannel(body),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val rateLimit = client.rateLimit()
        assertThat(rateLimit.limit).isEqualTo(5000)
        assertThat(rateLimit.used).isEqualTo(1446)
        assertThat(rateLimit.remaining).isEqualTo(3554)
        assertThat(rateLimit.reset).isEqualTo(1652307179)
    }

    @Test
    fun `retrieve pull request review threads`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("graphql")
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Post).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/PullRequestReviewThreads.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v4Org("123")

        val result = client.pullRequestReviewThreads(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            prPageSize = 15,
        )

        assertThat(result.pullRequests).hasSize(6)
    }

    @Test
    fun `retrieve commits for file`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("graphql")
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Post).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/FileCommitHistory.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v4Org("123")

        val result = client.fileCommits(
            owner = "NextChapterSoftware",
            repoName = "unblocked",
            path = "projects/clients/client-scm/src/main/kotlin/com/nextchaptersoftware/scm/models/ScmUser.kt",
            maxItems = 4,
        ).toList()

        assertThat(result.size).isEqualTo(4)
        assertThat(result.first().sha).isEqualTo(Hash.parse("3dfa28c2aec71163e976e2dc8d0c22812f66b7a8"))
        assertThat(result.first().commitDate).isEqualTo(Instant.parse("2024-01-25T05:31:30Z"))
        assertThat(result.first().author?.name).isEqualTo("Peter Werry")
        assertThat(result.first().author?.externalUserId).isEqualTo("858772")
    }

    @Test
    fun `pull authenticated organization info`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("organizations", "91906527")
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/Organization.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val response = client.organization(91906527.toString())
        assertThat(response.externalId).isEqualTo("91906527")
    }

    @Test
    fun `pull org members`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("organizations", "91906527", "members")
                    parameters["per_page"] = "100"
                    parameters["role"] = "all"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/OrgMembers.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val response = client.membersByRole("91906527", "all").toList()
        assertThat(11).isEqualTo(response.size)
        assertThat(response.any { it.login == "davidkwlam" }).isTrue
    }

    @Test
    fun `pull repositories`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("installation", "repositories")
                    parameters["per_page"] = "100"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/InstallationRepositories.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3Org("123")

        val repos = client.repositories().toList().flatten()
        assertThat(repos).hasSize(11)
        assertThat(repos.any { it.repoName == "BotherFace" }).isTrue
    }

    @Test
    fun `pull installs`() = runTest {
        val mockEngine = testMockEngine { request ->
            assertThat(
                url {
                    host = "api.github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("app", "installations")
                    parameters["per_page"] = "100"
                    parameters["since"] = "2022-02-03T06:45:48Z"
                },
            ).isEqualTo(
                request.url.asString,
            )
            assertThat(HttpMethod.Get).isEqualTo(request.method)
            assertThat(request.headers[HttpHeaders.UserAgent]).isEqualTo("un-blocked")
            respond(
                content = ByteReadChannel(getResource(this, "/scm/github/Installations.json")),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
            )
        }

        val client = apiFactory.getApi(scm = Scm.GitHub, orgId = null, clientEngine = mockEngine).v3App()

        val response = client.installations(since = Instant.parse("2022-02-03T06:45:48Z")).toList()
        assertThat(1).isEqualTo(response.size)
        assertThat(response[0].targetType).isEqualTo(Organization)
        assertThat(23185793).isEqualTo(response[0].installationId)
    }
}
