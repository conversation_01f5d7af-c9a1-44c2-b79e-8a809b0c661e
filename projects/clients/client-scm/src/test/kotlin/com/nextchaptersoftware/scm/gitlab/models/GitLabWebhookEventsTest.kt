package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabWebhookEventsTest {

    @Test
    fun createGitLabWebhookRequest() {
        val url = "https://getunblocked.com/api/hooks/gitlab".asUrl
        val request = GitLabWebhookEvents.createGitLabWebhookRequest(
            url = url,
        )

        assertThat(request).isEqualTo(
            GitLabWebhookRequest(
                url = url,
                enableSslVerification = true,
                // events
                mergeRequestsEvents = true,
                pipelineEvents = true,
                subgroupEvents = true,
            ),
        )
    }
}
