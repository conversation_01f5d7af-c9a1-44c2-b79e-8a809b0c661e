package com.nextchaptersoftware.scm.auth

import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.respondError
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.headersOf
import io.ktor.utils.io.ByteReadChannel
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.withinPercentage
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class GitHubAuthApiTest {
    private val config = GlobalConfig.INSTANCE
    private val scmConfig = ScmConfig.INSTANCE
    private val scmWebFactory = ScmWebFactory(scmConfig)

    private fun createScmAuthApiFactory(mockEngine: MockEngine): ScmAuthApiFactory {
        return ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = scmWebFactory,
            clientEngine = mockEngine,
        )
    }

    @Test
    fun `throws when unauthorized`() = runTest {
        val mockEngine = MockEngine {
            respondError(
                status = HttpStatusCode.Unauthorized,
            )
        }

        val scmAuthApiFactory = createScmAuthApiFactory(mockEngine = mockEngine)

        val githubAuthApi = scmAuthApiFactory.getApi(
            orgId = null,
            oAuthApiType = Scm.GitHub,
        )

        assertThrows<Exception> {
            githubAuthApi.exchangeForToken(context = OAuthTokenExchangeContext(code = "", state = null))
        }
        assertThrows<Exception> {
            githubAuthApi.refreshAccessTokens(Secret(""))
        }
    }

    @Test
    fun `returns oauth tokens`() = runTest {
        val mockEngine = MockEngine { request ->
            when (request.url.encodedPath) {
                "/login/oauth/access_token" -> respond(
                    content = ByteReadChannel(
                        """
                        {
                          "access_token": "mockAccessToken",
                          "refresh_token": "mockRefreshToken",
                          "expires_in": ${1.hours.inWholeSeconds},
                          "refresh_token_expires_in": ${180.days.inWholeSeconds}
                        }
                        """.trimIndent(),
                    ),
                    status = HttpStatusCode.OK,
                    headers = headersOf(HttpHeaders.ContentType, "application/json"),
                )

                else -> respondError(HttpStatusCode.BadRequest)
            }
        }

        val scmAuthApiFactory = createScmAuthApiFactory(mockEngine = mockEngine)

        val githubAuthApi = scmAuthApiFactory.getApi(
            orgId = null,
            oAuthApiType = Scm.GitHub,
        )

        githubAuthApi.exchangeForToken(context = OAuthTokenExchangeContext(code = "", state = null)).also {
            assertThat(it.oAuthTokens.accessToken).isEqualTo(Secret("mockAccessToken"))
            assertThat(it.oAuthTokens.refreshToken).isEqualTo(Secret("mockRefreshToken"))
            assertThat(
                it.oAuthTokens.accessTokenExpiresAt?.minus(Instant.nowWithMicrosecondPrecision())?.inWholeSeconds,
            ).isCloseTo(
                1.hours.inWholeSeconds,
                withinPercentage(1),
            )
            assertThat(
                it.oAuthTokens.refreshTokenExpiresAt?.minus(Instant.nowWithMicrosecondPrecision())?.inWholeSeconds,
            ).isCloseTo(
                180.days.inWholeSeconds,
                withinPercentage(1),
            )
        }

        githubAuthApi.refreshAccessTokens(Secret("")).also {
            assertThat(it.accessToken).isEqualTo(Secret("mockAccessToken"))
            assertThat(it.refreshToken).isEqualTo(Secret("mockRefreshToken"))
            assertThat(
                it.accessTokenExpiresAt?.minus(Instant.nowWithMicrosecondPrecision())?.inWholeSeconds,
            ).isCloseTo(
                1.hours.inWholeSeconds,
                withinPercentage(1),
            )
            assertThat(
                it.refreshTokenExpiresAt?.minus(Instant.nowWithMicrosecondPrecision())?.inWholeSeconds,
            ).isCloseTo(
                180.days.inWholeSeconds,
                withinPercentage(1),
            )
        }
    }
}
