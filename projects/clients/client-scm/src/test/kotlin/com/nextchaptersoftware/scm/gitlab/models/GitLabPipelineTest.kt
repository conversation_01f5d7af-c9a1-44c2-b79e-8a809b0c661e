package com.nextchaptersoftware.scm.gitlab.models

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.test.utils.testData
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class GitLabPipelineTest {

    private fun test(
        file: String,
        test: (GitLabPipeline) -> Unit = {},
    ) {
        testData(
            file = file,
            decoder = { it.decode<GitLabPipeline>() },
        ) {
            assertThat(it)
                .isNotNull
                .satisfies(test)
        }
    }

    @Test
    fun `pipeline-1 -- failed`() = test("/scm/gitlab/pipeline-1-failed.json")
}
