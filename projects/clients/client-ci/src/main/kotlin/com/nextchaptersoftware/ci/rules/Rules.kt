package com.nextchaptersoftware.ci.rules

object Rules {

    /**
     * A [Rule] that [Result.Allow] iff **ALL** the given [rules] also [Result.Allow]
     */
    open class All<T>(
        private vararg val rules: Rule<T>,
    ) : Rule<T> {

        override val name = rules.joinToString(",  ", prefix = "ALL[", postfix = "]") { it.name }

        override fun evaluate(event: T): Result {
            for (rule in rules) {
                val result = rule.evaluate(event)
                if (result is Result.Deny) {
                    return result
                }
            }
            return Result.Allow
        }
    }

    open class Adapter<I, O>(
        name: String? = null,
        private val rule: Rule<O>,
        private val with: (I) -> O,
    ) : Rule<I> {

        override val name: String = name ?: "ADAPTER[${rule.name}]"

        override fun evaluate(event: I): Result {
            return with(event).let(rule::evaluate)
        }
    }

    /**
     * A [Rule] that [Result.Allow] any event
     */
    abstract class AllowAll<T> : Rule<T> {
        final override fun evaluate(event: T): Result = Result.Allow
    }

    /**
     * A [Rule] that [Result.Allow] whenever the predicate [test] yields `true`
     */
    abstract class AllowIf<T> : Rule<T> {

        protected abstract fun T.item(): String

        protected abstract fun T.test(): Boolean

        final override fun evaluate(event: T) = when (event.test()) {
            true -> Result.Allow
            else -> Result.Deny(rule = name, item = event.item())
        }
    }

    /**
     * A [Rule] that [Result.Deny] any event
     */
    abstract class DenyAll<T> : Rule<T> {

        protected abstract fun T.item(): String

        final override fun evaluate(event: T): Result {
            return Result.Deny(rule = name, item = event.item())
        }
    }

    /**
     * A [Rule] that [Result.Deny] whenever the predicate [test] yields `true`
     */
    abstract class DenyIf<T> : Rule<T> {

        protected abstract fun T.item(): String

        protected abstract fun T.test(): Boolean

        final override fun evaluate(event: T) = when (event.test()) {
            true -> Result.Deny(rule = name, item = event.item())
            else -> Result.Allow
        }
    }
}

fun <I, O> Rule<O>.adapted(
    name: String? = null,
    with: (I) -> O,
): Rule<I> = Rules.Adapter(
    name = name,
    rule = this,
    with = with,
)
