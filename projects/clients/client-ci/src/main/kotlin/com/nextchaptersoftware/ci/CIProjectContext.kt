package com.nextchaptersoftware.ci

import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.gitlab.GitLabPipelinesClientProvider
import java.util.UUID
import kotlinx.serialization.Contextual
import kotlinx.serialization.Serializable

@Serializable
sealed class CIProjectContext {
    protected abstract val ciProvider: Provider

    @Serializable
    data class BitbucketPipelines(
        val externalInstallationId: String,
        val externalRepositoryId: String,
    ) : CIProjectContext() {
        override val ciProvider: Provider = Provider.Bitbucket
    }

    @Serializable
    data class Buildkite(
        val orgSlug: String,
        val projectSlug: String,
    ) : CIProjectContext() {
        override val ciProvider: Provider = Provider.Buildkite
    }

    @Serializable
    data class CircleCI(
        @Contextual
        val projectExternalId: UUID? = null,
        val projectSlug: String,
    ) : CIProjectContext() {
        override val ciProvider: Provider = Provider.CircleCI
    }

    @Serializable
    class GitHubActions(
        val enterpriseAppConfigId: EnterpriseAppConfigId? = null,
        val externalInstallationId: String,
        val repoExternalId: String,
    ) : CIProjectContext() {
        override val ciProvider: Provider = Provider.GitHubActions
    }

    @Serializable
    class GitLabPipelines(
        val externalInstallationId: String,
        val externalRepositoryId: String,
    ) : CIProjectContext() {
        override val ciProvider: Provider = Provider.GitLabPipelines
    }
}

suspend fun BitbucketPipelinesClientProvider.create(
    projectContext: CIProjectContext.BitbucketPipelines,
) = create(
    externalInstallationId = projectContext.externalInstallationId,
    externalRepositoryId = projectContext.externalRepositoryId,
)

fun GitHubActionsClientProvider.create(
    orgId: OrgId,
    projectContext: CIProjectContext.GitHubActions,
) = create(
    orgId = orgId,
    enterpriseAppConfigId = projectContext.enterpriseAppConfigId,
    providerInstallationId = projectContext.externalInstallationId,
)

suspend fun GitLabPipelinesClientProvider.create(
    projectContext: CIProjectContext.GitLabPipelines,
) = create(
    providerInstallationId = projectContext.externalInstallationId,
    externalRepositoryId = projectContext.externalRepositoryId,
)
