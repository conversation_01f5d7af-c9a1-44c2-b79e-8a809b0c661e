package com.nextchaptersoftware.ci

import com.nextchaptersoftware.ci.bitbucket.BitbucketPipelinesProjectApi
import com.nextchaptersoftware.ci.buildkite.BuildkiteProjectApi
import com.nextchaptersoftware.ci.circleci.CircleCIProjectApi
import com.nextchaptersoftware.ci.config.CISecretsConfig
import com.nextchaptersoftware.ci.github.GitHubActionsProjectApi
import com.nextchaptersoftware.crypto.RSAClientServerCryptoSystem
import com.nextchaptersoftware.db.models.CIToken
import com.nextchaptersoftware.db.models.Installation
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.ciTokenStore
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.required

class CIProjectApiFactory(
    private val bitbucketPipelinesClientProvider: BitbucketPipelinesClientProvider,
    private val ciSecretsConfig: CISecretsConfig,
    private val gitHubActionsClientProvider: GitHubActionsClientProvider,
) {

    private val tokenSecretDecryption by lazy {
        RSAClientServerCryptoSystem.RSADecryption(ciSecretsConfig.ci.secretsPrivateKey.value)
    }

    suspend fun getProjectApi(
        ciInstallation: Installation,
        projectContext: CIProjectContext,
    ): CIProjectApi {
        return when (ciInstallation.provider) {
            Provider.BitbucketPipelines -> {
                BitbucketPipelinesProjectApi(
                    projectContext = projectContext as CIProjectContext.BitbucketPipelines,
                    bitbucketPipelinesClient = bitbucketPipelinesClientProvider.create(projectContext),
                )
            }

            Provider.Buildkite -> {
                val token = findCIToken(
                    ciInstallation = ciInstallation,
                )

                BuildkiteProjectApi(
                    projectContext = projectContext as CIProjectContext.Buildkite,
                    token = tokenSecretDecryption.decrypt(token.encryptedToken),
                )
            }

            Provider.CircleCI -> {
                val token = findCIToken(
                    ciInstallation = ciInstallation,
                )

                CircleCIProjectApi(
                    projectContext = projectContext as CIProjectContext.CircleCI,
                    token = tokenSecretDecryption.decrypt(token.encryptedToken),
                )
            }

            Provider.GitHubActions -> {
                GitHubActionsProjectApi(
                    projectContext = projectContext as CIProjectContext.GitHubActions,
                    gitHubActionsClient = gitHubActionsClientProvider.create(orgId = ciInstallation.orgId, projectContext = projectContext),
                )
            }

            Provider.GitLabPipelines -> {
                TODO() // GitLabPipelinesProjectApi
            }

            else -> {
                error("Not implemented")
            }
        }
    }
}

private suspend fun findCIToken(
    ciInstallation: Installation,
): CIToken {
    val tokens = ciTokenStore.listTokens(ciInstallation.id)
        .filter { it.isValid(ciInstallation.provider) }
        .nullIfEmpty()
        .required { "No valid token found for CI installation" }

    // TODO should be the first one that satisfies the CIProjectContext org/project
    return tokens.first()
}
