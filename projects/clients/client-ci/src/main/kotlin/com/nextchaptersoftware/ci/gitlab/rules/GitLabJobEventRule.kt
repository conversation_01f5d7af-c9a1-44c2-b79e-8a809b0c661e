package com.nextchaptersoftware.ci.gitlab.rules

import com.nextchaptersoftware.ci.rules.Rules
import com.nextchaptersoftware.scm.gitlab.models.GitLabJobEvent

/**
 * denying any job events, next step is to disable the webhook to stop receiving
 * them and then we can safely remove [GitLabJobEvent] and all friends
 */
object GitLabJobEventRule : Rules.DenyAll<GitLabJobEvent>() {
    override fun GitLabJobEvent.item(): String = "job:$id"
}
