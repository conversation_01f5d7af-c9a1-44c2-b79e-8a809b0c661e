package com.nextchaptersoftware.ci.gitlab.models

import com.nextchaptersoftware.ci.models.CiExecution
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.gitlab.models.GitLabJob

fun GitLabJob.asCiJob() = CiExecution.CiJob(
    type = javaClass.simpleName,
    displayName = "$stage: $name",
    externalId = id.toString(),
    externalParentId = pipeline.id.toString(),
    apiUrl = null,
    htmlUrl = webUrl.asUrl,
    attempt = null,
    status = status.asCiStatus(),
    result = status.asCiResult(),
    createdAt = createdAt,
    startedAt = startedAt,
    completedAt = finishedAt,
)
