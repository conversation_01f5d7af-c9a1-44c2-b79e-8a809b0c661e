package com.nextchaptersoftware.ci.gitlab.models

import com.nextchaptersoftware.ci.models.CiResult
import com.nextchaptersoftware.ci.models.CiStatus
import com.nextchaptersoftware.scm.gitlab.models.GitLabStatus

fun GitLabStatus.asCiStatus(): CiStatus = when (this) {
    GitLabStatus.Canceling,
    GitLabStatus.Created,
    GitLabStatus.Manual,
    GitLabStatus.Pending,
    GitLabStatus.Preparing,
    GitLabStatus.Scheduled,
    GitLabStatus.WaitingForResource,
        -> CiStatus.Waiting

    GitLabStatus.Running,
        -> CiStatus.Running

    GitLabStatus.Canceled,
    GitLabStatus.Failed,
    GitLabStatus.Skipped,
    GitLabStatus.Success,
        -> CiStatus.Complete
}

/**
 * Pipelines and Jobs identified as [CiResult.Failure] will be promoted to triage
 */
fun GitLabStatus.asCiResult(): CiResult = when (this) {
    GitLabStatus.Canceling,
    GitLabStatus.Created,
    GitLabStatus.Manual,
    GitLabStatus.Pending,
    GitLabStatus.Preparing,
    GitLabStatus.Running,
    GitLabStatus.Scheduled,
    GitLabStatus.WaitingForResource,
        -> CiResult.Pending

    GitLabStatus.Success,
        -> CiResult.Success

    GitLabStatus.Failed,
        -> CiResult.Failure

    GitLabStatus.Canceled,
    GitLabStatus.Skipped,
        -> CiResult.Ignored
}
