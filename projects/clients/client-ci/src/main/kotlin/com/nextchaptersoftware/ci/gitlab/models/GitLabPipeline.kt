package com.nextchaptersoftware.ci.gitlab.models

import com.nextchaptersoftware.ci.models.CiExecution
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.gitlab.models.GitLabPipeline
import kotlin.time.Duration.Companion.seconds
import kotlinx.datetime.Instant

fun GitLabPipeline.asCiBuild() = CiExecution.CiBuild(
    runner = "GitLab Pipelines",
    type = javaClass.simpleName,
    externalId = id.toString(),
    displayName = name ?: "Pipeline $id",
    displayNumber = iid,
    apiUrl = null,
    htmlUrl = webUrl?.asUrl,
    attempt = null,
    status = status.asCiStatus(),
    result = status.asCiResult(),
    createdAt = createdAt,
    startedAt = startedAt(),
    completedAt = finishedAt,
    pullRequestNumber = null,
    baseSha = beforeSha,
    headSha = sha,
)

/**
 * For retries, gitlab keeps sending the `startedAt` for the first attempt, but we can infer the time using `finishedAt - duration`
 *
 * Because gitlab does not expose `attempt`, the `startedAt` becomes critical to select the correct sub-set of jobs to triage.
 */
private fun GitLabPipeline.startedAt(): Instant? {
    val finishedAt = finishedAt ?: return startedAt
    val duration = duration ?: return startedAt

    return finishedAt - duration.seconds
}
