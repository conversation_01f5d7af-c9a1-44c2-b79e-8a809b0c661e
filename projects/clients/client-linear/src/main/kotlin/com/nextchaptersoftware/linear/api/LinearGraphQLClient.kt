package com.nextchaptersoftware.linear.api

import com.nextchaptersoftware.graphql.client.GraphQLHttpClient
import com.nextchaptersoftware.graphql.client.GraphQLKtorClient
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.sksamuel.hoplite.Secret
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.defaultRequest
import io.ktor.http.Url
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds

object LinearGraphQLClient {
    fun makeClient(
        apiEndpoint: Url,
        accessToken: Secret,
        timeout: Duration = 60.seconds,
    ) = GraphQLKtorClient(
        httpClient = GraphQLHttpClient.create(orgId = null).config {
            install(Auth) {
                bearer {
                    loadTokens {
                        BearerTokens(
                            accessToken = accessToken.value,
                            refreshToken = "",
                        )
                    }
                }
            }
            defaultRequest {
                url(apiEndpoint.asString)
            }
            install(HttpTimeout) {
                requestTimeoutMillis = timeout.inWholeMilliseconds
                socketTimeoutMillis = timeout.inWholeMilliseconds
            }
        },
    )
}
