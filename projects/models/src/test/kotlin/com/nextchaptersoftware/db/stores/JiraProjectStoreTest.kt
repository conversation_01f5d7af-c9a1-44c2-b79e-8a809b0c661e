package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeJiraSite
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.JiraProjectDAO
import com.nextchaptersoftware.db.models.JiraProjectModel
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class JiraProjectStoreTest : DatabaseTestsBase() {
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var installation: InstallationDAO
    private lateinit var jiraSite: JiraSiteDAO

    private val store = Stores.jiraProjectStore

    private suspend fun setup() {
        org = makeOrg()
        scmTeam = makeScmTeam(org = org)
        installation = makeInstallation(org = org)
        jiraSite = makeJiraSite(installation = installation)
    }

    @Test
    fun `upsert and getJiraProjectIdByNameOrKeyMatch`() = suspendingDatabaseTest {
        setup()

        val new = store.upsert(
            jiraSiteId = jiraSite.id.value,
            projectId = "10000",
            projectName = "Test Project",
            projectKey = "TP",
            isTeamManaged = false,
        )

        assertThat(store.getJiraProjectIdByNameOrKeyMatch(orgId = org.idValue, name = "test project")).isEqualTo(new.id)
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(orgId = org.idValue, name = "blahblahblah")).isNull()
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(installationId = installation.idValue, name = "test project")).isEqualTo(new.id)
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(installationId = installation.idValue, name = "blahblahblah")).isNull()

        assertThat(new.jiraSiteId).isEqualTo(jiraSite.id.value)
        assertThat(new.projectId).isEqualTo("10000")
        assertThat(new.projectName).isEqualTo("Test Project")
        assertThat(new.projectKey).isEqualTo("TP")

        val updated = store.upsert(
            jiraSiteId = jiraSite.id.value,
            projectId = "10000",
            projectName = "Test Project RENAME",
            projectKey = "TPR",
            isTeamManaged = false,
        )

        assertThat(store.getJiraProjectIdByNameOrKeyMatch(orgId = org.idValue, name = "test project RENAMED")).isEqualTo(new.id)
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(orgId = org.idValue, name = "blahblahblah")).isNull()
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(installationId = installation.idValue, name = "test project RENAMED")).isEqualTo(new.id)
        assertThat(store.getJiraProjectIdByNameOrKeyMatch(installationId = installation.idValue, name = "blahblahblah")).isNull()

        assertThat(new.id).isEqualTo(updated.id)
        assertThat(updated.jiraSiteId).isEqualTo(jiraSite.id.value)
        assertThat(updated.projectId).isEqualTo("10000")
        assertThat(updated.projectName).isEqualTo("Test Project RENAME")
        assertThat(updated.projectKey).isEqualTo("TPR")

        store.delete(
            jiraSiteId = jiraSite.id.value,
            jiraProjectId = new.id,
        )

        assertThat(suspendedTransaction { JiraProjectDAO.find { JiraProjectModel.jiraSite eq jiraSite.id } }).isEmpty()
    }

    @Test
    fun updatePermissionSchemeId() = suspendingDatabaseTest {
        setup()

        val project = store.upsert(
            jiraSiteId = jiraSite.id.value,
            projectId = "10000",
            projectName = "Test Project",
            projectKey = "TP",
            isTeamManaged = false,
        )

        assertThat(project.permissionSchemeId).isNull()

        val jiraPermissionScheme = Stores.jiraPermissionSchemeStore.upsert(
            jiraSiteId = jiraSite.id.value,
            externalId = "0",
            permissionScheme = ByteArray(0),
        )

        store.updatePermissionSchemeId(
            jiraProjectId = project.id,
            jiraPermissionSchemeId = jiraPermissionScheme.id,
        )

        assertThat(store.findById(org.idValue, project.id)?.permissionSchemeId)
            .isEqualTo(jiraPermissionScheme.id)

        store.upsert(
            jiraSiteId = jiraSite.id.value,
            projectId = "10000",
            projectName = "Test Project EDITED",
            projectKey = "TP",
            isTeamManaged = false,
        )

        assertThat(store.findById(org.idValue, project.id)?.permissionSchemeId)
            .isEqualTo(jiraPermissionScheme.id)
    }
}
