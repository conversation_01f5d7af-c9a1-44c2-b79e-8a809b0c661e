package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.OperatorExtensions.coalesceTo
import com.nextchaptersoftware.db.common.functions.extractEpoch
import com.nextchaptersoftware.db.common.functions.percentileCont
import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildJobModel
import com.nextchaptersoftware.db.models.BuildModel
import com.nextchaptersoftware.db.models.BuildTriage
import com.nextchaptersoftware.db.models.BuildTriageBundle
import com.nextchaptersoftware.db.models.BuildTriageDAO
import com.nextchaptersoftware.db.models.BuildTriageExecutionId
import com.nextchaptersoftware.db.models.BuildTriageId
import com.nextchaptersoftware.db.models.BuildTriageModel
import com.nextchaptersoftware.db.models.BuildTriageReactionModel
import com.nextchaptersoftware.db.models.BuildTriageReactionSentiment
import com.nextchaptersoftware.db.models.BuildTriageReviewStatus
import com.nextchaptersoftware.db.models.BuildTriageState
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.PullRequestModel
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toBuildTriage
import com.nextchaptersoftware.db.models.toBuildTriageBundle
import com.nextchaptersoftware.db.sql.Intervals.intervalLiteral
import com.nextchaptersoftware.db.sql.QueryExtensions.maybeLimit
import com.nextchaptersoftware.db.sql.SelectExtensions.selectAll
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.Stores.buildTriageReactionStore
import com.nextchaptersoftware.db.stores.Stores.buildTriageStore
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.ReportingUtils.REPORTING_TIMEZONE
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import kotlin.time.Duration
import kotlin.time.Duration.Companion.days
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds
import kotlinx.datetime.Instant
import kotlinx.datetime.atStartOfDayIn
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SortOrder
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greater
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.isNotNull
import org.jetbrains.exposed.sql.SqlExpressionBuilder.less
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.minus
import org.jetbrains.exposed.sql.SqlExpressionBuilder.plus
import org.jetbrains.exposed.sql.Transaction
import org.jetbrains.exposed.sql.alias
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.countDistinct
import org.jetbrains.exposed.sql.exists
import org.jetbrains.exposed.sql.intLiteral
import org.jetbrains.exposed.sql.kotlin.datetime.date
import org.jetbrains.exposed.sql.kotlin.datetime.hour
import org.jetbrains.exposed.sql.kotlin.datetime.timestampLiteral
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsertReturning

class BuildTriageStore internal constructor() {

    suspend fun upsert(
        trx: Transaction? = null,
        buildId: BuildId,
        jobId: BuildJobId,
        jobIds: Collection<BuildJobId>,
        executionId: BuildTriageExecutionId,
        groupKey: String,
        state: BuildTriageState,
        header: String,
        details: String,
        debug: String?,
        isInsider: Boolean,
        isVisible: Boolean,
        commitIssueSha: String?,
    ): BuildTriage = suspendedTransaction(trx) {
        BuildTriageModel.upsertReturning(
            keys = arrayOf(
                BuildTriageModel.build,
                BuildTriageModel.job,
            ),
            onUpdateExclude = BuildTriageModel.columns - setOf(
                BuildTriageModel.jobs,
                BuildTriageModel.execution,
                BuildTriageModel.groupKey,
                BuildTriageModel.state,
                BuildTriageModel.header,
                BuildTriageModel.header,
                BuildTriageModel.details,
                BuildTriageModel.debug,
                BuildTriageModel.header,
                BuildTriageModel.isVisible,
                BuildTriageModel.isInsider,
                BuildTriageModel.commitIssueSha,
            ),
        ) { insertStmt ->
            insertStmt[this.build] = buildId
            insertStmt[this.job] = jobId
            insertStmt[this.jobs] = jobIds.sorted()
            insertStmt[this.execution] = executionId
            insertStmt[this.groupKey] = groupKey
            insertStmt[this.state] = state
            insertStmt[this.header] = header
            insertStmt[this.details] = details
            insertStmt[this.debug] = debug
            insertStmt[this.isInsider] = isInsider
            insertStmt[this.isVisible] = isVisible
            insertStmt[this.commitIssueSha] = commitIssueSha
        }
            .single()
            .toBuildTriage()
    }

    suspend fun review(
        triageId: BuildTriageId,
        reviewerId: IdentityId,
        status: BuildTriageReviewStatus?,
        rootCause: String?,
        comment: String?,
    ) = suspendedTransaction {
        BuildTriageModel.update(
            where = { BuildTriageModel.id eq triageId },
        ) { stmt ->
            stmt[this.reviewer] = reviewerId
            stmt[this.reviewStatus] = status
            stmt[this.reviewRootCause] = rootCause
            stmt[this.reviewComment] = comment
        }
    }

    private suspend fun markTriagesAsState(
        trx: Transaction? = null,
        pullRequestId: PullRequestId,
        fromState: BuildTriageState,
        intoState: BuildTriageState,
        jobDisplayName: String,
        theCommitIssueSha: String? = null,
        useCommitIssueSha: Boolean = false,
        theCommitFixedSha: String? = null,
        useCommitFixedSha: Boolean = false,
    ) = suspendedTransaction(trx) {
        val jobSubQuery = BuildJobModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildJobModel.build,
                joinType = JoinType.INNER,
            ) {
                BuildModel.pullRequest eq pullRequestId
            }
            .select(intLiteral(1))
            .whereAll(
                BuildJobModel.id eq BuildTriageModel.job,
                BuildJobModel.displayName eq jobDisplayName,
            )

        BuildTriageModel.update(
            where = {
                AllOp(
                    BuildTriageModel.isVisible eq true,
                    BuildTriageModel.state eq fromState,
                    exists(jobSubQuery),
                )
            },
        ) { update ->
            update[this.state] = intoState
            if (useCommitIssueSha) {
                update[this.commitIssueSha] = theCommitIssueSha
            }
            if (useCommitFixedSha) {
                update[this.commitFixedSha] = theCommitFixedSha
            }
        }
    }

    suspend fun markTriagesOnFailure(
        trx: Transaction? = null,
        pullRequestId: PullRequestId,
        jobDisplayName: String,
    ) = suspendedTransaction(trx) {
        // 1. Open -> Outdated
        val open = markTriagesAsState(
            trx = this,
            pullRequestId = pullRequestId,
            jobDisplayName = jobDisplayName,
            fromState = BuildTriageState.Open,
            intoState = BuildTriageState.Outdated,
            useCommitFixedSha = true,
            theCommitFixedSha = null,
        )
        // 2. Fixed -> Obsolete
        val fixed = markTriagesAsState(
            trx = this,
            pullRequestId = pullRequestId,
            jobDisplayName = jobDisplayName,
            fromState = BuildTriageState.Fixed,
            intoState = BuildTriageState.Obsolete,
            useCommitFixedSha = true,
            theCommitFixedSha = null,
        )
        open + fixed
    }

    suspend fun markTriagesOnSuccess(
        pullRequestId: PullRequestId,
        jobDisplayName: String,
        commitFixedSha: String,
    ) = suspendedTransaction {
        // 1. Open -> Fixed
        val fixed = markTriagesAsState(
            trx = this,
            pullRequestId = pullRequestId,
            jobDisplayName = jobDisplayName,
            fromState = BuildTriageState.Open,
            intoState = BuildTriageState.Fixed,
            useCommitFixedSha = true,
            theCommitFixedSha = commitFixedSha,
        )
        // 2. Outdated -> Obsolete
        val obsoleted = markTriagesAsState(
            trx = this,
            pullRequestId = pullRequestId,
            jobDisplayName = jobDisplayName,
            fromState = BuildTriageState.Outdated,
            intoState = BuildTriageState.Obsolete,
            useCommitFixedSha = true,
            theCommitFixedSha = commitFixedSha,
        )
        fixed + obsoleted
    }

    suspend fun findById(
        triageId: BuildTriageId,
    ) = suspendedTransaction {
        BuildTriageDAO.findById(id = triageId)?.asDataModel()
    }

    suspend fun findByBuild(
        buildId: BuildId,
        jobIds: Collection<BuildJobId>? = null,
        limit: Int? = null,
    ) = suspendedTransaction {
        BuildTriageModel
            .selectAll()
            .whereAll(
                BuildTriageModel.build eq buildId,
                jobIds?.let { BuildTriageModel.job inList it.toSet() },
            )
            .maybeLimit(limit)
            .firstOrNull()
            ?.toBuildTriage()
    }

    @Suppress("LongMethod", "CyclomaticComplexMethod")
    suspend fun findAllBundles(
        ciInstallationId: InstallationId? = null,
        scmTeamId: ScmTeamId? = null,
        repoId: RepoId? = null,
        pullRequestIds: Collection<PullRequestId>? = null,
        buildIds: Collection<BuildId>? = null,
        jobIds: Collection<BuildJobId>? = null,
        triagesIds: Collection<BuildTriageId>? = null,
        sentiment: BuildTriageReactionSentiment? = null,
        filterIsInsider: Boolean = false,
        limit: Int = 1000,
    ): List<BuildTriageBundle>? = suspendedTransaction {
        val pullRequestCreator = IdentityModel.alias("pullRequestCreator")
        val triageReviewer = IdentityModel.alias("triageReviewer")

        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                AllOp(
                    buildIds?.let { BuildModel.id inList it.toSet() },
                    ciInstallationId?.let { BuildModel.ciInstallation eq it },
                    Op.TRUE,
                )
            }
            .join(
                otherTable = BuildJobModel,
                otherColumn = BuildJobModel.id,
                onColumn = BuildTriageModel.job,
                joinType = JoinType.INNER,
            ) {
                jobIds?.let { BuildJobModel.id inList it.toSet() } ?: Op.TRUE
            }
            .join(
                otherTable = PullRequestModel,
                otherColumn = PullRequestModel.id,
                onColumn = BuildModel.pullRequest,
                joinType = JoinType.INNER,
            ) {
                pullRequestIds?.let { PullRequestModel.id inList it.toSet() } ?: Op.TRUE
            }
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
                repoId?.let { RepoModel.id eq it } ?: Op.TRUE
            }
            .join(
                otherTable = ScmTeamModel,
                otherColumn = ScmTeamModel.id,
                onColumn = RepoModel.scmTeam,
                joinType = JoinType.INNER,
            ) {
                scmTeamId?.let { RepoModel.scmTeam eq it } ?: Op.TRUE
            }
            .join(
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = ScmTeamModel.org,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = MemberModel,
                otherColumn = MemberModel.id,
                onColumn = PullRequestModel.creator,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = pullRequestCreator,
                otherColumn = pullRequestCreator[IdentityModel.id],
                onColumn = MemberModel.identity,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = triageReviewer,
                otherColumn = triageReviewer[IdentityModel.id],
                onColumn = BuildTriageModel.reviewer,
                joinType = JoinType.LEFT,
            )
            .selectAll(
                OrgModel.columns,
                ScmTeamModel.columns,
                RepoModel.columns,
                PullRequestModel.columns,
                BuildModel.columns,
                BuildJobModel.columns,
                BuildTriageModel.columns,
                pullRequestCreator.columns,
                triageReviewer.columns,
            )
            .whereAll(
                triagesIds?.let { BuildTriageModel.id inList it.toSet() },
                filterIsInsider.takeIf { it }?.let { BuildTriageModel.isInsider eq false },
                sentiment?.let {
                    exists(
                        BuildTriageReactionModel
                            .select(BuildTriageReactionModel.pullRequest)
                            .withDistinct(true)
                            .whereAll(
                                BuildTriageReactionModel.pullRequest eq PullRequestModel.id,
                                BuildTriageReactionModel.sentiment eq it,
                            ),
                    )
                },
                Op.TRUE,
            )
            .orderBy(BuildTriageModel.createdAt, SortOrder.DESC)
            .limit(limit)
            .map { it.toBuildTriageBundle() }
            .nullIfEmpty()
            ?.let { bundles ->
                val aggregates = buildTriageReactionStore.findAll(triageIds = bundles.map { it.triage.id })?.groupBy { it.triageId }
                bundles.map { bundle ->
                    aggregates
                        ?.get(bundle.triage.id)
                        ?.let { bundle.copy(reactions = it) }
                        ?: bundle
                }
            }
    }

    suspend fun findAll(
        triagesIds: Collection<BuildTriageId>? = null,
        buildIds: Collection<BuildId>? = null,
        jobIds: Collection<BuildJobId>? = null,
    ): List<BuildTriage>? = suspendedTransaction {
        BuildTriageModel
            .selectAll()
            .whereAll(
                triagesIds?.let { BuildTriageModel.id inList it.toSet() },
                buildIds?.let { BuildTriageModel.build inList it.toSet() },
                jobIds?.let { BuildTriageModel.job inList it.toSet() },
                Op.TRUE,
            )
            .map {
                it.toBuildTriage()
            }
            .nullIfEmpty()
    }

    suspend fun findAll(
        pullRequestId: PullRequestId,
        includeNonVisible: Boolean? = null,
    ): List<BuildTriage>? = suspendedTransaction {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                BuildModel.pullRequest eq pullRequestId
            }
            .select(BuildTriageModel.columns)
            .whereAll(
                when (includeNonVisible) {
                    true -> Op.TRUE
                    else -> BuildTriageModel.isVisible eq true
                },
            )
            .map { it.toBuildTriage() }
            .nullIfEmpty()
    }

    suspend fun findAllStates(
        trx: Transaction? = null,
        pullRequestId: PullRequestId,
    ): Set<BuildTriageState>? = suspendedTransaction(trx) {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            )
            .select(BuildTriageModel.state)
            .withDistinct(true)
            .where {
                BuildModel.pullRequest eq pullRequestId
            }
            .mapNotNull {
                it[BuildTriageModel.state]
            }
            .toSet()
            .nullIfEmpty()
    }

    /**
     * Calculates the pull request triage state based on the generated triage reports
     */
    suspend fun findTriageStateFor(
        pullRequestId: PullRequestId,
    ): BuildTriageState? = suspendedTransaction fn@{
        val state = buildTriageStore.findAllStates(
            trx = this@fn,
            pullRequestId = pullRequestId,
        )
            ?.min()

        state?.let {
            if (it.isActive) {
                BuildTriageState.Open
            } else {
                BuildTriageState.Fixed
            }
        }
    }

    suspend fun findBestBeforeReaction(
        pullRequestId: PullRequestId,
        reactedAt: Instant,
        threshold: Duration = 5.minutes,
    ): BuildTriage? = suspendedTransaction {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                BuildModel.pullRequest eq pullRequestId
            }
            .selectAll()
            .whereAll(
                BuildTriageModel.isVisible eq true,
                BuildTriageModel.createdAt lessEq reactedAt,
            )
            .orderBy(BuildTriageModel.createdAt, SortOrder.DESC)
            .limit(2)
            .map { it.toBuildTriage() }
            .let {
                val last = it.getOrNull(0)
                val prev = it.getOrNull(1)
                when {
                    last == null -> null

                    prev == null -> last

                    else -> last.takeIf {
                        val delta = last.createdAt - prev.createdAt
                        delta > threshold
                    }
                }
            }
    }

    data class TriageGrowthMetrics(
        val date: Instant,
        val triages: Long,
    )

    suspend fun getTriageGrowthMetrics(
        scmTeamId: ScmTeamId? = null,
        ciInstallationId: InstallationId? = null,
        startDate: Instant? = null,
        includeNonVisible: Boolean,
        includeInsiders: Boolean?,
    ): List<TriageGrowthMetrics> = suspendedTransaction {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                ciInstallationId?.let { BuildModel.ciInstallation eq it } ?: Op.TRUE
            }
            .join(
                otherTable = PullRequestModel,
                otherColumn = PullRequestModel.id,
                onColumn = BuildModel.pullRequest,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
                scmTeamId?.let { RepoModel.scmTeam eq it } ?: Op.TRUE
            }
            .select(
                BuildTriageModel.createdAt.date(),
                BuildTriageModel.id.count(),
            )
            .whereAll(
                startDate?.let { BuildTriageModel.createdAt greaterEq it },
                when (includeNonVisible) {
                    true -> Op.TRUE
                    else -> BuildTriageModel.isVisible eq true
                },
                when (includeInsiders) {
                    true -> null
                    else -> BuildTriageModel.isInsider eq false
                },
            )
            .groupBy(
                BuildTriageModel.createdAt.date(),
            )
            .orderBy(
                BuildTriageModel.createdAt.date(),
            )
            .map {
                TriageGrowthMetrics(
                    date = it[BuildTriageModel.createdAt.date()].atStartOfDayIn(timeZone = REPORTING_TIMEZONE),
                    triages = it[BuildTriageModel.id.count()],
                )
            }
    }

    data class UserEngagedMetrics(
        val orgName: String,
        val date: Instant,
        val users: Long,
    )

    suspend fun getUserEngagedMetrics(
        scmTeamId: ScmTeamId? = null,
        ciInstallationId: InstallationId? = null,
        startDate: Instant?,
        includeInsiders: Boolean?,
    ): List<UserEngagedMetrics> = suspendedTransaction {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                ciInstallationId?.let { BuildModel.ciInstallation eq it } ?: Op.TRUE
            }
            .join(
                otherTable = PullRequestModel,
                otherColumn = PullRequestModel.id,
                onColumn = BuildModel.pullRequest,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
                scmTeamId?.let { RepoModel.scmTeam eq it } ?: Op.TRUE
            }
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = PullRequestModel.creatorOrgMember,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = OrgMemberModel.org,
                joinType = JoinType.INNER,
            )
            .select(
                OrgModel.displayName,
                BuildTriageModel.createdAt.date(),
                PullRequestModel.creatorOrgMember.countDistinct(),
            )
            .groupBy(
                OrgModel.displayName,
                BuildTriageModel.createdAt.date(),
            )
            .withDistinct(true)
            .whereAll(
                startDate?.let { BuildTriageModel.createdAt greaterEq it },
                BuildTriageModel.isVisible eq true,
                when (includeInsiders) {
                    true -> null
                    else -> BuildTriageModel.isInsider eq false
                },
            )
            .map {
                UserEngagedMetrics(
                    orgName = it[OrgModel.displayName],
                    date = it[BuildTriageModel.createdAt.date()].atStartOfDayIn(timeZone = REPORTING_TIMEZONE),
                    users = it[PullRequestModel.creatorOrgMember.countDistinct()],
                )
            }
    }

    data class UserGrowthMetrics(
        val orgName: String,
        val date: Instant,
        val users: Long,
    )

    /**
     * count of outsider persons that created their account after one of their PRs received one visible triage
     */
    suspend fun getUserGrowthMetrics(
        scmTeamId: ScmTeamId? = null,
        ciInstallationId: InstallationId? = null,
        startDate: Instant? = null,
        includeInsiders: Boolean?,
    ): List<UserGrowthMetrics> = suspendedTransaction {
        BuildTriageModel
            .join(
                otherTable = BuildModel,
                otherColumn = BuildModel.id,
                onColumn = BuildTriageModel.build,
                joinType = JoinType.INNER,
            ) {
                ciInstallationId?.let { BuildModel.ciInstallation eq it } ?: Op.TRUE
            }
            .join(
                otherTable = PullRequestModel,
                otherColumn = PullRequestModel.id,
                onColumn = BuildModel.pullRequest,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
                scmTeamId?.let { RepoModel.scmTeam eq it } ?: Op.TRUE
            }
            .join(
                otherTable = OrgMemberModel,
                otherColumn = OrgMemberModel.id,
                onColumn = PullRequestModel.creatorOrgMember,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = OrgModel,
                otherColumn = OrgModel.id,
                onColumn = OrgMemberModel.org,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = PersonModel,
                otherColumn = PersonModel.id,
                onColumn = OrgMemberModel.person,
                joinType = JoinType.INNER,
            )
            .select(
                OrgModel.displayName,
                PersonModel.createdAt.date(),
                PersonModel.id.countDistinct(),
            )
            .groupBy(
                OrgModel.displayName,
                PersonModel.createdAt.date(),
            )
            .withDistinct(true)
            .whereAll(
                startDate?.let { BuildTriageModel.createdAt greaterEq it },
                startDate?.let { PersonModel.createdAt greaterEq it },
                BuildTriageModel.isVisible eq true,
                when (includeInsiders) {
                    true -> null
                    else -> BuildTriageModel.isInsider eq false
                },
                PersonModel.createdAt greater BuildTriageModel.createdAt,
                PersonModel.createdAt less BuildTriageModel.createdAt + intervalLiteral(3.days),
            )
            .map {
                UserGrowthMetrics(
                    orgName = it[OrgModel.displayName],
                    date = it[PersonModel.createdAt.date()].atStartOfDayIn(timeZone = REPORTING_TIMEZONE),
                    users = it[PersonModel.id.countDistinct()],
                )
            }
    }

    data class PullRequestGrowthMetrics(
        val date: Instant,
        val pullRequests: Long,
    )

    suspend fun getPullRequestGrowthMetrics(
        scmTeamId: ScmTeamId? = null,
        ciInstallationId: InstallationId? = null,
        startDate: Instant,
        filterTriaged: Boolean,
        includeInsiders: Boolean?,
    ): List<PullRequestGrowthMetrics> = suspendedTransaction {
        PullRequestModel
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
                AllOp(
                    RepoModel.isCiEnabled eq true,
                    scmTeamId?.let { RepoModel.scmTeam eq it },
                )
            }
            .select(
                PullRequestModel.createdAt.date(),
                PullRequestModel.id.countDistinct(),
            )
            .withDistinct(true)
            .groupBy(
                PullRequestModel.createdAt.date(),
            )
            .whereAll(
                PullRequestModel.createdAt greaterEq startDate,
                filterTriaged.takeIf { it }?.let {
                    exists(
                        BuildTriageModel
                            .join(
                                otherTable = BuildModel,
                                otherColumn = BuildModel.id,
                                onColumn = BuildTriageModel.build,
                                joinType = JoinType.INNER,
                            ) {
                                ciInstallationId?.let { BuildModel.ciInstallation eq it } ?: Op.TRUE
                            }
                            .select(intLiteral(1))
                            .whereAll(
                                BuildModel.pullRequest eq PullRequestModel.id,
                                BuildTriageModel.isVisible eq true,
                                when (includeInsiders) {
                                    true -> null
                                    else -> BuildTriageModel.isInsider eq false
                                },
                            ),
                    )
                },
            )
            .map {
                PullRequestGrowthMetrics(
                    date = it[PullRequestModel.createdAt.date()].atStartOfDayIn(timeZone = REPORTING_TIMEZONE),
                    pullRequests = it[PullRequestModel.id.countDistinct()],
                )
            }
    }

    data class TriageLatencyMetrics(
        val date: Instant,
        val p50: Duration,
        val p90: Duration,
        val p99: Duration,
    )

    suspend fun getTriageLatencyMetrics(
        scmTeamId: ScmTeamId? = null,
        ciInstallationId: InstallationId? = null,
        includeInsiders: Boolean? = null,
        startDate: Instant? = null,
    ): List<TriageLatencyMetrics> = suspendedTransaction {
        // exposed can't handle non-null expressions because the column is nullable, even having a non-null clause
        val now = Instant.nowWithMicrosecondPrecision().let(::timestampLiteral)

        val p50 = BuildTriageModel.createdAt.minus(BuildModel.completedAt.coalesceTo(now)).percentileCont(p = 0.50).extractEpoch()
        val p90 = BuildTriageModel.createdAt.minus(BuildModel.completedAt.coalesceTo(now)).percentileCont(p = 0.90).extractEpoch()
        val p99 = BuildTriageModel.createdAt.minus(BuildModel.completedAt.coalesceTo(now)).percentileCont(p = 0.99).extractEpoch()

        BuildModel
            .join(
                otherTable = PullRequestModel,
                otherColumn = PullRequestModel.id,
                onColumn = BuildModel.pullRequest,
                joinType = JoinType.INNER,
            )
            .join(
                otherTable = RepoModel,
                otherColumn = RepoModel.id,
                onColumn = PullRequestModel.repo,
                joinType = JoinType.INNER,
            ) {
              scmTeamId?.let { RepoModel.scmTeam eq it } ?: Op.TRUE
            }
            .join(
                otherTable = BuildTriageModel,
                otherColumn = BuildTriageModel.build,
                onColumn = BuildModel.id,
                joinType = JoinType.INNER,
            )
            .select(
                // dimensions
                BuildModel.completedAt.date(),
                BuildModel.completedAt.hour(),
                // aggregates
                p50,
                p90,
                p99,
            )
            .groupBy(
                BuildModel.completedAt.date(),
                BuildModel.completedAt.hour(),
            )
            .whereAll(
                BuildModel.completedAt.isNotNull(),
                BuildTriageModel.isVisible eq true,
                ciInstallationId?.let { BuildModel.ciInstallation eq it },
                startDate?.let { BuildTriageModel.createdAt greater it },
                when (includeInsiders) {
                    true -> null
                    else -> BuildTriageModel.isInsider eq false
                },
            )
            .map {
                val date = it[BuildModel.completedAt.date()]
                val hour = it[BuildModel.completedAt.hour()]

                TriageLatencyMetrics(
                    date = date.atStartOfDayIn(timeZone = REPORTING_TIMEZONE).plus(hour.hours),
                    p50 = it[p50].coerceAtLeast(0.0).seconds,
                    p90 = it[p90].coerceAtLeast(0.0).seconds,
                    p99 = it[p99].coerceAtLeast(0.0).seconds,
                )
            }
    }
}
