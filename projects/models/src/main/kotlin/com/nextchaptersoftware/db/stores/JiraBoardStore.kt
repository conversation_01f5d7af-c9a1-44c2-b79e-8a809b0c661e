package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.JiraBoard
import com.nextchaptersoftware.db.models.JiraBoardDAO
import com.nextchaptersoftware.db.models.JiraBoardModel
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.toJiraBoard
import com.nextchaptersoftware.utils.TextUtils.findClosestMatch
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.upsertReturning

class JiraBoardStore internal constructor() {
    suspend fun list(
        installationId: InstallationId,
    ): List<JiraBoard> = suspendedTransaction {
        JiraBoardDAO
            .find { JiraBoardModel.installation eq installationId }
            .map { it.asDataModel() }
    }

    suspend fun upsert(
        installationId: InstallationId,
        jiraProjectId: JiraProjectId,
        boardId: String,
        name: String,
        type: String,
        isPrivate: Boolean,
    ): JiraBoard = suspendedTransaction {
        JiraBoardModel.upsertReturning(
            keys = arrayOf(JiraBoardModel.installation, JiraBoardModel.project, JiraBoardModel.boardId),
            onUpdateExclude = JiraBoardModel.columns - setOf(
                JiraBoardModel.name,
                JiraBoardModel.type,
                JiraBoardModel.isPrivate,
            ),
        ) {
            it[this.installation] = installationId
            it[this.project] = jiraProjectId
            it[this.boardId] = boardId
            it[this.name] = name
            it[this.type] = type
            it[this.isPrivate] = isPrivate
        }.first().toJiraBoard()
    }

    suspend fun getBoardByBoardId(
        installationId: InstallationId,
        boardId: String,
    ): JiraBoard? {
        val boardIdSanitized = boardId.trim().lowercase()

        return suspendedTransaction {
            JiraBoardDAO
                .find { (JiraBoardModel.installation eq installationId) and (JiraBoardModel.boardId eq boardIdSanitized) }
                .limit(1)
                .firstOrNull()
                ?.asDataModel()
        }
    }

    suspend fun getBoardByNameMatch(
        installationId: InstallationId,
        name: String,
    ): JiraBoard? {
        val nameSanitized = name.trim().lowercase().removeSuffix(" board")

        // Most/all boards end with "Board" in the name, so try with and without it
        // First try resolving by board name, then by project name
        return resolveBoardByNameMatch(installationId = installationId, name = nameSanitized.plus(" board"))
            ?: resolveBoardByNameMatch(installationId = installationId, name = nameSanitized)
            ?: Stores.jiraProjectStore.getJiraProjectIdByNameOrKeyMatch(installationId = installationId, name = nameSanitized)?.let {
                JiraBoardDAO.find { JiraBoardModel.project eq it }.limit(1).firstOrNull()?.asDataModel()
            }
    }

    private suspend fun resolveBoardByNameMatch(
        installationId: InstallationId,
        name: String,
    ): JiraBoard? {
        return suspendedTransaction {
            JiraBoardDAO
                .find { (JiraBoardModel.installation eq installationId) and (JiraBoardModel.name.lowerCase() eq name) }
                .limit(1)
                .firstOrNull()
                ?.asDataModel()
        } ?: run {
            // No exact match was found, so find the one with the closest name
            val allBoards = suspendedTransaction {
                JiraBoardDAO.find { JiraBoardModel.installation eq installationId }.map { it.asDataModel() }
            }

            name.findClosestMatch(strings = allBoards.map { it.name })
                ?.let { closestMatch -> allBoards.firstOrNull { it.name == closestMatch } }
        }
    }
}
