package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.JiraPermissionSchemeId
import com.nextchaptersoftware.db.models.JiraProject
import com.nextchaptersoftware.db.models.JiraProjectDAO
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.JiraProjectModel
import com.nextchaptersoftware.db.models.JiraSiteId
import com.nextchaptersoftware.db.models.JiraSiteModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.toJiraProject
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.utils.BooleanUtils.nullIfFalse
import com.nextchaptersoftware.utils.TextUtils.findClosestMatch
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.Join
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.count
import org.jetbrains.exposed.sql.lowerCase
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsertReturning

class JiraProjectStore internal constructor() {
    suspend fun findById(
        orgId: OrgId,
        id: JiraProjectId,
    ): JiraProject? = suspendedTransaction {
        JiraProjectModel
            .join(
                otherTable = JiraSiteModel,
                joinType = JoinType.INNER,
                otherColumn = JiraSiteModel.id,
                onColumn = JiraProjectModel.jiraSite,
            )
            .join(
                otherTable = InstallationModel,
                joinType = JoinType.INNER,
                otherColumn = InstallationModel.id,
                onColumn = JiraSiteModel.installation,
            ) {
                InstallationModel.org eq orgId
            }
            .select(JiraProjectModel.columns)
            .where { JiraProjectModel.id eq id }
            .limit(1)
            .map { JiraProjectDAO.wrapRow(it).asDataModel() }
            .firstOrNull()
    }

    suspend fun find(
        jiraSiteId: JiraSiteId,
        ids: List<JiraProjectId>,
    ): List<JiraProject> = list(
        jiraSiteId = jiraSiteId,
        additionalWhereClause = JiraProjectModel.id inList ids,
    )

    suspend fun list(
        jiraSiteId: JiraSiteId,
        additionalWhereClause: Op<Boolean>? = null,
        limit: Int? = null,
    ): List<JiraProject> = suspendedTransaction {
        JiraProjectDAO.find {
            AllOp(
                JiraProjectModel.jiraSite eq jiraSiteId,
                additionalWhereClause,
            )
        }.apply {
            limit?.let(::limit)
        }.map {
            it.asDataModel()
        }
    }

    suspend fun countShouldIngest(
        jiraSiteId: JiraSiteId,
    ): Int = suspendedTransaction {
        JiraProjectModel
            .select(JiraProjectModel.id.count())
            .where { (JiraProjectModel.jiraSite eq jiraSiteId) and (JiraProjectModel.shouldIngest eq true) }
            .first()[JiraProjectModel.id.count()].toInt()
    }

    suspend fun upsert(
        jiraSiteId: JiraSiteId,
        projectId: String,
        projectName: String,
        projectKey: String,
        isTeamManaged: Boolean,
    ): JiraProject = suspendedTransaction {
        JiraProjectModel.upsertReturning(
            keys = arrayOf(JiraProjectModel.jiraSite, JiraProjectModel.projectId),
            onUpdateExclude = JiraProjectModel.columns - setOf(
                JiraProjectModel.projectName,
                JiraProjectModel.projectKey,
                JiraProjectModel.isTeamManaged,
            ),
        ) {
            it[JiraProjectModel.jiraSite] = jiraSiteId
            it[JiraProjectModel.projectId] = projectId
            it[JiraProjectModel.projectName] = projectName
            it[JiraProjectModel.projectKey] = projectKey
            it[JiraProjectModel.isTeamManaged] = isTeamManaged.nullIfFalse()
        }.first().toJiraProject()
    }

    suspend fun updateShouldIngest(
        jiraProjectIds: List<JiraProjectId>,
        shouldIngest: Boolean,
    ): Unit = suspendedTransaction {
        JiraProjectModel.update({ JiraProjectModel.id inList jiraProjectIds }) {
            it[this.shouldIngest] = shouldIngest
        }
    }

    suspend fun updateLastSyncedPermissions(
        jiraProjectId: JiraProjectId,
        lastSyncedPermissions: Instant?,
    ): Unit = suspendedTransaction {
        JiraProjectModel.update({ JiraProjectModel.id eq jiraProjectId }) {
            it[this.lastSyncedPermissions] = lastSyncedPermissions
        }
    }

    suspend fun updatePermissionSchemeId(
        jiraProjectId: JiraProjectId,
        jiraPermissionSchemeId: JiraPermissionSchemeId?,
    ): Unit = suspendedTransaction {
        JiraProjectModel.update({ JiraProjectModel.id eq jiraProjectId }) {
            it[this.permissionScheme] = jiraPermissionSchemeId
        }
    }

    suspend fun delete(
        jiraSiteId: JiraSiteId,
        jiraProjectId: JiraProjectId,
    ) {
        suspendedTransaction {
            JiraProjectDAO
                .find { (JiraProjectModel.id eq jiraProjectId) and (JiraProjectModel.jiraSite eq jiraSiteId) }
                .firstOrNull()
                ?.delete()
        }
    }

    suspend fun getJiraProjectIdByNameOrKeyMatch(
        installationId: InstallationId,
        name: String,
    ): JiraProjectId? {
        val join = JiraProjectModel
            .join(
                otherTable = JiraSiteModel,
                joinType = JoinType.INNER,
                otherColumn = JiraSiteModel.id,
                onColumn = JiraProjectModel.jiraSite,
            ) {
                JiraSiteModel.installation eq installationId
            }

        return getJiraProjectIdByNameOrKeyMatch(join = join, name = name)
    }

    suspend fun getJiraProjectIdByNameOrKeyMatch(
        orgId: OrgId,
        name: String,
    ): JiraProjectId? {
        val join = JiraProjectModel
            .join(
                otherTable = JiraSiteModel,
                joinType = JoinType.INNER,
                otherColumn = JiraSiteModel.id,
                onColumn = JiraProjectModel.jiraSite,
            )
            .join(
                otherTable = InstallationModel,
                joinType = JoinType.INNER,
                otherColumn = InstallationModel.id,
                onColumn = JiraSiteModel.installation,
            ) {
                InstallationModel.org eq orgId
            }

        return getJiraProjectIdByNameOrKeyMatch(join = join, name = name)
    }

    private suspend fun getJiraProjectIdByNameOrKeyMatch(
        join: Join,
        name: String,
    ): JiraProjectId? {
        val nameSanitized = name.trim().lowercase()

        return suspendedTransaction {
            join.select(JiraProjectModel.id)
                .where { JiraProjectModel.projectName.lowerCase() eq nameSanitized }
                .limit(1)
                .map { it[JiraProjectModel.id].value }
                .firstOrNull()
        } ?: suspendedTransaction {
            join.select(JiraProjectModel.id)
                .where { JiraProjectModel.projectKey.lowerCase() eq nameSanitized }
                .limit(1)
                .map { it[JiraProjectModel.id].value }
                .firstOrNull()
        } ?: run {
            // No exact match was found, so find the one with the closest name
            val allProjects = suspendedTransaction {
                join.select(JiraProjectModel.id, JiraProjectModel.projectName)
                    .map { it[JiraProjectModel.id].value to it[JiraProjectModel.projectName] }
            }

            nameSanitized.findClosestMatch(strings = allProjects.map { it.second })
                ?.let { closestMatch -> allProjects.firstOrNull { it.second == closestMatch }?.first }
        }
    }
}
