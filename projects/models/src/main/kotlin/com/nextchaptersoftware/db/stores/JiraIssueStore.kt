package com.nextchaptersoftware.db.stores

import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.IssueStatus
import com.nextchaptersoftware.db.models.JiraIssue
import com.nextchaptersoftware.db.models.JiraIssueDAO
import com.nextchaptersoftware.db.models.JiraIssueId
import com.nextchaptersoftware.db.models.JiraIssueModel
import com.nextchaptersoftware.db.models.JiraProject
import com.nextchaptersoftware.db.models.JiraProjectId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.toJiraIssue
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.utils.BooleanUtils.nullIfFalse
import com.nextchaptersoftware.utils.StringUuidExtensions
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.greaterEq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.SqlExpressionBuilder.lessEq
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.selectAll
import org.jetbrains.exposed.sql.update
import org.jetbrains.exposed.sql.upsertReturning

class JiraIssueStore internal constructor() {
    companion object {
        fun generateJiraIssueId(
            installationId: InstallationId,
            projectId: String,
            issueId: String,
        ): JiraIssueId {
            return JiraIssueId(value = StringUuidExtensions.uuidFromString("$installationId/$projectId/$issueId"))
        }
    }

    suspend fun find(
        id: JiraIssueId,
    ): JiraIssue? {
        return suspendedTransaction {
            JiraIssueDAO.findById(id)?.asDataModel()
        }
    }

    suspend fun upsert(
        id: JiraIssueId,
        installationId: InstallationId,
        project: JiraProject,
        issueId: String,
        issueKey: String,
        issueStatus: IssueStatus,
        reporter: OrgMemberId?,
        assignee: OrgMemberId?,
        createdAt: Instant,
        updatedAt: Instant,
        resolvedAt: Instant?,
        isRestricted: Boolean,
        needsExtraction: Boolean,
        needsEmbedding: Boolean,
    ): JiraIssue {
        val expectedId = generateJiraIssueId(installationId = installationId, projectId = project.projectId, issueId = issueId)
        require(id == expectedId) { "Expected JiraIssueId to be $expectedId but was $id" }

        return suspendedTransaction {
            JiraIssueModel.upsertReturning(
                keys = arrayOf(JiraIssueModel.installation, JiraIssueModel.project, JiraIssueModel.externalIssueId),
                onUpdateExclude = JiraIssueModel.columns - setOf(
                    JiraIssueModel.issueKey,
                    JiraIssueModel.issueStatus,
                    JiraIssueModel.reporter,
                    JiraIssueModel.assignee,
                    JiraIssueModel.updatedAt,
                    JiraIssueModel.resolvedAt,
                    JiraIssueModel.isRestricted,
                    JiraIssueModel.needsExtraction,
                    JiraIssueModel.needsEmbedding,
                ),
            ) { stmt ->
                stmt[this.id] = id
                stmt[this.createdAt] = createdAt
                stmt[this.installation] = installationId
                stmt[this.project] = project.id
                stmt[this.externalIssueId] = issueId
                stmt[this.issueKey] = issueKey
                stmt[this.issueStatus] = issueStatus
                stmt[this.reporter] = reporter
                stmt[this.assignee] = assignee
                stmt[this.updatedAt] = updatedAt
                stmt[this.resolvedAt] = resolvedAt
                stmt[this.isRestricted] = isRestricted.nullIfFalse()
                stmt[this.needsExtraction] = needsExtraction.nullIfFalse()
                stmt[this.needsEmbedding] = needsEmbedding.nullIfFalse()
            }.first().toJiraIssue()
        }
    }

    suspend fun completeExtraction(id: JiraIssueId) {
        suspendedTransaction {
            JiraIssueModel.update(
                {
                    AllOp(
                        JiraIssueModel.id eq id,
                        JiraIssueModel.needsExtraction eq true,
                    )
                },
            ) {
                it[this.needsExtraction] = null
                it[this.needsEmbedding] = true
            }
        }
    }

    suspend fun completeEmbedding(id: JiraIssueId) {
        suspendedTransaction {
            JiraIssueModel.update(
                {
                    AllOp(
                        JiraIssueModel.id eq id,
                        JiraIssueModel.needsEmbedding eq true,
                    )
                },
            ) {
                it[this.needsEmbedding] = null
            }
        }
    }

    suspend fun getInstallationsForExtraction(
        limit: Int = 50,
    ): List<Pair<OrgId, InstallationId>> {
        return suspendedTransaction {
            JiraIssueModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = JiraIssueModel.installation,
                )
                .select(InstallationModel.org, InstallationModel.id)
                .where { JiraIssueModel.needsExtraction eq true }
                .withDistinct(true)
                .limit(limit)
                .map { it[InstallationModel.org].value to it[InstallationModel.id].value }
        }
    }

    suspend fun getInstallationIssuesForExtraction(
        installationId: InstallationId,
        limit: Int = 400,
    ): List<JiraIssue> {
        return suspendedTransaction {
            JiraIssueModel
                .select(JiraIssueModel.columns)
                .whereAll(
                    JiraIssueModel.installation eq installationId,
                    JiraIssueModel.needsExtraction eq true,
                )
                .limit(limit)
                .orderBy(JiraIssueModel.createdAt)
                .map { JiraIssueDAO.wrapRow(it).asDataModel() }
        }
    }

    suspend fun getNextIssuesForEmbedding(
        installationId: InstallationId? = null,
        limit: Int = 1000,
    ): Map<OrgId, List<JiraIssue>> {
        return getNextIssues(
            installationId = installationId,
            clause = (JiraIssueModel.needsExtraction eq null) and (JiraIssueModel.needsEmbedding eq true),
            limit = limit,
        )
    }

    private suspend fun getNextIssues(
        installationId: InstallationId? = null,
        clause: Op<Boolean>,
        limit: Int,
    ): Map<OrgId, List<JiraIssue>> {
        return suspendedTransaction {
            JiraIssueModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = JiraIssueModel.installation,
                )
                .select(JiraIssueModel.columns + InstallationModel.org)
                .whereAll(
                    installationId?.let { JiraIssueModel.installation eq installationId },
                    clause,
                )
                .limit(limit)
                .orderBy(JiraIssueModel.createdAt)
                .groupBy({ it[InstallationModel.org].value }) { JiraIssueDAO.wrapRow(it).asDataModel() }
        }
    }

    suspend fun listByIssueIds(
        installationId: InstallationId,
        issueIds: List<String>,
    ): List<JiraIssue> {
        if (issueIds.isEmpty()) {
            return emptyList()
        }

        return suspendedTransaction {
            JiraIssueModel
                .selectAll()
                .whereAll(
                    JiraIssueModel.installation eq installationId,
                    JiraIssueModel.externalIssueId inList issueIds.distinct(),
                )
                .map { it.toJiraIssue() }
        }
    }

    suspend fun listIsRestricted(
        orgId: OrgId,
        ids: Set<JiraIssueId>,
    ): List<JiraIssueId> = suspendedTransaction {
        JiraIssueModel
            .join(
                joinType = JoinType.INNER,
                otherTable = InstallationModel,
                otherColumn = InstallationModel.id,
                onColumn = JiraIssueModel.installation,
            )
            .select(JiraIssueModel.id)
            .whereAll(
                JiraIssueModel.id inList ids,
                JiraIssueModel.isRestricted eq true,
                InstallationModel.org eq orgId,
            )
            .map { it[JiraIssueModel.id].value }
    }

    suspend fun findById(
        id: JiraIssueId,
    ): JiraIssue? = suspendedTransaction {
        JiraIssueDAO.findById(id)?.asDataModel()
    }

    suspend fun findByExternalIssueId(
        installationId: InstallationId,
        externalIssueId: String,
    ): JiraIssue? = suspendedTransaction {
        JiraIssueDAO
            .find { (JiraIssueModel.installation eq installationId) and (JiraIssueModel.externalIssueId eq externalIssueId) }
            .limit(1)
            .firstOrNull()
            ?.asDataModel()
    }

    suspend fun findJiraIssueIds(
        installationId: InstallationId,
        ids: List<JiraIssueId>,
    ): Map<String, JiraIssueId> = suspendedTransaction {
        JiraIssueModel
            .select(JiraIssueModel.id, JiraIssueModel.externalIssueId)
            .whereAll(
                JiraIssueModel.id inList ids,
                JiraIssueModel.installation eq installationId,
            )
            .mapNotNull { row -> row[JiraIssueModel.externalIssueId] to row[JiraIssueModel.id].value }
            .toMap()
    }

    suspend fun findByKey(orgId: OrgId, issueKey: String): JiraIssue? {
        return suspendedTransaction {
            JiraIssueModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = JiraIssueModel.installation,
                )
                .select(JiraIssueModel.columns)
                .whereAll(
                    InstallationModel.org eq orgId,
                    JiraIssueModel.issueKey eq issueKey,
                )
                .map { it.toJiraIssue() }
                .firstOrNull()
        }
    }

    suspend fun findJiraIssuesWithFilters(
        orgId: OrgId,
        projectId: JiraProjectId?,
        issueStatus: IssueStatus?,
        assigneeId: OrgMemberId?,
        creatorId: OrgMemberId?,
        createdAfter: Instant?,
        createdBefore: Instant?,
        updatedAfter: Instant?,
        updatedBefore: Instant?,
        resolvedAfter: Instant?,
        resolvedBefore: Instant?,
    ): List<JiraIssue> {
        return suspendedTransaction {
            JiraIssueModel
                .join(
                    joinType = JoinType.INNER,
                    otherTable = InstallationModel,
                    otherColumn = InstallationModel.id,
                    onColumn = JiraIssueModel.installation,
                )
                .select(JiraIssueModel.columns)
                .whereAll(
                    InstallationModel.org eq orgId,
                    projectId?.let { JiraIssueModel.project eq it },
                    issueStatus?.let { JiraIssueModel.issueStatus eq it },
                    assigneeId?.let { JiraIssueModel.assignee eq it },
                    creatorId?.let { JiraIssueModel.reporter eq it },
                    createdAfter?.let { JiraIssueModel.createdAt greaterEq it },
                    createdBefore?.let { JiraIssueModel.createdAt lessEq it },
                    updatedAfter?.let { JiraIssueModel.updatedAt greaterEq it },
                    updatedBefore?.let { JiraIssueModel.updatedAt lessEq it },
                    resolvedAfter?.let { JiraIssueModel.resolvedAt greaterEq it },
                    resolvedBefore?.let { JiraIssueModel.resolvedAt lessEq it },
                )
                .map { it.toJiraIssue() }
        }
    }
}
