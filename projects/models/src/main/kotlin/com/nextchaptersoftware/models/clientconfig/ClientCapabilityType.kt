package com.nextchaptersoftware.models.clientconfig

import com.nextchaptersoftware.db.common.DbOrdinal

/**
 * Client boolean config capabilities.
 *
 * Use to remotely control features and capabilities for the authorized entity.
 *
 * Deprecated configs can no longer be viewed or changed in admin web.
 *
 * @see ClientQuantityType
 */
enum class ClientCapabilityType(
    override val dbOrdinal: Int,
    val description: String,
    val isDeprecated: Boolean = false,
) : DbOrdinal {
    FeatureSlackIntegration(
        dbOrdinal = 1,
        description = "Dashboard shows Slack integration settings",
        isDeprecated = true,
    ),
    FeatureNewOnboardingUI(
        dbOrdinal = 2,
        description = "New onboarding UI in VSCode, November 2022",
        isDeprecated = true,
    ),
    FeatureVideoWalkthrough(
        dbOrdinal = 3,
        description = "Video walkthrough feature",
        isDeprecated = true,
    ),
    FeatureNewExplorerInsightsUI(
        dbOrdinal = 4,
        description = "Update explorer insights UI in VSCode https://github.com/NextChapterSoftware/unblocked/pull/3533",
        isDeprecated = true,
    ),
    FeatureDashboardInsightsUI(
        dbOrdinal = 5,
        description = "Dashboard show insights and topics",
        isDeprecated = true,
    ),
    DebugSourcemarkEngine(
        dbOrdinal = 6,
        description = "Sourcemark engine logs verbosely",
    ),
    DebugClient(
        dbOrdinal = 7,
        description = "Clients log verbosely",
    ),
    UseVSCodeGitProvider(
        dbOrdinal = 8,
        description = "VSCode uses VSCodeGitProvider, instead of using the CustomGitProvider",
        isDeprecated = true,
    ),
    InstallJetbrainsPlugin(
        dbOrdinal = 9,
        description = "Hub installs the JetBrains plugin",
        isDeprecated = true,
    ),
    DownloadVSCodePlugin(
        dbOrdinal = 10,
        description = "Hub downloads the VSCode plugin, instead of unpacking it from the installer",
        isDeprecated = true,
    ),
    FeatureTopicDescriptions(
        dbOrdinal = 11,
        description = "Dashboard shows topic descriptions and expert summaries",
        isDeprecated = true,
    ),
    FeatureScmSettings(
        dbOrdinal = 12,
        description = "Dashboard shows SCM settings",
        isDeprecated = true,
    ),
    FeatureJiraSettings(
        dbOrdinal = 13,
        description = "Dashboard shows Jira settings",
        isDeprecated = true,
    ),
    SemanticSearch(
        dbOrdinal = 14,
        description = "Semantic search feature; shows semantic search UI in Hub and adds the Unblocked bot to the team",
        isDeprecated = true,
    ),
    IDEExplorerUI(
        dbOrdinal = 15,
        description = "Update IDE explorer UI, for July 2023 release",
        isDeprecated = true,
    ),
    HubSearch(
        dbOrdinal = 16,
        description = "Hub search feature; integration of PG and semantic search in the same UI",
        isDeprecated = true,
    ),
    HubPRTitle(
        dbOrdinal = 17,
        description = "Hub AB Test; Set PR title as title of Inbox cards",
        isDeprecated = true,
    ),
    OnboardingV2(
        dbOrdinal = 18,
        description = "Updated Onboarding flow",
        isDeprecated = true,
    ),
    HybridSourcemarkEngine(
        dbOrdinal = 19,
        description = "Hybrid cloud/local sourcemark algorithm, " +
                "where the cloud-based engine precomputes file paths for Git revisions, " +
                "and the local engine downloads and recalculates sourcemarks for opened files only.",
        isDeprecated = true,
    ),
    SemanticSearchRepoContext(
        dbOrdinal = 20,
        description = "Use repo context when creating QA threads from IDE clients.",
        isDeprecated = true,
    ),
    FeatureGoogleSettings(
        dbOrdinal = 21,
        description = "Dashboard shows Google integration/installation",
        isDeprecated = true,
    ),
    InterpolatedMessageRendering(
        dbOrdinal = 22,
        description = "Interpolated message rendering",
        isDeprecated = true,
    ),
    FeatureNewInbox(
        dbOrdinal = 23,
        description = "New inbox UI as well as My Questions",
        isDeprecated = true,
    ),
    FeatureNewIDESidebar(
        dbOrdinal = 25,
        description = "New IDE Sidebar UI",
        isDeprecated = true,
    ),
    TryUnblocked(
        dbOrdinal = 26,
        description = "Try Unblocked with a demo team during onboarding",
        isDeprecated = true,
    ),
    FeatureSemanticSearchFile(
        dbOrdinal = 27,
        description = "Semantic search against a file in the IDE to retrieve references from various providers",
        isDeprecated = true,
    ),
    SlackSettingsChannelPatterns(
        dbOrdinal = 28,
        description = "Slack channel patterns in web dashboard settings alongside full channel names",
    ),
    FeatureUnblockedAPI(
        dbOrdinal = 29,
        description = "Dashboard shows Unblocked API settings",
        isDeprecated = true,
    ),
    MinimalDocumentDecorator(
        dbOrdinal = 30,
        description = "Reduces the number of DB calls decorating documents post Pinecone retrieval",
        isDeprecated = true,
    ),
    FeatureRoleBasedAccessControl(
        dbOrdinal = 31,
        description = "Exposes role-based access control settings in the web dashboard",
        isDeprecated = true,
    ),
    FeatureConfluenceDataCenter(
        dbOrdinal = 32,
        description = "Confluence Data Center integration in web dashboard",
        isDeprecated = true,
    ),
    FeatureJiraDataCenter(
        dbOrdinal = 33,
        description = "Jira Data Center integration in web dashboard",
        isDeprecated = true,
    ),
    FeatureDataSourceAccessControl(
        dbOrdinal = 34,
        description = "Honour user-specific data source access control",
        isDeprecated = true,
    ),
    FeatureReferenceObsoletion(
        dbOrdinal = 35,
        description = "Ability to obsolete references, and list obsolete references in Recently Deleted",
        isDeprecated = true,
    ),
    FeatureDesktopSCMSupport(
        dbOrdinal = 36,
        description = "SCM threads, notifications, unreads in desktop app",
    ),
    FeatureDesktopAppCutover(
        dbOrdinal = 37,
        description = "Automatic cutover from hub app to desktop app",
        isDeprecated = true,
    ),
    FeatureMultiScm(
        dbOrdinal = 38,
        description = "Users can connect multiple SCMs to their org",
        isDeprecated = true,
    ),
    FeatureSlackAutoResponse(
        dbOrdinal = 39,
        description = "Slack auto response feature",
        isDeprecated = true,
    ),
    FeatureInProductPricing(
        dbOrdinal = 40,
        description = "In-product pricing UI across clients",
        isDeprecated = true,
    ),
    FeatureSaml(
        dbOrdinal = 41,
        description = "SAML settings and sign in flow",
        isDeprecated = true,
    ),
    FeatureNewLoadingState(
        dbOrdinal = 42,
        description = "Client usage of new loading state and text",
        isDeprecated = true,
    ),
    FeatureSlackPrivateScopes(
        dbOrdinal = 43,
        description = "Client UI related to slack private scopes",
        isDeprecated = true,
    ),
    FeatureOnboardingV5(
        dbOrdinal = 44,
        description = "Onboarding V5",
        isDeprecated = true,
    ),
    FeatureScim(
        dbOrdinal = 45,
        description = "SCIM provisioning settings in web dashboard",
        isDeprecated = true,
    ),
    FeatureAnswerPreferences(
        dbOrdinal = 46,
        description = "User answer preferences settings",
        isDeprecated = true,
    ),
    FeatureNewSlackConfigUI(
        dbOrdinal = 47,
        description = "New Slack Configuration UI",
        isDeprecated = true,
    ),
    FeatureFeedbackAnalytics(
        dbOrdinal = 48,
        description = "Show feedback in analytics UI",
        isDeprecated = true,
    ),
    FeatureAnalytics(
        dbOrdinal = 49,
        description = "Analytics UI in web dashboard",
        isDeprecated = true,
    ),
    FeatureShowNonQADashboard(
        dbOrdinal = 50,
        description = "Show Non QA items in dashboard discussion lists",
    ),
    SlackHideAutoResponse(
        dbOrdinal = 51,
        description = "Hide the display of auto-answer in clients. Should only be used for Slack App Review organizations.",
        isDeprecated = true,
    ),
    TeamMemberTableProviderColumn(
        dbOrdinal = 52,
        description = "Show the provider column on the Team Members Table",
        isDeprecated = true,
    ),
    FeatureLoginWithSlack(
        dbOrdinal = 53,
        description = "Login with Slack feature",
        isDeprecated = true,
    ),
    FeatureAnnotateMermaidDiagramsForSlack(
        dbOrdinal = 54,
        description = "Annotate mermaid diagrams generated by Unblocked with links to images for outgoing Slack messages",
        isDeprecated = true,
    ),
    FeatureConfigureCI(
        dbOrdinal = 55,
        description = "Show CI data sources and settings configurations.",
    ),
    FeatureIDECodeGen(
        dbOrdinal = 56,
        description = "Show code generation UI in IDEs.",
    ),
    FeatureNewSlackConfigUI2(
        dbOrdinal = 57,
        description = "New Slack Configuration UI (Feb 2025), adding row checkbox selection to match the SCM repo table",
        isDeprecated = true,
    ),
    FeatureDataPresets(
        dbOrdinal = 58,
        description = "Show Data presets UI in clients",
        isDeprecated = true,
    ),
    FeatureCircleCI(
        dbOrdinal = 59,
        description = "Circle CI integration",
        isDeprecated = true,
    ),
    FeatureDataShieldPerDataSource(
        dbOrdinal = 61,
        description = "Data shield can be configured per data source",
        isDeprecated = true,
    ),
    FeatureGoogleDriveWorkspace(
        dbOrdinal = 62,
        description = "Google Drive via Google Workspace.",
    ),
    FeatureBuildkite(
        dbOrdinal = 63,
        description = "Buildkite CI",
        isDeprecated = true,
    ),
    SlackUserAcceptanceTesting(
        dbOrdinal = 64,
        description = "Slack user acceptance testing",
    ),
    FeatureAsana(
        dbOrdinal = 65,
        description = "Asana integration",
    ),
    FeatureMcp(
        dbOrdinal = 66,
        description = "Allow installation and usage of MCP servers",
    ),
    FeatureCIAnalytics(
        dbOrdinal = 67,
        description = "Show CI related charts on the web Analytics page",
        isDeprecated = true,
    ),
    FeatureAnswersTutorial(
        dbOrdinal = 68,
        description = "Show Answers Tutorial",
    ),
    FeatureCIPricing(
        dbOrdinal = 69,
        description = "Enable CI Pricing",
    ),
    ;

    companion object {
        fun allActiveConfigs(): List<ClientCapabilityType> {
            return entries.filterNot { it.isDeprecated }
        }
    }
}
