package com.nextchaptersoftware.webhookservice

import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.atlassian.forge.AtlassianForgeAppTokenService
import com.nextchaptersoftware.atlassian.forge.AtlassianForgeInvocationTokenService
import com.nextchaptersoftware.ci.enqueue.CiWebhookEventEnqueueService
import com.nextchaptersoftware.coda.events.queue.enqueue.CodaEventEnqueueService
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.crypto.Decryptions
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.integration.queue.redis.cache.NoopIngestionProgressServiceProvider
import com.nextchaptersoftware.jira.queue.enqueue.JiraEventEnqueueService
import com.nextchaptersoftware.linear.events.queue.enqueue.LinearEventEnqueueService
import com.nextchaptersoftware.notion.events.queue.enqueue.NotionEventEnqueueService
import com.nextchaptersoftware.ratelimit.services.TokenBucketRateLimiter
import com.nextchaptersoftware.redis.health.RedisHealthChecker
import com.nextchaptersoftware.scm.queue.enqueue.ScmWebhookEventEnqueueService
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureCors
import com.nextchaptersoftware.service.plugins.configureDefaultHeaders
import com.nextchaptersoftware.service.plugins.configureForwardProxySupport
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.service.plugins.configureStatusPages
import com.nextchaptersoftware.slack.event.SlackEventPrioritizer
import com.nextchaptersoftware.slack.event.SlackEventRateLimiter
import com.nextchaptersoftware.slack.event.SlackEventTypeExtractor
import com.nextchaptersoftware.slack.security.SlackEventVerifier
import com.nextchaptersoftware.slack.webhook.queue.enqueue.SlackWebhookEventEnqueueService
import com.nextchaptersoftware.slack.webhook.services.SlackWebhookEventRequestService
import com.nextchaptersoftware.stripe.events.queue.enqueue.StripeEventEnqueueService
import com.nextchaptersoftware.stripe.webhook.services.StripeWebhookEventService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.webhookservice.plugins.configureRouting
import com.nextchaptersoftware.webhookservice.plugins.configureTracing
import io.ktor.server.application.Application

@Suppress("LongMethod")
fun Application.module(
    config: GlobalConfig = GlobalConfig.INSTANCE,
    serviceLifecycle: ServiceLifecycle = ServiceLifecycle(healthCheckers = listOf(RedisHealthChecker())),
) {
    val userSecretService by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = Decryptions.NULL,
        )
    }

    val slackEventVerifier by lazy {
        SlackEventVerifier(
            configs = listOfNotNull(config.providers.slack, config.providers.slackUAT),
        )
    }

    val slackEventTypeExtractor by lazy {
        SlackEventTypeExtractor()
    }

    val slackEventPrioritizer by lazy {
        SlackEventPrioritizer(
            slackEventTypeExtractor = slackEventTypeExtractor,
        )
    }

    val slackRateLimiter by lazy {
        TokenBucketRateLimiter(
            keyPrefix = "slack-rate-limiter",
            maxTokens = 150,
            refillRatePerSecond = 1.5,
        )
    }

    val slackEventRateLimiter by lazy {
        SlackEventRateLimiter(
            slackEventTypeExtractor = slackEventTypeExtractor,
            slackRateLimiter = slackRateLimiter,
        )
    }

    val slackWebhookEventEnqueueService by lazy {
        SlackWebhookEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.hooksSlackQueueName,
                ),
            ),
        )
    }

    val slackWebhookEventRequestService by lazy {
        SlackWebhookEventRequestService(
            slackWebhookEventEnqueueService = slackWebhookEventEnqueueService,
            slackEventVerifier = slackEventVerifier,
            slackEventPrioritizer = slackEventPrioritizer,
            slackEventRateLimiter = slackEventRateLimiter,
        )
    }

    val ciWebhookEventEnqueueService by lazy {
        CiWebhookEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.hooksCiQueueName,
                ),
            ),
        )
    }
    val scmWebhookEventEnqueueService by lazy {
        ScmWebhookEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.hooksScmQueueName,
                ),
            ),
        )
    }

    val stripeEventEnqueueService by lazy {
        StripeEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.hooksStripeQueueName,
                ),
            ),
        )
    }

    val stripeWebhookEventService by lazy {
        StripeWebhookEventService(
            stripeEventEnqueueService = stripeEventEnqueueService,
        )
    }

    val transcriptionEventWebhookEnqueueService by lazy {
        StandardEventEnqueueService(
            messageProducer = ActiveMQProducer.producer(
                queueName = config.queue.hooksTranscriptionQueueName,
            ),
        )
    }

    val linearEventEnqueueService by lazy {
        LinearEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.linearEventsQueueName,
                ),
            ),
        )
    }

    val codaEventEnqueueService by lazy {
        CodaEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.codaEventsQueueName,
                ),
            ),
        )
    }

    val notionEventEnqueueService by lazy {
        NotionEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notionEventsQueueName,
                ),
            ),
            progressServiceProvider = NoopIngestionProgressServiceProvider(),
        )
    }

    val jiraEventEnqueueService by lazy {
        JiraEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.jiraEventsQueueName,
                ),
            ),
        )
    }

    val atlassianForgeAppTokenService by lazy {
        AtlassianForgeAppTokenService(
            userSecretService = userSecretService,
        )
    }

    val atlassianForgeInvocationTokenService by lazy {
        AtlassianForgeInvocationTokenService(
            appId = config.providers.jira?.forgeAppId ?: return@lazy null,
            jwksUrl = config.atlassianForgeConfig.jwksUrl,
        )
    }

    configureRouting(
        serviceLifecycle = serviceLifecycle,
        ciWebhookEventEnqueueService = ciWebhookEventEnqueueService,
        slackWebhookEventRequestService = slackWebhookEventRequestService,
        scmWebhookEventEnqueueService = scmWebhookEventEnqueueService,
        stripeWebhookEventService = stripeWebhookEventService,
        transcriptionEventWebhookEnqueueService = transcriptionEventWebhookEnqueueService,
        linearEventEnqueueService = linearEventEnqueueService,
        codaEventEnqueueService = codaEventEnqueueService,
        notionEventEnqueueService = notionEventEnqueueService,
        jiraEventEnqueueService = jiraEventEnqueueService,
        atlassianForgeAppTokenService = atlassianForgeAppTokenService,
        atlassianForgeInvocationTokenService = atlassianForgeInvocationTokenService,
    )
    configureDefaultHeaders()
    configureForwardProxySupport()
    configureCors(config.cors)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureStatusPages()
    configureTracing()
    configureJvmMetrics()
}
