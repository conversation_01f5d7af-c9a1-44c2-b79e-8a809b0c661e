package com.nextchaptersoftware.ciservice.handlers.gitlab

import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.scm.gitlab.models.GitLabJobEvent

object GitLabJobEventHandler : TypedEventHandler<GitLabJobEvent> {
    override suspend fun handle(
        event: GitLabJobEvent,
    ): Boolean {
        // TODO: remove once queue is drained
        return true
    }
}
