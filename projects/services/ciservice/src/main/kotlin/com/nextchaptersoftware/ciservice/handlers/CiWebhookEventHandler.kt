package com.nextchaptersoftware.ciservice.handlers

import com.nextchaptersoftware.ci.payloads.CiEvent
import com.nextchaptersoftware.ci.payloads.CiWebhookEvent
import com.nextchaptersoftware.ciservice.handlers.bitbucket.BitbucketBuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteBuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiWorkflowEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckRunEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckSuiteEventHandler
import com.nextchaptersoftware.ciservice.handlers.gitlab.GitLabJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.gitlab.GitLabPipelineEventHandler
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler

/**
 * Handles CI specific webhook payload ingestion
 *
 * ScmTeam and Repo validation happen at this stage
 *
 * @see com.nextchaptersoftware.ci.CITriageController
 */
class CiWebhookEventHandler(
    private val bitbucketBuildEventHandler: BitbucketBuildEventHandler,
    private val buildkiteBuildEventHandler: BuildkiteBuildEventHandler,
    private val buildkiteJobEventHandler: BuildkiteJobEventHandler,
    private val circleCiJobEventHandler: CircleCiJobEventHandler,
    private val circleCiWorkflowEventHandler: CircleCiWorkflowEventHandler,
    private val gitHubCheckRunEventHandler: GitHubCheckRunEventHandler,
    private val gitHubCheckSuiteEventHandler: GitHubCheckSuiteEventHandler,
    private val gitLabJobEventHandler: GitLabJobEventHandler,
    private val gitLabPipelineEventHandler: GitLabPipelineEventHandler,
) : TypedEventHandler<CiEvent.WebhookEvent> {

    override suspend fun handle(
        event: CiEvent.WebhookEvent,
    ): Boolean {
        when (val ciWebhook = event as CiWebhookEvent) {
            is CiWebhookEvent.WebhookBitbucketBuildStatusEvent -> bitbucketBuildEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookBuildkiteBuildEvent -> buildkiteBuildEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookBuildkiteJobEvent -> buildkiteJobEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookCircleCiJobEvent -> circleCiJobEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookCircleCiWorkflowEvent -> circleCiWorkflowEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookGitHubCheckRunEvent -> gitHubCheckRunEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookGitHubCheckSuiteEvent -> gitHubCheckSuiteEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookGitLabJobEvent -> gitLabJobEventHandler.handle(ciWebhook.event)
            is CiWebhookEvent.WebhookGitLabPipelineEvent -> gitLabPipelineEventHandler.handle(ciWebhook.event)
        }
        return true
    }
}
