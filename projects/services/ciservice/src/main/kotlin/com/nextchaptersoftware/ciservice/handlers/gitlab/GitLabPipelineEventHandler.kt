package com.nextchaptersoftware.ciservice.handlers.gitlab

import com.nextchaptersoftware.ci.CIProjectContext
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.gitlab.models.asCiBuild
import com.nextchaptersoftware.ci.gitlab.models.asCiJob
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestState
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.db.stores.Stores.pullRequestStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.event.queue.handlers.TypedEventHandler
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.log.kotlin.traceAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.scm.gitlab.GitLabPipelinesClientProvider
import com.nextchaptersoftware.scm.gitlab.models.GitLabPipelineEvent
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList

private val LOGGER = mu.KotlinLogging.logger {}

class GitLabPipelineEventHandler(
    private val buildIngestionService: BuildIngestionService,
    private val ciTriageController: CITriageController,
    private val gitLabPipelinesClientProvider: GitLabPipelinesClientProvider,
) : TypedEventHandler<GitLabPipelineEvent> {

    override suspend fun handle(
        event: GitLabPipelineEvent,
    ): Boolean = withLoggingContextAsync(
        "gitlab.project.id" to event.project.id,
        "gitlab.project.namespace" to event.project.pathWithNamespace.substringBefore('/'),
        "gitlab.project.path" to event.project.pathWithNamespace,
        "gitlab.pipeline.id" to event.pipeline.id,
    ) {
        doHandle(event)
        true
    }

    private suspend fun doHandle(
        event: GitLabPipelineEvent,
    ) {
        val repo = repoStore.findByUrl(
            orgIds = null,
            repoUrl = event.project.webUrl.let(RepoUrl::parseOrThrow),
        ) ?: run {
            LOGGER.traceAsync { "Repo not found" }
            return
        }

        val scmTeam = scmTeamStore.findById(teamId = repo.teamId) ?: run {
            LOGGER.traceAsync { "SCM team not found" }
            return
        }

        val ciInstallation = installationStore.findByProvider(
            orgId = scmTeam.orgId,
            provider = Provider.GitLab,
        ).firstOrNull() ?: run {
            LOGGER.traceAsync { "CI installation not found" }
            return
        }

        val pullRequest = pullRequestStore.findByHead(
            repoId = repo.id,
            headBranch = event.pipeline.ref,
            headSha = event.pipeline.sha,
        )
            ?.takeIf { it.state == PullRequestState.Open }
            ?: run {
                LOGGER.traceAsync { "PR is missing or stale" }
                return
            }

        if (!ciTriageController.ciEnabled(
                orgId = scmTeam.orgId,
                repo = repo,
                orgMemberId = pullRequest.creatorOrgMemberId,
                ciInstallationId = ciInstallation.id,
                scmInstallationId = scmTeam.installationId,
            )
        ) {
            LOGGER.traceAsync { "Not enabled" }
            return
        }

        val gitLabPipelinesClient = gitLabPipelinesClientProvider.create(
            scmTeam = scmTeam,
            repo = repo,
        )

        val pipeline = gitLabPipelinesClient.pipeline(
            pipelineId = event.pipeline.id,
        )

        val jobs = gitLabPipelinesClient.pipelineJobs(
            pipelineId = event.pipeline.id,
        )

        buildIngestionService.ingestBuild(
            ciInstallation = ciInstallation,
            projectContext = CIProjectContext.GitLabPipelines(
                externalInstallationId = scmTeam.providerExternalId,
                externalRepositoryId = repo.externalId,
            ),
            scmTeamId = scmTeam.id,
            repoId = repo.id,
            pullRequest = pullRequest,
            ciBuild = pipeline.asCiBuild(),
            ciJobs = jobs.map { it.asCiJob() }.toList(),
        )
    }
}
