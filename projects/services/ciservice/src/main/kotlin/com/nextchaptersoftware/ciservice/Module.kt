package com.nextchaptersoftware.ciservice

import com.aallam.openai.client.OpenAIHost
import com.nextchaptersoftware.activemq.ActiveMQConsumer
import com.nextchaptersoftware.activemq.ActiveMQProducer
import com.nextchaptersoftware.anthropic.api.AnthropicApiConfiguration
import com.nextchaptersoftware.anthropic.api.AnthropicApiProvider
import com.nextchaptersoftware.atlassian.api.AtlassianAuthApiImpl
import com.nextchaptersoftware.atlassian.api.NoopAtlassianAuthApi
import com.nextchaptersoftware.atlassian.services.AtlassianTokenProvider
import com.nextchaptersoftware.auth.ci.CIJwtSigner
import com.nextchaptersoftware.auth.oauth.NoopOAuthTokenRefresher
import com.nextchaptersoftware.auth.secret.oauth.EncryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UnencryptedTokenPersistence
import com.nextchaptersoftware.auth.secret.oauth.UserSecretOAuthRefreshService
import com.nextchaptersoftware.aws.bedrock.anthropic.api.BedrockAnthropicCompletionsApi
import com.nextchaptersoftware.aws.bedrock.cohere.api.BedrockCohereRerankApi
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.BedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeAsyncProviderFactory
import com.nextchaptersoftware.aws.bedrockruntime.StandardBedrockRuntimeProviderFactory
import com.nextchaptersoftware.aws.client.AWSClientProvider
import com.nextchaptersoftware.aws.extensions.StringExtensions.toRegion
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.billing.utils.OrgBillingSeatService
import com.nextchaptersoftware.bot.services.InstallationBotAccountService
import com.nextchaptersoftware.ci.CIProjectApiFactory
import com.nextchaptersoftware.ci.CITriageController
import com.nextchaptersoftware.ci.CIUserApiFactory
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.ci.config.CISecretsConfig
import com.nextchaptersoftware.ci.enqueue.BuildEventEnqueueService
import com.nextchaptersoftware.ci.enqueue.CiBuildEventEnqueueService
import com.nextchaptersoftware.ci.enqueue.TriageEventEnqueueService
import com.nextchaptersoftware.ci.events.CIProjectEventMessageProcessor
import com.nextchaptersoftware.ci.logging.LogFocusService
import com.nextchaptersoftware.ci.logging.LogSummaryService
import com.nextchaptersoftware.ci.service.BuildIngestionService
import com.nextchaptersoftware.ci.services.CIProjectControlService
import com.nextchaptersoftware.ci.triage.BuildTriageDataRetrievalService
import com.nextchaptersoftware.ci.triage.BuildTriageDiffCompressionService
import com.nextchaptersoftware.ci.triage.BuildTriageEvalService
import com.nextchaptersoftware.ci.triage.BuildTriageFixSuggestionService
import com.nextchaptersoftware.ci.triage.BuildTriageInputMatcherService
import com.nextchaptersoftware.ci.triage.BuildTriageJobCompressionService
import com.nextchaptersoftware.ci.triage.BuildTriageLogSummaryService
import com.nextchaptersoftware.ci.triage.BuildTriageQueryExecutor
import com.nextchaptersoftware.ci.triage.BuildTriageService
import com.nextchaptersoftware.ciservice.alarms.TriageLatencyAlarm
import com.nextchaptersoftware.ciservice.handlers.CiEventHandler
import com.nextchaptersoftware.ciservice.handlers.CiTriageEventHandler
import com.nextchaptersoftware.ciservice.handlers.CiWebhookEventHandler
import com.nextchaptersoftware.ciservice.handlers.CiWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.bitbucket.BitbucketBuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.bitbucket.BitbucketWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteBuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.buildkite.BuildkiteWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.builds.BuildTriageTaskHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.circleci.CircleCiWorkflowEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckRunEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubCheckSuiteEventHandler
import com.nextchaptersoftware.ciservice.handlers.github.GitHubWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.gitlab.GitLabJobEventHandler
import com.nextchaptersoftware.ciservice.handlers.gitlab.GitLabPipelineEventHandler
import com.nextchaptersoftware.ciservice.handlers.gitlab.GitLabWebhookHandler
import com.nextchaptersoftware.ciservice.handlers.triages.TriageBillingEventHandler
import com.nextchaptersoftware.ciservice.handlers.triages.TriagePublishEventHandler
import com.nextchaptersoftware.ciservice.handlers.triages.TriageRequestEventHandler
import com.nextchaptersoftware.ciservice.jobs.ReactionIngestionJob
import com.nextchaptersoftware.ciservice.jobs.RepoTriageMetricsJob
import com.nextchaptersoftware.ciservice.jobs.TriageLatencyMetricsJob
import com.nextchaptersoftware.ciservice.plugins.configureRouting
import com.nextchaptersoftware.ciservice.services.ReactionIngestionService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.cohere.api.CohereApiConfiguration
import com.nextchaptersoftware.cohere.api.CohereApiProvider
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.config.ServiceInitializer
import com.nextchaptersoftware.confluence.api.ConfluenceCloudApiProvider
import com.nextchaptersoftware.crypto.AESCryptoSystem
import com.nextchaptersoftware.crypto.RSACryptoSystem
import com.nextchaptersoftware.datasources.ConfluenceAccessService
import com.nextchaptersoftware.datasources.JiraAccessService
import com.nextchaptersoftware.datasources.ThreadAccessService
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.dsac.filter.DataSourceAccessControlFilterFactory
import com.nextchaptersoftware.dsac.provider.CodaDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.ConfluenceDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.GoogleDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraAccessComputeService
import com.nextchaptersoftware.dsac.provider.JiraDataCenterDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.JiraDocumentAccessProvider
import com.nextchaptersoftware.dsac.provider.LinearAccessComputeService
import com.nextchaptersoftware.dsac.provider.NotionDocumentAccessProvider
import com.nextchaptersoftware.embedding.config.EmbeddingSecretConfig
import com.nextchaptersoftware.embedding.encoding.EmbeddingEncoding
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.environment.StandardUrlBuilderProvider
import com.nextchaptersoftware.event.queue.dequeue.SequentialBatchEventDequeue
import com.nextchaptersoftware.event.queue.dequeue.StandardEventMessageProcessor
import com.nextchaptersoftware.event.queue.enqueue.StandardEventEnqueueService
import com.nextchaptersoftware.gemini.api.GeminiApiProvider
import com.nextchaptersoftware.gemini.config.GeminiApiConfig
import com.nextchaptersoftware.google.api.GoogleApiProvider
import com.nextchaptersoftware.google.services.GoogleCredentialProvider
import com.nextchaptersoftware.google.services.GoogleWorkspaceServiceAccountKeyProvider
import com.nextchaptersoftware.insider.InsiderService
import com.nextchaptersoftware.insider.NoOpInsiderService
import com.nextchaptersoftware.insight.index.PullRequestInsightIndexContentService
import com.nextchaptersoftware.insight.index.ThreadInsightIndexContentService
import com.nextchaptersoftware.integration.queue.redis.cache.StandardIngestionProgressServiceProvider
import com.nextchaptersoftware.jira.api.JiraApiProvider
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.linear.api.LinearApiProvider
import com.nextchaptersoftware.linear.services.LinearTokenProvider
import com.nextchaptersoftware.links.LinkInstallationResolverFactory
import com.nextchaptersoftware.links.LinkProcessorFactory
import com.nextchaptersoftware.ml.api.delegate.MachineLearningApiProviderDelegate
import com.nextchaptersoftware.ml.api.delegate.MachineLearningGCloudPrioritizedApiProviderDelegate
import com.nextchaptersoftware.ml.completion.AnthropicCompletionService
import com.nextchaptersoftware.ml.completion.BedrockAnthropicCompletionService
import com.nextchaptersoftware.ml.completion.CohereCompletionService
import com.nextchaptersoftware.ml.completion.DecisionCompletionService
import com.nextchaptersoftware.ml.completion.GeminiCompletionService
import com.nextchaptersoftware.ml.completion.MachineLearningCompletionService
import com.nextchaptersoftware.ml.completion.OpenAICompletionService
import com.nextchaptersoftware.ml.completion.RoundRobinCompletionService
import com.nextchaptersoftware.ml.doc.converter.ConfluenceDocConverter
import com.nextchaptersoftware.ml.doc.converter.JiraDocConverter
import com.nextchaptersoftware.ml.doc.converter.PullRequestDocConverter
import com.nextchaptersoftware.ml.doc.converter.ThreadDocConverter
import com.nextchaptersoftware.ml.doc.rerank.services.BedrockCohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.CohereDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.DecisionDocumentRerankService
import com.nextchaptersoftware.ml.doc.rerank.services.RoundRobinDocumentRerankService
import com.nextchaptersoftware.ml.embedding.opensearch.store.OpenSearchEmbeddingStore
import com.nextchaptersoftware.ml.embedding.pinecone.store.PineconeEmbeddingStore
import com.nextchaptersoftware.ml.embedding.query.services.StandardEmbeddingQueryService
import com.nextchaptersoftware.ml.embedding.query.services.filter.EmbeddingQueryFilterBuilder
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionDecorator
import com.nextchaptersoftware.ml.embedding.query.services.fusion.ReciprocalRankFusionService
import com.nextchaptersoftware.ml.embedding.services.EmbeddingService
import com.nextchaptersoftware.ml.embedding.services.RoundRobinEmbeddingService
import com.nextchaptersoftware.ml.functions.FileMLFunctions
import com.nextchaptersoftware.ml.functions.GitHubIssuesMLFunctions
import com.nextchaptersoftware.ml.functions.JiraMLFunctions
import com.nextchaptersoftware.ml.functions.LinearMLFunctions
import com.nextchaptersoftware.ml.functions.SlackMLFunctions
import com.nextchaptersoftware.ml.inference.services.template.MLInferenceTemplateService
import com.nextchaptersoftware.ml.prompt.services.PromptCompilerService
import com.nextchaptersoftware.ml.services.MLFunctionMemberService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.redis.store.InviteRedisStore
import com.nextchaptersoftware.notification.events.services.InviteService
import com.nextchaptersoftware.notification.events.services.OrgDescriptionModelService
import com.nextchaptersoftware.openai.api.OpenAIApiConfiguration
import com.nextchaptersoftware.openai.api.OpenAIApiProvider
import com.nextchaptersoftware.openai.api.delegates.AzureOpenAIApiProviderDelegate
import com.nextchaptersoftware.opensearch.api.OpenSearchApiConfiguration
import com.nextchaptersoftware.opensearch.api.OpenSearchApiProvider
import com.nextchaptersoftware.opensearch.config.OpenSearchConfig
import com.nextchaptersoftware.opensearch.index.OpenSearchIndexLoader
import com.nextchaptersoftware.opensearch.plugins.configureOpenSearch
import com.nextchaptersoftware.pinecone.api.PineconeApiConfiguration
import com.nextchaptersoftware.pinecone.api.PineconeApiProvider
import com.nextchaptersoftware.pinecone.api.PineconeControlPlaneApiProvider
import com.nextchaptersoftware.pinecone.config.PineconeConfig
import com.nextchaptersoftware.pinecone.index.PineconeIndexLoader
import com.nextchaptersoftware.pinecone.plugins.configurePinecone
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesServiceProvider
import com.nextchaptersoftware.rapid.services.RapidServiceProvider
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.redis.lock.LockExecution
import com.nextchaptersoftware.redis.lock.LockProvider
import com.nextchaptersoftware.redis.lock.LockType
import com.nextchaptersoftware.repo.LocalRepoComputeService
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.repo.RepoFocusService
import com.nextchaptersoftware.scm.CiApiLegacyFactory
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmRepoApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.bitbucket.BitbucketPipelinesClientProvider
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.config.ScmSecretConfig
import com.nextchaptersoftware.scm.github.GitHubActionsClientProvider
import com.nextchaptersoftware.scm.gitlab.GitLabPipelinesClientProvider
import com.nextchaptersoftware.scm.providers.TeamAndRepoProvider
import com.nextchaptersoftware.scm.validators.GitHubEventValidator
import com.nextchaptersoftware.search.semantic.services.SemanticSearchDocumentService
import com.nextchaptersoftware.search.semantic.services.agents.DocumentEvaluationRetriever
import com.nextchaptersoftware.search.semantic.services.agents.DocumentRelevanceEvaluatorAgent
import com.nextchaptersoftware.search.semantic.services.documentation.StandardDocumentationValidationService
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionExecutor
import com.nextchaptersoftware.search.semantic.services.functions.MLFunctionService
import com.nextchaptersoftware.search.semantic.services.functions.ScmCommitService
import com.nextchaptersoftware.search.semantic.services.references.InlineReferencesToMarkdownLinksService
import com.nextchaptersoftware.search.semantic.services.retrieval.SemanticDocumentRetriever
import com.nextchaptersoftware.search.services.query.factory.DocumentInsightContentService
import com.nextchaptersoftware.search.services.query.factory.SearchDecorator
import com.nextchaptersoftware.search.services.query.filter.DocumentSearchResultDecisionServiceProvider
import com.nextchaptersoftware.search.services.query.filter.SlackDecisionService
import com.nextchaptersoftware.search.services.query.filter.StandardSearchResultsFilter
import com.nextchaptersoftware.search.services.query.filter.ThreadSearchResultDecisionServiceProvider
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.service.PollingBackgroundJob
import com.nextchaptersoftware.service.createBackgroundJob
import com.nextchaptersoftware.service.exclusive
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.service.plugins.configureBackgroundJobs
import com.nextchaptersoftware.service.plugins.configureCoroutineSchedulerMetrics
import com.nextchaptersoftware.service.plugins.configureJvmMetrics
import com.nextchaptersoftware.service.plugins.configureMonitoring
import com.nextchaptersoftware.service.plugins.configureSerialization
import com.nextchaptersoftware.service.polling
import com.nextchaptersoftware.service.redisRateLimit
import com.nextchaptersoftware.slack.extractor.utils.SlackConversationSummaryPromptExtractor
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.slack.services.AuthorizedSlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackChannelAccessService
import com.nextchaptersoftware.slack.services.SlackChannelResolver
import com.nextchaptersoftware.slack.services.SlackTokenService
import com.nextchaptersoftware.user.secret.UserSecretService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.user.secret.config.UserSecretConfig
import com.nextchaptersoftware.web.events.queue.enqueue.WebEventEnqueueService
import io.ktor.server.application.Application
import io.lettuce.core.ExperimentalLettuceCoroutinesApi
import kotlin.time.Duration.Companion.hours
import kotlin.time.Duration.Companion.minutes
import kotlin.time.Duration.Companion.seconds

/**
 * The number of concurrent consumers for the ci webhooks queue
 */
private const val CONSUMER_CI_WEBHOOKS = 10

/**
 * The number of concurrent consumers for the ci event queue.
 */
private const val CONSUMER_CI_EVENTS = 10

/**
 * The number of concurrent consumers for the ci triages queue
 */
private const val CONSUMER_CI_TRIAGES = 2

/**
 * The number of concurrent consumers for the ci project queue
 */
private const val CONSUMER_CI_PROJECT_EVENTS = 2

@OptIn(ExperimentalLettuceCoroutinesApi::class)
@Suppress("LongMethod")
fun Application.module(
    serviceLifecycle: ServiceLifecycle,
    ciConfig: CIConfig = CIConfig.INSTANCE,
    config: GlobalConfig = GlobalConfig.INSTANCE,
    geminiConfig: GeminiApiConfig = GeminiApiConfig.INSTANCE,
    openSearchConfig: OpenSearchConfig = OpenSearchConfig.INSTANCE,
    pineconeConfig: PineconeConfig = PineconeConfig.INSTANCE,
    scmConfig: ScmConfig = ScmConfig.INSTANCE,
    scmSecretConfig: ScmSecretConfig = ScmSecretConfig.INSTANCE,
    userSecretConfig: UserSecretConfig = UserSecretConfig.INSTANCE,
    bedrockRuntimeProviderFactory: BedrockRuntimeProviderFactory = StandardBedrockRuntimeProviderFactory(),
    bedrockRuntimeAsyncProviderFactory: BedrockRuntimeAsyncProviderFactory = StandardBedrockRuntimeAsyncProviderFactory(),
) {
    val openSearchApiProvider by lazy {
        OpenSearchApiProvider(
            config = OpenSearchApiConfiguration(
                baseApiUri = config.openSearch.baseApiUri.asUrl,
                timeout = config.openSearch.defaultTimeout,
                userName = config.openSearch.userName,
                password = config.openSearch.password,
            ),
        )
    }

    val openSearchEmbeddingStore by lazy {
        OpenSearchEmbeddingStore(
            indexName = openSearchConfig.openSearchIndex.indexName,
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchIndexLoader by lazy {
        OpenSearchIndexLoader(
            openSearchApiProvider = openSearchApiProvider,
        )
    }

    val openSearchLockProvider by lazy {
        LockProvider(type = LockType.OpenSearchLoader)
    }

    val pineconeApiProvider by lazy {
        PineconeApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeControlPlaneApiProvider by lazy {
        PineconeControlPlaneApiProvider(
            config = PineconeApiConfiguration(
                indexName = pineconeConfig.pineconeIndex.indexName,
                dimension = pineconeConfig.pineconeIndex.dimension,
                apiKey = config.pinecone.apiKey,
                maxRetries = config.pinecone.maxRetries,
                timeout = config.pinecone.defaultTimeout,
            ),
        )
    }

    val pineconeEmbeddingStore by lazy {
        PineconeEmbeddingStore(pineconeApiProvider = pineconeApiProvider)
    }

    val pineconeIndexLoader by lazy {
        PineconeIndexLoader(
            pineconeControlPlaneApiProvider = pineconeControlPlaneApiProvider,
        )
    }

    val pineconeLockProvider by lazy {
        LockProvider(type = LockType.PineconeLoader)
    }

    val awsClientProvider by lazy {
        AWSClientProvider.from(
            region = ServiceInitializer.REGION.toRegion(),
        )
    }

    val bedrockRuntimeProvider by lazy {
        bedrockRuntimeProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockRuntimeAsyncProvider by lazy {
        bedrockRuntimeAsyncProviderFactory.generate(
            awsClientProvider = awsClientProvider,
        )
    }

    val bedrockAnthropicCompletionsApi by lazy {
        BedrockAnthropicCompletionsApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
            bedrockRuntimeAsyncProvider = bedrockRuntimeAsyncProvider,
        )
    }

    val bedrockCohereRerankApi by lazy {
        BedrockCohereRerankApi(
            bedrockRuntimeProvider = bedrockRuntimeProvider,
        )
    }

    val teamAndRepoProvider by lazy {
        TeamAndRepoProvider()
    }

    val buildEventEnqueueService by lazy {
        BuildEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.ciBuildEventsQueueName,
                ),
            ),
        )
    }

    val triageEventEnqueueService by lazy {
        TriageEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.ciTriageEventsQueueName,
                ),
            ),
        )
    }

    val ciBuildEventEnqueueService by lazy {
        CiBuildEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.ciBuildEventsQueueName,
                ),
            ),
        )
    }

    val buildIngestionService by lazy {
        BuildIngestionService(
            buildEventEnqueueService = buildEventEnqueueService,
        )
    }

    val anthropicApiConfiguration by lazy {
        AnthropicApiConfiguration(
            baseApiUri = config.anthropic.baseApiUri.asUrl,
            timeout = config.anthropic.defaultTimeout,
            token = config.anthropic.apiKey,
        )
    }

    val anthropicApiProvider by lazy {
        AnthropicApiProvider(
            config = anthropicApiConfiguration,
        )
    }

    val anthropicCompletionService by lazy {
        AnthropicCompletionService(
            anthropicApiProvider = anthropicApiProvider,
        )
    }

    val bedrockAnthropicCompletionService by lazy {
        BedrockAnthropicCompletionService(
            bedrockAnthropicCompletionsApi = bedrockAnthropicCompletionsApi,
        )
    }

    val roundRobinAnthropicCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = listOf(
                anthropicCompletionService,
                bedrockAnthropicCompletionService,
            ),
        )
    }

    val scmAppApiFactory by lazy {
        ScmAppApiFactory(
            scmConfig = scmConfig,
        )
    }

    val gitHubActionsClientProvider by lazy {
        GitHubActionsClientProvider(
            scmAppApiFactory = scmAppApiFactory,
        )
    }

    val insiderService by lazy {
        InsiderService()
    }

    val planCapabilitiesService by lazy {
        PlanCapabilitiesServiceProvider(config = config.billing).get()
    }

    val capabilityValidation by lazy {
        CapabilityValidation(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val clientConfigService by lazy {
        ClientConfigService()
    }

    val ciTriageController by lazy {
        CITriageController(
            capabilityValidation = capabilityValidation,
            clientConfigService = clientConfigService,
        )
    }

    val scmAuthApiFactory by lazy {
        ScmAuthApiFactory(
            authenticationConfig = config.authentication,
            scmConfig = scmConfig,
            scmWebFactory = ScmWebFactory(scmConfig),
        )
    }

    val userSecretServiceRSA by lazy {
        UserSecretService(
            encryption = RSACryptoSystem.RSAEncryption(
                publicKey = config.encryption.userSecrets4096PublicKey,
                modulusBitLength = 4096,
            ),
            decryption = RSACryptoSystem.RSADecryption(
                privateKey = userSecretConfig.encryption.userSecrets4096PrivateKey.value,
                modulusBitLength = 4096,
            ),
        )
    }

    val userSecretServiceAES by lazy {
        val key = AESCryptoSystem.importKey(
            userSecretConfig.encryption.userSecretsAesKey.value,
        )
        UserSecretService(
            encryption = AESCryptoSystem.AESEncryption(key),
            decryption = AESCryptoSystem.AESDecryption(key),
        )
    }

    val userSecretServiceResolver by lazy {
        UserSecretServiceResolver(
            userSecretServiceRSA = userSecretServiceRSA,
            userSecretServiceAES = userSecretServiceAES,
        )
    }

    val scmRepoApiFactory by lazy {
        ScmRepoApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val machineLearningApiProviders by MachineLearningApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val scmWebFactory by lazy {
        ScmWebFactory(
            scmConfig = scmConfig,
        )
    }

    val urlBuilderProvider by lazy {
        StandardUrlBuilderProvider(config)
    }

    val promptCompilerService by lazy {
        PromptCompilerService(scmWebFactory = scmWebFactory, urlBuilderProvider = urlBuilderProvider)
    }

    val embeddingService by lazy {
        RoundRobinEmbeddingService(
            embeddingServices = machineLearningApiProviders.map { machineLearningApiProvider ->
                EmbeddingService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val machineLearningGCloudPrioritizedApiProviders by MachineLearningGCloudPrioritizedApiProviderDelegate(
        machineLearningConfig = config.machineLearning,
    )

    val machineLearningCompletionService by lazy {
        RoundRobinCompletionService(
            completionServices = machineLearningGCloudPrioritizedApiProviders.map { machineLearningApiProvider ->
                MachineLearningCompletionService(
                    machineLearningApiProvider = machineLearningApiProvider,
                )
            },
        )
    }

    val openAIApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openAI.defaultTimeout,
                token = config.openAI.apiKey,
            ),
        )
    }

    val openRouterApiProvider by lazy {
        OpenAIApiProvider(
            config = OpenAIApiConfiguration(
                timeout = config.openRouter.defaultTimeout,
                token = config.openRouter.apiKey,
                host = OpenAIHost(baseUrl = config.openRouter.baseApiUri),
            ),
        )
    }

    val azureGPT4OmniOpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt4OmniDeploymentId,
    )

    val azureGPT4OmniMiniOpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt4OmniMiniDeploymentId,
    )

    val azureGPT41OpenAIApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41DeploymentId,
    )

    val azureGPT41NanoApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41NanoDeploymentId,
    )

    val azureGPT41MiniApiProviders by AzureOpenAIApiProviderDelegate(
        azureOpenAIConfig = config.azureOpenAI,
        deploymentId = config.azureOpenAI.gpt41MiniDeploymentId,
    )

    val cohereApiProvider by lazy {
        CohereApiProvider(
            config = CohereApiConfiguration(config),
        )
    }

    val cohereCompletionService by lazy {
        CohereCompletionService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val geminiApiProvider by lazy {
        GeminiApiProvider(
            config = geminiConfig,
        )
    }

    val geminiCompletionService by lazy {
        GeminiCompletionService(
            geminiApiProvider = geminiApiProvider,
        )
    }

    val completionService by lazy {
        DecisionCompletionService(
            machineLearningCompletionService = machineLearningCompletionService,
            openAICompletionService = OpenAICompletionService(
                openAIApiProvider = openAIApiProvider,
            ),
            openRouterCompletionService = OpenAICompletionService(
                openAIApiProvider = openRouterApiProvider,
            ),
            bedrockAnthropicCompletionService = bedrockAnthropicCompletionService,
            anthropicCompletionService = anthropicCompletionService,
            roundRobinGPT41CompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41OpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT41MiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41MiniApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT41NanoCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT41NanoApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT4OmniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT4OmniOpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinGPT4OmniMiniCompletionService = RoundRobinCompletionService(
                completionServices = azureGPT4OmniMiniOpenAIApiProviders.plus(openAIApiProvider).map {
                    OpenAICompletionService(
                        openAIApiProvider = it,
                    )
                },
            ),
            roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
            cohereCompletionService = cohereCompletionService,
            geminiCompletionService = geminiCompletionService,
        )
    }

    val embeddingSecretConfig by lazy {
        EmbeddingSecretConfig.INSTANCE
    }

    val embeddingEncoding by lazy {
        EmbeddingEncoding(
            embeddingContentConfig = embeddingSecretConfig.embedding,
        )
    }

    val reciprocalRankFusionService by lazy {
        ReciprocalRankFusionService()
    }

    val reciprocalRankFusionDecorator by lazy {
        ReciprocalRankFusionDecorator(
            reciprocalRankFusionService = reciprocalRankFusionService,
        )
    }

    val botAccountService by lazy {
        InstallationBotAccountService(urlBuilderProvider = urlBuilderProvider)
    }

    val rapidServiceProvider by lazy {
        RapidServiceProvider(
            config = config.rapid,
        )
    }

    val threadInsightIndexContentService by lazy {
        ThreadInsightIndexContentService(urlBuilderProvider = urlBuilderProvider)
    }

    val pullRequestInsightIndexContentService by lazy {
        PullRequestInsightIndexContentService()
    }

    val documentInsightContentService by lazy {
        DocumentInsightContentService()
    }

    val documentDecisionServiceProvider by lazy {
        DocumentSearchResultDecisionServiceProvider()
    }

    val slackChannelAccessService by lazy {
        SlackChannelAccessService(
            planCapabilitiesService = planCapabilitiesService,
        )
    }

    val slackDecisionService by lazy {
        SlackDecisionService(slackChannelAccessService = slackChannelAccessService)
    }

    val threadSearchResultDecisionProvider by lazy {
        ThreadSearchResultDecisionServiceProvider(slackDecisionService = slackDecisionService)
    }

    val searchResultsFilter by lazy {
        StandardSearchResultsFilter(
            threadSearchResultDecisionProvider = threadSearchResultDecisionProvider,
            documentDecisionServiceProvider = documentDecisionServiceProvider,
        )
    }

    val searchDecorator by lazy {
        SearchDecorator(
            botAccountService = botAccountService,
            threadInsightIndexContentService = threadInsightIndexContentService,
            prInsightIndexContentService = pullRequestInsightIndexContentService,
            documentInsightContentService = documentInsightContentService,
            searchResultsFilter = searchResultsFilter,
        )
    }

    val embeddingStoreFacade by lazy {
        EmbeddingStoreFacade(
            openSearchEmbeddingStore = openSearchEmbeddingStore,
            pineconeEmbeddingStore = pineconeEmbeddingStore,
        )
    }

    val embeddingQueryFilterBuilder by lazy {
        EmbeddingQueryFilterBuilder()
    }

    val standardEmbeddingQueryService by lazy {
        StandardEmbeddingQueryService(
            embeddingStoreFacade = embeddingStoreFacade,
            embedder = embeddingService,
            embeddingQueryFilterBuilder = embeddingQueryFilterBuilder,
            embeddingEncoding = embeddingEncoding,
            reciprocalRankFusionDecorator = reciprocalRankFusionDecorator,
            searchDecorator = searchDecorator,
        )
    }

    val webEventEnqueueService by lazy {
        WebEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.webEventsQueueName,
                ),
            ),
            progressServiceProvider = StandardIngestionProgressServiceProvider(),
        )
    }

    val documentationValidationService by lazy {
        StandardDocumentationValidationService(
            webEventEnqueueService = webEventEnqueueService,
        )
    }

    val semanticSearchDocumentService by lazy {
        SemanticSearchDocumentService(
            embeddingQueryService = standardEmbeddingQueryService,
            documentationValidationService = documentationValidationService,
        )
    }

    val cohereDocumentRerankService by lazy {
        CohereDocumentRerankService(
            cohereApiProvider = cohereApiProvider,
        )
    }

    val bedrockCohereDocumentRerankService by lazy {
        BedrockCohereDocumentRerankService(
            bedrockCohereRerankApi = bedrockCohereRerankApi,
        )
    }

    val documentRerankService by lazy {
        DecisionDocumentRerankService(
            cohereDocumentRerankService = cohereDocumentRerankService,
            bedrockCohereDocumentRerankService = bedrockCohereDocumentRerankService,
            roundRobinCohereDocumentRerankService = RoundRobinDocumentRerankService(
                documentRerankServices = listOf(
                    cohereDocumentRerankService,
                    bedrockCohereDocumentRerankService,
                ),
            ),
        )
    }

    val unencryptedTokenPersistence by lazy {
        UnencryptedTokenPersistence(identityStore = Stores.identityStore)
    }

    val confluenceAuthApi by lazy {
        config.providers.confluence?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val confluenceAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = confluenceAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val confluenceCloudApiProvider by lazy {
        ConfluenceCloudApiProvider()
    }

    val userSecretService by lazy {
        // TODO: remove once all have been migrated to `userSecretServiceRSA`
        userSecretServiceRSA
    }

    val jiraAuthApi by lazy {
        config.providers.jira?.let { config ->
            AtlassianAuthApiImpl(
                clientId = config.oauth.clientId,
                clientSecret = config.clientSecret.value,
                tokenExchangeUrl = checkNotNull(config.oauth.tokenExchangeUrl),
                oauthCallbackUrl = checkNotNull(config.oauth.oauthCallbackUrl),
            )
        } ?: NoopAtlassianAuthApi()
    }

    val jiraAtlassianTokenProvider by lazy {
        AtlassianTokenProvider(
            oauthTokenRefreshService = UserSecretOAuthRefreshService(
                tokenRefresher = jiraAuthApi,
                tokenPersistence = unencryptedTokenPersistence,
            ),
        )
    }

    val jiraApiProvider by lazy {
        JiraApiProvider()
    }

    val googleOAuthRefreshService by lazy {
        UserSecretOAuthRefreshService(
            tokenRefresher = NoopOAuthTokenRefresher(), // Not used for Google OAuth
            tokenPersistence = EncryptedTokenPersistence(userSecretService = userSecretService),
        )
    }

    val googleWorkspaceServiceAccountKeyProvider by lazy {
        GoogleWorkspaceServiceAccountKeyProvider(
            userSecretService = userSecretServiceAES,
        )
    }

    val googleCredentialProvider by lazy {
        config.providers.googleDrive?.let {
            GoogleCredentialProvider(
                config = it,
                oAuthRefreshService = googleOAuthRefreshService,
                googleWorkspaceServiceAccountKeyProvider = googleWorkspaceServiceAccountKeyProvider,
            )
        }
    }

    val googleApiProvider by lazy {
        GoogleApiProvider()
    }

    val linearTokenProvider by lazy {
        LinearTokenProvider(
            userSecretService = userSecretService,
        )
    }

    val linearAccessComputeService by lazy {
        config.providers.linear?.let {
            LinearAccessComputeService(
                apiProvider = LinearApiProvider(
                    config = it,
                ),
                linearTokenProvider = linearTokenProvider,
            )
        }
    }

    val googleDocumentAccessProvider by lazy {
        googleCredentialProvider?.let {
            GoogleDocumentAccessProvider(
                googleCredentialProvider = it,
                googleApiProvider = googleApiProvider,
            )
        }
    }

    val dataSourceAccessControlFilterFactory by lazy {
        DataSourceAccessControlFilterFactory(
            codaDocumentAccessProvider = CodaDocumentAccessProvider(),
            confluenceDocumentAccessProvider = ConfluenceDocumentAccessProvider(
                atlassianTokenProvider = confluenceAtlassianTokenProvider,
                confluenceCloudApiProvider = confluenceCloudApiProvider,
            ),
            confluenceDataCenterDocumentAccessProvider = ConfluenceDataCenterDocumentAccessProvider(),
            jiraDocumentAccessProvider = JiraDocumentAccessProvider(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraProjectComputeService = JiraAccessComputeService(
                atlassianTokenProvider = jiraAtlassianTokenProvider,
                jiraApiProvider = jiraApiProvider,
            ),
            jiraDataCenterDocumentAccessProvider = JiraDataCenterDocumentAccessProvider(),
            googleDocumentAccessProvider = googleDocumentAccessProvider,
            linearAccessComputeService = linearAccessComputeService,
            notionDocumentAccessProvider = NotionDocumentAccessProvider(),
        )
    }

    val scmUserApiFactory by lazy {
        ScmUserApiFactory(
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val scmTeamApiFactory by lazy {
        ScmTeamApiFactory(
            scmAppApiFactory = scmAppApiFactory,
            scmAuthApiFactory = scmAuthApiFactory,
            scmConfig = scmConfig,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val repoFocusService by lazy {
        RepoFocusService()
    }
    val repoComputeService by lazy {
        LocalRepoComputeService(
            scmTeamApiFactory = scmTeamApiFactory,
            scmUserApiFactory = scmUserApiFactory,
        )
    }

    val repoAccessService by lazy {
        RepoAccessService(
            repoComputeService = repoComputeService,
        )
    }

    val semanticDocumentRetriever by lazy {
        SemanticDocumentRetriever(
            embedder = embeddingService,
            semanticSearchDocumentService = semanticSearchDocumentService,
            documentRerankService = documentRerankService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
            repoFocusService = repoFocusService,
            repoAccessService = repoAccessService,
            planCapabilitiesService = planCapabilitiesService,
            slackChannelAccessService = slackChannelAccessService,
        )
    }

    val inferenceTemplateService by lazy {
        MLInferenceTemplateService()
    }

    val documentRelevanceEvaluatorAgent by lazy {
        DocumentRelevanceEvaluatorAgent(
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
        )
    }

    val threadAccessService by lazy {
        ThreadAccessService(
            repoAccessService = repoAccessService,
            dataSourceAccessControlFilterFactory = dataSourceAccessControlFilterFactory,
        )
    }

    val jiraAccessService by lazy {
        JiraAccessService(
            atlassianTokenProvider = jiraAtlassianTokenProvider,
            jiraApiProvider = jiraApiProvider,
        )
    }

    val ciApiLegacyFactory by lazy {
        CiApiLegacyFactory(
            gitHubActionsClientProvider = gitHubActionsClientProvider,
            insiderService = insiderService,
        )
    }

    val logSummaryService by lazy {
        LogSummaryService(
            roundRobinAnthropicCompletionService = roundRobinAnthropicCompletionService,
            logFocusService = LogFocusService(),
        )
    }

    val commitService by lazy {
        ScmCommitService(
            scmRepoApiFactory = scmRepoApiFactory,
            repoFocusService = repoFocusService,
            completionService = completionService,
            repoAccessService = repoAccessService,
            ciApiLegacyFactory = ciApiLegacyFactory,
            logSummaryService = logSummaryService,
        )
    }

    val mlFunctionMemberService by lazy {
        MLFunctionMemberService(
            completionService = completionService,
        )
    }

    val jiraDocConverter by lazy {
        JiraDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val jiraMLFunctions by lazy {
        JiraMLFunctions(
            dsacFilterFactory = dataSourceAccessControlFilterFactory,
            jiraAccessService = jiraAccessService,
            jiraDocConverter = jiraDocConverter,
            mlFunctionMemberService = mlFunctionMemberService,
        )
    }

    val linearMLFunctions by lazy {
        LinearMLFunctions(
            mlFunctionMemberService = mlFunctionMemberService,
            threadAccessService = threadAccessService,
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val githubIssuesMLFunctions by lazy {
        GitHubIssuesMLFunctions(
            threadInsightIndexContentService = threadInsightIndexContentService,
            threadAccessService = threadAccessService,
        )
    }

    val fileMLFunctions by lazy {
        FileMLFunctions(
            commitService = commitService,
        )
    }

    val slackTokenService by lazy {
        SlackTokenService(
            userSecretService = userSecretService,
        )
    }

    val slackChannelResolver by lazy {
        SlackChannelResolver()
    }

    val authorizedSlackChannelResolver by lazy {
        AuthorizedSlackChannelResolver(
            slackChannelAccessService = slackChannelAccessService,
            slackChannelResolver = slackChannelResolver,
        )
    }

    val slackConversationSummaryPromptExtractor by lazy {
        SlackConversationSummaryPromptExtractor()
    }

    val slackMLFunctions by lazy {
        SlackMLFunctions(
            slackTokenService = slackTokenService,
            authorizedSlackChannelResolver = authorizedSlackChannelResolver,
            slackSummaryFunctionTimeout = config.search.functions.slackSummaryFunctionTimeout,
            urlBuilderProvider = urlBuilderProvider,
            slackConversationSummaryPromptExtractor = slackConversationSummaryPromptExtractor,
        )
    }

    val confluenceAccessService by lazy {
        ConfluenceAccessService(
            atlassianTokenProvider = confluenceAtlassianTokenProvider,
            confluenceCloudApiProvider = confluenceCloudApiProvider,
        )
    }

    val mlFunctionService by lazy {
        MLFunctionService(
            functions = listOfNotNull(
                fileMLFunctions,
                githubIssuesMLFunctions,
                jiraMLFunctions,
                linearMLFunctions,
                slackMLFunctions,
            ),
            templateService = inferenceTemplateService,
            promptCompilerService = promptCompilerService,
            completionService = completionService,
            globalFunctionTimeout = config.search.functions.globalFunctionTimeout,
        )
    }

    val mlFunctionExecutor by lazy {
        MLFunctionExecutor(
            mlFunctionService = mlFunctionService,
        )
    }

    val pullRequestDocConverter by lazy {
        PullRequestDocConverter(
            pullRequestInsightIndexContentService = pullRequestInsightIndexContentService,
        )
    }

    val threadDocConverter by lazy {
        ThreadDocConverter(
            threadInsightIndexContentService = threadInsightIndexContentService,
        )
    }

    val confluenceDocConverter by lazy {
        ConfluenceDocConverter(rapidQueryService = rapidServiceProvider.queryService)
    }

    val linkProcessorFactory by lazy {
        LinkProcessorFactory(
            installationResolverFactory = LinkInstallationResolverFactory(
                confluenceAccessService = confluenceAccessService,
                jiraAccessService = jiraAccessService,
                jiraDocConverter = jiraDocConverter,
                pullRequestDocConverter = pullRequestDocConverter,
                repoAccessService = repoAccessService,
                scmRepoApiFactory = scmRepoApiFactory,
                threadAccessService = threadAccessService,
                threadDocConverter = threadDocConverter,
                confluenceDocConverter = confluenceDocConverter,
            ),
        )
    }

    val documentEvaluationRetriever by lazy {
        DocumentEvaluationRetriever(
            linkProcessorFactory = linkProcessorFactory,
            semanticDocumentRetriever = semanticDocumentRetriever,
            promptCompilerService = promptCompilerService,
            templateService = inferenceTemplateService,
            documentRelevanceEvaluatorAgent = documentRelevanceEvaluatorAgent,
            mlFunctionExecutor = mlFunctionExecutor,
            embedder = embeddingService,
        )
    }

    val inlineReferencesToMarkdownLinksService by lazy {
        InlineReferencesToMarkdownLinksService(
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    val bitbucketPipelinesClientProvider by lazy {
        BitbucketPipelinesClientProvider(
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val ciProjectApiFactory by lazy {
        CIProjectApiFactory(
            bitbucketPipelinesClientProvider = bitbucketPipelinesClientProvider,
            ciSecretsConfig = CISecretsConfig.INSTANCE,
            gitHubActionsClientProvider = gitHubActionsClientProvider,
        )
    }

    val gitHubCheckRunEventHandler by lazy {
        GitHubCheckRunEventHandler(
            buildIngestionService = buildIngestionService,
            ciTriageController = ciTriageController,
            gitHubActionsClientProvider = gitHubActionsClientProvider,
            teamAndRepoProvider = teamAndRepoProvider,
        )
    }

    val gitHubCheckSuiteEventHandler by lazy {
        GitHubCheckSuiteEventHandler(
            buildIngestionService = buildIngestionService,
            ciTriageController = ciTriageController,
            gitHubActionsClientProvider = gitHubActionsClientProvider,
            teamAndRepoProvider = teamAndRepoProvider,
        )
    }

    val buildTriageQueryExecutor by lazy {
        BuildTriageQueryExecutor(
            completionService = completionService,
            promptCompilerService = promptCompilerService,
            inferenceTemplateService = inferenceTemplateService,
        )
    }

    val buildTriageDataRetrievalService by lazy {
        BuildTriageDataRetrievalService(
            inferenceTemplateService = inferenceTemplateService,
            queryExecutor = buildTriageQueryExecutor,
            documentEvaluationRetriever = documentEvaluationRetriever,
            embedder = embeddingService,
        )
    }

    val buildTriageLogSummaryService by lazy {
        BuildTriageLogSummaryService(
            queryExecutor = buildTriageQueryExecutor,
            logFocusService = LogFocusService(),
        )
    }

    val buildTriageEvalService by lazy {
        BuildTriageEvalService(
            queryExecutor = buildTriageQueryExecutor,
        )
    }

    val buildTriageFixSuggestionService by lazy {
        BuildTriageFixSuggestionService(
            queryExecutor = buildTriageQueryExecutor,
            inlineReferencesToMarkdownLinksService = inlineReferencesToMarkdownLinksService,
        )
    }

    val buildTriageDiffCompressionService by lazy {
        BuildTriageDiffCompressionService(
            queryExecutor = buildTriageQueryExecutor,
        )
    }

    val buildTriageJobCompressionService by lazy {
        BuildTriageJobCompressionService(
            inferenceTemplateService = inferenceTemplateService,
            queryExecutor = buildTriageQueryExecutor,
        )
    }

    val buildTriageInputMatcherService by lazy {
        BuildTriageInputMatcherService(
            queryExecutor = buildTriageQueryExecutor,
        )
    }

    val buildTriageService by lazy {
        BuildTriageService(
            buildTriageDataRetrievalService = buildTriageDataRetrievalService,
            buildTriageDiffCompressionService = buildTriageDiffCompressionService,
            buildTriageEvalService = buildTriageEvalService,
            buildTriageFixSuggestionService = buildTriageFixSuggestionService,
            buildTriageInputMatcherService = buildTriageInputMatcherService,
            buildTriageJobCompressionService = buildTriageJobCompressionService,
            buildTriageLogSummaryService = buildTriageLogSummaryService,
            ciProjectApiFactory = ciProjectApiFactory,
            ciTriageController = ciTriageController,
            insiderService = insiderService,
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val buildEventHandler by lazy {
        BuildEventHandler(
            ciTriageController = ciTriageController,
            triageEventEnqueueService = triageEventEnqueueService,
        )
    }

    val buildJobEventHandler by lazy {
        BuildJobEventHandler(
            ciTriageController = ciTriageController,
        )
    }

    val triagePublishLock by lazy {
        LockExecution(
            lockProvider = LockProvider(type = LockType.CiTriagePublish),
        )
    }

    val buildTriageTaskHandler by lazy {
        BuildTriageTaskHandler(
            lockExecution = triagePublishLock,
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val bitbucketBuildEventHandler by lazy {
        BitbucketBuildEventHandler(
            bitbucketPipelinesClientProvider = bitbucketPipelinesClientProvider,
            buildIngestionService = buildIngestionService,
            ciTriageController = ciTriageController,
            teamAndRepoProvider = teamAndRepoProvider,
        )
    }

    val buildkiteBuildEventHandler by lazy {
        BuildkiteBuildEventHandler(
            buildIngestionService = buildIngestionService,
        )
    }

    val buildkiteJobEventHandler by lazy {
        BuildkiteJobEventHandler(
            buildIngestionService = buildIngestionService,
        )
    }

    val circleCiJobEventHandler by lazy {
        CircleCiJobEventHandler(
            buildIngestionService = buildIngestionService,
        )
    }
    val circleCiWorkflowEventHandler by lazy {
        CircleCiWorkflowEventHandler(
            buildIngestionService = buildIngestionService,
        )
    }

    val gitLabPipelinesClientProvider by lazy {
        GitLabPipelinesClientProvider(
            gitLabCloudConfig = scmConfig.gitlabCloud,
            scmAuthApiFactory = scmAuthApiFactory,
            userSecretServiceResolver = userSecretServiceResolver,
        )
    }

    val gitLabJobEventHandler by lazy {
        GitLabJobEventHandler
    }

    val gitLabPipelineEventHandler by lazy {
        GitLabPipelineEventHandler(
            buildIngestionService = buildIngestionService,
            ciTriageController = ciTriageController,
            gitLabPipelinesClientProvider = gitLabPipelinesClientProvider,
        )
    }

    val ciWebhookEventHandler by lazy {
        CiWebhookEventHandler(
            bitbucketBuildEventHandler = bitbucketBuildEventHandler,
            buildkiteBuildEventHandler = buildkiteBuildEventHandler,
            buildkiteJobEventHandler = buildkiteJobEventHandler,
            circleCiJobEventHandler = circleCiJobEventHandler,
            circleCiWorkflowEventHandler = circleCiWorkflowEventHandler,
            gitHubCheckRunEventHandler = gitHubCheckRunEventHandler,
            gitHubCheckSuiteEventHandler = gitHubCheckSuiteEventHandler,
            gitLabJobEventHandler = gitLabJobEventHandler,
            gitLabPipelineEventHandler = gitLabPipelineEventHandler,
        )
    }

    val ciEventHandler by lazy {
        CiEventHandler(
            buildEventHandler = buildEventHandler,
            buildJobEventHandler = buildJobEventHandler,
            buildTriageTaskHandler = buildTriageTaskHandler,
            ciWebhookEventHandler = ciWebhookEventHandler,
        )
    }

    val gitHubEventValidator by lazy {
        GitHubEventValidator(
            gitHubCloudHMACAuthenticator = scmSecretConfig.githubCloud?.let {
                HMACAuthenticator(
                    authenticationSecret = it.webhookHMACSecret,
                    signaturePrefix = "sha256=",
                )
            },
            gitHubCloudConfig = scmConfig.githubCloud,
        )
    }

    val ciWebhookHandler by lazy {
        CiWebhookHandler(
            bitbucketWebhookHandler = BitbucketWebhookHandler(
                ciBuildEventEnqueueService = ciBuildEventEnqueueService,
            ),
            buildkiteWebhookHandler = BuildkiteWebhookHandler(
                ciBuildEventEnqueueService = ciBuildEventEnqueueService,
            ),
            circleCiWebhookHandler = CircleCiWebhookHandler(
                ciBuildEventEnqueueService = ciBuildEventEnqueueService,
            ),
            gitHubEventValidator = gitHubEventValidator,
            gitHubWebhookHandler = GitHubWebhookHandler(
                ciBuildEventEnqueueService = ciBuildEventEnqueueService,
            ),
            gitLabWebhookHandler = GitLabWebhookHandler(
                ciBuildEventEnqueueService = ciBuildEventEnqueueService,
            ),
        )
    }

    val reactionIngestionService by lazy {
        ReactionIngestionService(
            scmRepoApiFactory = scmRepoApiFactory,
        )
    }

    val triageRequestEventHandler by lazy {
        TriageRequestEventHandler(
            buildTriageService = buildTriageService,
            ciTriageController = ciTriageController,
            lockExecution = LockExecution(type = LockType.CiTriageRequest),
            triageEventEnqueueService = triageEventEnqueueService,
        )
    }

    val inviteRedisStore by lazy {
        InviteRedisStore(redisApi = Redis.API)
    }

    val orgDescriptionModelService by lazy {
        OrgDescriptionModelService()
    }

    val inviteService by lazy {
        InviteService(
            inviteRedisStore = inviteRedisStore,
            orgDescriptionModelService = orgDescriptionModelService,
        )
    }

    val ciSecretsConfig by lazy {
        CISecretsConfig.INSTANCE
    }

    val ciJwtSigner by lazy {
        CIJwtSigner(
            ciConfig = ciConfig,
            ciSecretsConfig = ciSecretsConfig,
        )
    }

    val triagePublishEventHandler by lazy {
        TriagePublishEventHandler(
            ciJwtSigner = ciJwtSigner,
            inviteService = inviteService,
            lockExecution = triagePublishLock,
            scmRepoApiFactory = scmRepoApiFactory,
            triageEventEnqueueService = triageEventEnqueueService,
        )
    }

    val notificationEventEnqueueService by lazy {
        NotificationEventEnqueueService(
            eventEnqueueService = StandardEventEnqueueService(
                messageProducer = ActiveMQProducer.producer(
                    queueName = config.queue.notificationEventsQueueName,
                ),
            ),
        )
    }

    val orgBillingSeatService by lazy {
        OrgBillingSeatService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val triageBillingEventHandler by lazy {
        TriageBillingEventHandler(
            orgBillingSeatService = orgBillingSeatService,
        )
    }
    val ciTriageEventHandler by lazy {
        CiTriageEventHandler(
            triageRequestEventHandler = triageRequestEventHandler,
            triagePublishEventHandler = triagePublishEventHandler,
            triageBillingEventHandler = triageBillingEventHandler,
        )
    }

    val ciUserApiFactory by lazy {
        CIUserApiFactory()
    }

    val ciProjectControlService by lazy {
        CIProjectControlService(
            ciProjectApiFactory = ciProjectApiFactory,
            ciSecretConfig = ciSecretsConfig.ci,
            ciUserApiFactory = ciUserApiFactory,
        )
    }

    val slackNotifier by lazy {
        SlackNotifier(
            internalSlackConfig = config.internalSlack,
            adminWebConfig = config.adminWeb,
            insiderService = insiderService,
            urlBuilderProvider = urlBuilderProvider,
        )
    }

    configureOpenSearch(
        lockProvider = openSearchLockProvider,
        config = config,
        openSearchIndexLoader = openSearchIndexLoader,
        openSearchConfig = openSearchConfig,
    )
    configurePinecone(
        lockProvider = pineconeLockProvider,
        pineconeConfig = pineconeConfig,
        pineconeIndexLoader = pineconeIndexLoader,
    )
    configureRouting(serviceLifecycle = serviceLifecycle)
    configureMonitoring(insiderService = NoOpInsiderService())
    configureSerialization()
    configureJvmMetrics()
    configureCoroutineSchedulerMetrics()
    configureBackgroundJobs(
        jobs = buildList {
            add(
                ReactionIngestionJob(
                    reactionIngestionService = reactionIngestionService,
                )
                    .exclusive()
                    .polling(12.hours),
            )

            add(
                TriageLatencyMetricsJob(
                    slackNotifier = slackNotifier,
                    triageLatencyAlarm = TriageLatencyAlarm(
                        config = ciConfig.alarms.triageLatency,
                    ),
                )
                    .exclusive()
                    .redisRateLimit(ciConfig.alarms.triageLatency.interval)
                    .polling(5.minutes),
            )

            add(
                RepoTriageMetricsJob()
                    .exclusive()
                    .polling(3.hours),
            )

            repeat(CONSUMER_CI_WEBHOOKS) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = createBackgroundJob(
                            name = "CI Webhook processing job",
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.hooksCiQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = ciWebhookHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }
            repeat(CONSUMER_CI_EVENTS) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = createBackgroundJob(
                            name = "CI build event processing job",
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.ciBuildEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = ciEventHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }
            repeat(CONSUMER_CI_TRIAGES) {
                add(
                    PollingBackgroundJob(
                        interval = 1.seconds,
                        job = createBackgroundJob(
                            name = "CI triage event processing job",
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.ciTriageEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = StandardEventMessageProcessor(
                                    handler = ciTriageEventHandler,
                                ),
                            ),
                        ),
                    ),
                )
            }
            repeat(CONSUMER_CI_PROJECT_EVENTS) {
                add(
                    PollingBackgroundJob(
                        interval = 2.seconds,
                        job = createBackgroundJob(
                            name = "CI Projects processing job",
                            eventDequeueService = SequentialBatchEventDequeue(
                                messageConsumer = ActiveMQConsumer.consumer(
                                    queueName = config.queue.ciProjectEventsQueueName,
                                    transacted = true,
                                ),
                                eventMessageProcessor = CIProjectEventMessageProcessor(
                                    ciProjectControlService = ciProjectControlService,
                                ),
                            ),
                        ),
                    ),
                )
            }
        },
    )
}
