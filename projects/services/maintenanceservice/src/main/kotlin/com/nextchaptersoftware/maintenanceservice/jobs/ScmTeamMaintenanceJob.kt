package com.nextchaptersoftware.maintenanceservice.jobs

import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamMaintenance
import com.nextchaptersoftware.recommendation.SocialCommentNetwork
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.service.BackgroundJob
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ScmTeamMaintenanceJob(
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val scmAppApiFactory: ScmAppApiFactory,
    private val scmTeamApiFactory: ScmTeamApiFactory,
    private val scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    private val scmTeamMaintenance: ScmTeamMaintenance,
    private val socialCommentNetwork: SocialCommentNetwork,
) : BackgroundJob {

    override val name: String = javaClass.simpleName

    override suspend fun run() {
        scmTeamStore.listActiveTeams()
            .filter { it.provider.isScmProvider }
            .forEach { scmTeam ->
                withLoggingContextAsync("teamId" to scmTeam.id) {
                    runSuspendCatching {
                        refreshTeam(scmTeam, Scm.fromTeam(scmTeam))
                        LOGGER.infoAsync { "Refreshed team ok" }
                    }.onFailure {
                        LOGGER.warnAsync(it) { "Failed to refresh team" }
                    }
                }
            }
    }

    private suspend fun isInstallationValid(scmTeam: ScmTeam, scm: Scm): Boolean {
        val installationId = when (scm) {
            is Scm.GitHubEnterprise,
            Scm.GitHub,
                -> scmTeam.providerExternalInstallationId ?: return false

            is Scm.BitbucketDataCenter,
            is Scm.GitLabSelfHosted,
            Scm.AzureDevOps,
            Scm.Bitbucket,
            Scm.GitLab,
                -> return true
        }

        return scmAppApiFactory.getApi(scmTeam = scmTeam, scm = scm).v3App().use {
            it.installation(installationId)
        }.let {
            it.isValid && it.isSuspended.not()
        }
    }

    private suspend fun refreshTeam(scmTeam: ScmTeam, scm: Scm) {
        val providerInstallationValid = isInstallationValid(scmTeam, scm)

        scmTeamApiFactory.getApiFromTeam(scmTeam, scm).use { api ->
            scmTeamLifecycleMaintenance.upsertTeam(
                scm = scm,
                scmAccount = api.account(),
                orgId = null,
                createIfNecessary = false,
                // We don't know what the installation ID is in this context,
                // so we pass null which is ignored by the updateOrCreateTeam method.
                providerExternalInstallationId = null,
                providerInstallationValid = providerInstallationValid,
            )

            refreshTeamResources(scmTeam, scm)
        }

        val orgId = scmTeamStore.getOrgId(teamId = scmTeam.id)
        socialCommentNetwork.regenerateSocialNetwork(job = null, orgId = orgId)
    }

    private suspend fun refreshTeamResources(scmTeam: ScmTeam, scm: Scm) {
        return scmTeamMaintenance.refreshTeamResources(scmTeam, scm)
    }
}
