package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.Integration
import com.nextchaptersoftware.api.models.IntegrationInstallationV3
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.ModelBuilders.makeClientCapability
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeInstallation
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.CustomHeaders
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.scm.config.ScmConfig
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.currentCoroutineContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class IntegrationsApiDelegateImplTest : DatabaseTestsBase() {
    private lateinit var identity: IdentityDAO
    private lateinit var client: UnblockedApiClient
    private lateinit var org: OrgDAO
    private lateinit var member: MemberDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var installation: InstallationDAO
    private lateinit var installationIdentity: IdentityDAO

    private suspend fun setup(
        ciConfig: CIConfig = CIConfig.INSTANCE,
        config: GlobalConfig = GlobalConfig.INSTANCE,
        scmConfig: ScmConfig = ScmConfig.INSTANCE,
    ) {
        val person = makePerson()
        identity = makeIdentity(person = person)
        org = makeOrg()
        installation = makeInstallation(org = org, provider = Provider.Slack)
        scmTeam = makeScmTeam(org = org, installation = installation)
        member = makeMember(scmTeam = scmTeam, identity = identity, isCurrentMember = true, isPrimaryMember = true)

        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            ciConfig = ciConfig,
            config = config,
            scmConfig = scmConfig,
            authContext = ApiAuthContext.Authenticated(
                identityId = identity.id.value,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
        )
    }

    private suspend fun setupWithConnection(
        ciConfig: CIConfig = CIConfig.INSTANCE,
        config: GlobalConfig = GlobalConfig.INSTANCE,
        scmConfig: ScmConfig = ScmConfig.INSTANCE,
    ) {
        setup(
            ciConfig = ciConfig,
            config = config,
            scmConfig = scmConfig,
        )

        // This indicates that an installation connection has been established
        installationIdentity = makeIdentity(person = identity.person, provider = installation.provider)

        makeMember(scmTeam = scmTeam, identity = installationIdentity, orgMember = member.orgMember, installation = installation)
    }

    private fun listIntegrations(
        overrideEnvironment: String,
        withInstallations: Set<Provider> = emptySet(),
        withFeatureFlags: Set<ClientCapabilityType> = emptySet(),
        expectedProviders: List<ApiProvider>,
    ) = suspendingDatabaseTest {
        withFeatureFlags.forEach { flag ->
            makeClientCapability(value = true, key = flag)
        }

        setupWithConnection(
            ciConfig = CIConfig.getTestInstance(
                overrideEnvironment = overrideEnvironment,
                overrideUser = "nobody",
            ),
            config = GlobalConfig.getTestInstance(
                overrideEnvironment = overrideEnvironment,
                overrideUser = "nobody",
            ),
            scmConfig = ScmConfig.getTestInstance(
                overrideEnvironment = overrideEnvironment,
                overrideUser = "nobody",
            ),
        )

        withInstallations.forEach { provider ->
            makeInstallation(org = org, provider = provider)
        }

        client.listIntegrationsV2(orgId = org.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val actual: List<Integration> = body()
            assertThat(actual.map { it.provider }).containsExactlyInAnyOrderElementsOf(expectedProviders)
        }
    }

    @Nested
    inner class ListIntegrations {
        private val generallyAvailableProviders = Provider.entries
            .filter { it.isDataSourceProvider || it.isScmProvider }
            .filterNot { it == Provider.Asana } // TODO Make generally available
            .filterNot { it == Provider.GoogleDriveWorkspace } // TODO Make generally available
            .map { it.asApiModel() }

        @Test
        fun `listIntegrations -- LOCAL`() {
            listIntegrations(
                overrideEnvironment = "local",
                expectedProviders = generallyAvailableProviders,
            )
        }

        @Test
        fun `listIntegrations -- LOCAL with flags ON -- and all SCMs installed`() {
            listIntegrations(
                overrideEnvironment = "local",
                withInstallations = setOf(
                    Provider.Bitbucket,
                    Provider.GitHub,
                    Provider.GitLab,
                ),
                withFeatureFlags = setOf(
                    ClientCapabilityType.FeatureAsana,
                    ClientCapabilityType.FeatureConfigureCI,
                ),
                expectedProviders = generallyAvailableProviders + listOf(
                    ApiProvider.asana,
                    ApiProvider.bitbucketPipelines,
                    ApiProvider.buildKite,
                    ApiProvider.circleci,
                    ApiProvider.githubActions,
                    ApiProvider.gitlabPipelines,
                ),
            )
        }

        @Test
        fun `listIntegrations -- LOCAL with flags ON -- and GitHub installed`() {
            listIntegrations(
                overrideEnvironment = "local",
                withInstallations = setOf(
                    Provider.GitHub,
                ),
                withFeatureFlags = setOf(
                    ClientCapabilityType.FeatureAsana,
                    ClientCapabilityType.FeatureConfigureCI,
                ),
                expectedProviders = generallyAvailableProviders + listOf(
                    ApiProvider.asana,
                    ApiProvider.buildKite,
                    ApiProvider.circleci,
                    ApiProvider.githubActions,
                ),
            )
        }

        @Test
        fun `listIntegrations -- LOCAL with flags ON -- and Bitbucket installed`() {
            listIntegrations(
                overrideEnvironment = "local",
                withInstallations = setOf(
                    Provider.Bitbucket,
                ),
                withFeatureFlags = setOf(
                    ClientCapabilityType.FeatureAsana,
                    ClientCapabilityType.FeatureConfigureCI,
                ),
                expectedProviders = generallyAvailableProviders + listOf(
                    ApiProvider.asana,
                    ApiProvider.bitbucketPipelines,
                    ApiProvider.buildKite,
                    ApiProvider.circleci,
                ),
            )
        }

        @Test
        fun `listIntegrations -- LOCAL with flags ON -- and GitLab installed`() {
            listIntegrations(
                overrideEnvironment = "local",
                withInstallations = setOf(
                    Provider.GitLab,
                ),
                withFeatureFlags = setOf(
                    ClientCapabilityType.FeatureAsana,
                    ClientCapabilityType.FeatureConfigureCI,
                ),
                expectedProviders = generallyAvailableProviders + listOf(
                    ApiProvider.asana,
                    ApiProvider.buildKite,
                    ApiProvider.circleci,
                    ApiProvider.gitlabPipelines,
                ),
            )
        }
    }

    @Test
    fun listIntegrationInstallationsV3() = suspendingDatabaseTest {
        setupWithConnection()
        client.listIntegrationInstallationsV3(orgId = org.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual).hasSize(1)
            assertThat(actual.single().userOauthUrl).isNotNull()
            assertThat(actual.single().connectionIdentity).isNotNull()
            assertThat(actual.single().userDismissedConnectionPrompt).isFalse
            val lastModifiedAt = headers[CustomHeaders.LAST_MODIFIED]
            assertThat(lastModifiedAt).isEqualTo(installation.modifiedAt.toString())
        }

        client.listIntegrationInstallationsV3(orgId = org.idValue, ifModifiedSince = installation.modifiedAt.minus(1.milliseconds)) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual.single().id).isEqualTo(installation.id.value.value)
            assertThat(actual.single().userDismissedConnectionPrompt).isFalse
            val lastModifiedAt = headers[CustomHeaders.LAST_MODIFIED]
            assertThat(lastModifiedAt).isEqualTo(installation.modifiedAt.toString())
        }

        client.listIntegrationInstallationsV3(orgId = org.idValue, ifModifiedSince = installation.modifiedAt) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual).isEmpty()
            val lastModifiedAt = headers[CustomHeaders.LAST_MODIFIED]
            assertThat(lastModifiedAt).isEqualTo(installation.modifiedAt.toString())
        }

        // add suppression
        client.dismissIntegrationConnection(orgId = org.idValue, installationId = installation.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.NoContent)
        }
        client.listIntegrationInstallationsV3(orgId = org.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual).hasSize(1)
            assertThat(actual.single().userDismissedConnectionPrompt).isTrue
        }
    }

    @Test
    fun `listIntegrationInstallationsV3 without connection`() = suspendingDatabaseTest {
        setup()
        client.listIntegrationInstallationsV3(orgId = org.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual).hasSize(1)
            assertThat(actual.single().connectionIdentity).isNull()
            assertThat(actual.single().userDismissedConnectionPrompt).isFalse
        }

        // add suppression
        client.dismissIntegrationConnection(orgId = org.idValue, installationId = installation.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.NoContent)
        }

        client.listIntegrationInstallationsV3(orgId = org.idValue) {
            assertThat(this.status).isEqualTo(HttpStatusCode.OK)
            val actual: List<IntegrationInstallationV3> = body()
            assertThat(actual).hasSize(1)
            assertThat(actual.single().connectionIdentity).isNull()
            assertThat(actual.single().userDismissedConnectionPrompt).isTrue
        }
    }
}
