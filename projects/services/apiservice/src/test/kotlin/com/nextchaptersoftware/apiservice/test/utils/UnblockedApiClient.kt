package com.nextchaptersoftware.apiservice.test.utils

import com.nextchaptersoftware.api.integration.extension.services.slack.SlackService
import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.CreateApiKeyRequest
import com.nextchaptersoftware.api.models.CreateMessageRequestV3
import com.nextchaptersoftware.api.models.CreateThreadRequestV3
import com.nextchaptersoftware.api.models.DiscoverRepoRequest
import com.nextchaptersoftware.api.models.EmailPreferences
import com.nextchaptersoftware.api.models.FeatureSettings
import com.nextchaptersoftware.api.models.FetchTeamMembersRequest
import com.nextchaptersoftware.api.models.InsightCountsRequest
import com.nextchaptersoftware.api.models.OnboardingStatusUpdate
import com.nextchaptersoftware.api.models.PersonPreferences
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.models.PullRequestsForCommitsRequest
import com.nextchaptersoftware.api.models.QueryInstallationRequest
import com.nextchaptersoftware.api.models.SearchInsightsRequest
import com.nextchaptersoftware.api.models.SearchSlackChannelsRequest
import com.nextchaptersoftware.api.models.SemanticSearchFileRequest
import com.nextchaptersoftware.api.models.SlackConfigurationV3
import com.nextchaptersoftware.api.models.SlackConfigurationV4
import com.nextchaptersoftware.api.models.TeamMemberRole
import com.nextchaptersoftware.api.models.UpdateMessageFeedbackRequest
import com.nextchaptersoftware.api.models.UpdateMessageRequestV3
import com.nextchaptersoftware.api.models.UpdateThreadPrivacyRequest
import com.nextchaptersoftware.api.models.UpdateThreadRequest
import com.nextchaptersoftware.api.models.UpdateThreadUnreadRequest
import com.nextchaptersoftware.api.models.UpdateWebIngestionConfigurationRequest
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.public.key.ApiKeyPersistence
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installJsonSerializer
import com.nextchaptersoftware.api.services.PullRequestService
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.apiservice.module
import com.nextchaptersoftware.auth.saml.config.SamlConfig
import com.nextchaptersoftware.auth.saml.config.SamlConfigs
import com.nextchaptersoftware.ci.config.CIConfig
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.MockThreadUnreadStore
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MessageId
import com.nextchaptersoftware.db.models.OrgApiKeyId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.PullRequestCommentId
import com.nextchaptersoftware.db.models.PullRequestId
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.SlackTeamId
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.stores.ThreadUnreadStore
import com.nextchaptersoftware.db.utils.DatabaseContextPlugin
import com.nextchaptersoftware.ktor.CustomHeaders
import com.nextchaptersoftware.metrics.MetricsService
import com.nextchaptersoftware.ml.embedding.core.models.MLVectorPair
import com.nextchaptersoftware.ml.embedding.services.EmbeddingService
import com.nextchaptersoftware.plan.capabilities.NoopPlanCapabilitiesService
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.repo.RepoComputeService
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.search.services.query.PostgresQueryService
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.client.HttpClient
import io.ktor.client.plugins.HttpRequestRetry
import io.ktor.client.plugins.auth.Auth
import io.ktor.client.plugins.auth.providers.BearerTokens
import io.ktor.client.plugins.auth.providers.bearer
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.plugins.defaultRequest
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.parameter
import io.ktor.client.request.patch
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.contentType
import io.ktor.server.application.install
import io.ktor.server.testing.testApplication
import java.util.UUID
import kotlinx.coroutines.runBlocking
import kotlinx.datetime.Instant
import org.jetbrains.exposed.sql.Database
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.mock

@Suppress("LargeClass", "LongParameterList")
class UnblockedApiClient(
    private val authContext: ApiAuthContext,
    private val database: Database?,
    private val scmInstallationService: ScmInstallationService? = null,
    val threadUnreadStore: ThreadUnreadStore = MockThreadUnreadStore.get(),
    private val planCapabilitiesService: PlanCapabilitiesService = NoopPlanCapabilitiesService(),
    private val postgresQueryService: PostgresQueryService = MockSearchInsightQueryService.get(),
    private val pullRequestService: PullRequestService = mock(),
    private val metricsService: MetricsService = mock(),
    private val slackService: SlackService = mock(),
    private val apiKeyPersistence: ApiKeyPersistence = mock(),
    private val repoComputeService: RepoComputeService? = null,
    private val config: GlobalConfig = GlobalConfig.INSTANCE,
    private val samlConfig: SamlConfig = SamlConfigs.INSTANCE.saml,
    private val ciConfig: CIConfig = CIConfig.INSTANCE,
    private val scmConfig: ScmConfig = ScmConfig.INSTANCE,
) {
    private val basePath = "/api"

    private val jwt by lazy {
        Jwt(authenticationConfig = config.authentication)
    }

    private val mockEmbedder by lazy {
        val embedder = mock<EmbeddingService>()
        runBlocking {
            `when`(embedder.getQueryEmbedding(input = any(), embeddingModel = any())).thenReturn(
                MLVectorPair(denseVector = listOf(0.0, 0.0, 0.0), sparseVector = null),
            )
        }
        embedder
    }

    private fun client(
        block: suspend HttpClient.() -> Unit,
    ) = testApplication {
        application {
            module(
                ciConfig = ciConfig,
                config = config,
                scmConfig = scmConfig,
                samlConfig = samlConfig,
                overridePlanCapabilityService = planCapabilitiesService,
                overridePostgresQueryService = postgresQueryService,
                overridePullRequestService = pullRequestService,
                overrideMetricsService = metricsService,
                overrideSlackServiceImpl = slackService,
                overrideSlackNotifier = mock(),
                overrideEmbeddingService = mockEmbedder,
                overrideScmInstallService = scmInstallationService,
                overrideApiKeyPersistence = apiKeyPersistence,
                overrideRepoComputeService = repoComputeService,
                // prevent unexpected RPC while running tests
                overrideScmAppDelegate = mock(),
                overrideScmRepoDelegate = mock(),
                overrideScmUserDelegate = mock(),
            )
            install(DatabaseContextPlugin) {
                database = <EMAIL>
            }
        }
        val client = createClient {
            when (authContext) {
                ApiAuthContext.Unauthenticated -> {
                    doNothing()
                }

                is ApiAuthContext.Authenticated -> {
                    install(Auth) {
                        bearer {
                            loadTokens {
                                BearerTokens(
                                    accessToken = jwt.generateAuthToken(
                                        identityId = authContext.identityId.value.required(),
                                        personId = authContext.personId.value.required(),
                                        tokenChainId = UUID.randomUUID(),
                                        orgIds = authContext.orgIds.map { it.value }.toSet(),
                                        orgsMvra = authContext.orgsMvra,
                                        orgsAuthReq = authContext.orgsAuthReq,
                                        readOnly = authContext.readOnly,
                                    ),
                                    refreshToken = "",
                                )
                            }
                        }
                    }
                }
            }
            this.install(ContentNegotiation) {
                installJsonSerializer()
            }
            defaultRequest {
                contentType(ContentType.Application.Json)

                // Add SkipCORS bypass for local and CI testing. The trick is the non-standard scheme. See local.conf
                headers["Origin"] = "test://localhost"
            }
            install(HttpRequestRetry)
            expectSuccess = false
        }
        block(client)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.getPersonV2
     */
    fun getPerson(block: suspend HttpResponse.() -> Unit) = client {
        val response = get("$basePath/personV2")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.getPersonEmailPreferences
     */
    fun getPersonEmailPreferences(
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/person/emailPreferences")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.updatePersonEmailPreferences
     */
    fun updatePersonEmailPreferences(
        emailPreferences: EmailPreferences,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/person/emailPreferences") {
            setBody(emailPreferences)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.getPersonPreferences
     */
    fun getPersonPreferences(
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/person/preferences")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.updatePersonPreferences
     */
    fun updatePersonPreferences(
        personPreferences: PersonPreferences,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = patch("$basePath/person/preferences") {
            setBody(personPreferences)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.getOnboardingStatus
     */
    fun getOnboardingStatusV2(
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/person/onboardingStatusV2") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PersonsApi
     * @see com.nextchaptersoftware.api.PersonsApiDelegateInterface.updateOnboardingStatus
     */
    fun updateOnboardingStatus(
        request: OnboardingStatusUpdate,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = patch("$basePath/person/onboardingStatus") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
            setBody(request)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.getThread
     */
    fun getThread(
        orgId: OrgId,
        threadId: ThreadId,
        ifModifiedSince: Instant? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/${orgId.value}/threads/$threadId") {
            ifModifiedSince?.let {
                header(CustomHeaders.IF_MODIFIED_SINCE, it.toString())
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.fetchThreads
     */
    fun fetchThreads(
        orgId: OrgId,
        threadIds: List<ThreadId>,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/${orgId.value}/threads") {
            setBody(threadIds)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamsApi
     * @see com.nextchaptersoftware.api.TeamsApiDelegateInterface.getTeams
     */
    fun getTeams(block: suspend HttpResponse.() -> Unit) = client {
        val response = get("$basePath/teams")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamMembersApi
     * @see com.nextchaptersoftware.api.TeamMembersApiDelegateInterface.listTeamMembers
     */
    fun listTeamMembers(
        orgId: OrgId,
        limit: Int? = null,
        role: TeamMemberRole? = null,
        roles: Set<TeamMemberRole>? = null,
        accountType: List<ApiProvider>? = null,
        after: String? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/membersV4") {
            limit?.also { parameter("limit", limit) }
            after?.also { parameter("after", after) }
            role?.also { parameter("role", role.enumValue) }
            roles?.forEach { parameter("roles", it.enumValue) }
            accountType.orEmpty().forEach { parameter("accountType", it.toString()) }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamMembersApi
     * @see com.nextchaptersoftware.api.TeamMembersApiDelegateInterface.fetchTeamMembers
     */
    fun fetchTeamMembers(
        orgId: OrgId,
        memberIds: List<UUID>,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/members") {
            setBody(FetchTeamMembersRequest(ids = memberIds))
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamMembersApi
     * @see com.nextchaptersoftware.api.TeamMembersApiDelegateInterface.getBotMember
     */
    fun getBotMember(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/members/bot")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ReposApi
     * @see com.nextchaptersoftware.api.ReposApiDelegateInterface.getRepos
     */
    fun getRepos(
        orgId: OrgId,
        ifModifiedSince: Instant? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/repos") {
            ifModifiedSince?.let {
                header(CustomHeaders.IF_MODIFIED_SINCE, it.toString())
            }
        }
        block(response)
    }

    fun discoverRepo(
        findRepoRequest: DiscoverRepoRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/repo") {
            setBody(findRepoRequest)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SCMInstallationsApi
     * @see com.nextchaptersoftware.api.SCMInstallationsApiDelegateInterface.findInstallationsAndRepos
     */
    fun findInstallationsAndRepos(
        installRequests: QueryInstallationRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/installationsV2") {
            setBody(installRequests)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SearchApi
     * @see com.nextchaptersoftware.api.SearchApiDelegateInterface.semanticSearchFile
     */
    fun semanticSearchFile(
        orgId: OrgId,
        semanticSearchFileRequest: SemanticSearchFileRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/search/semanticSearchFile") {
            setBody(semanticSearchFileRequest)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.IntegrationsApi
     * @see com.nextchaptersoftware.api.IntegrationsApiDelegateInterface.listIntegrationsV2
     */
    fun listIntegrationsV2(
        orgId: OrgId,
        redirectUrl: String? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/integrationsV2") {
            redirectUrl?.let {
                parameter("redirectUrl", redirectUrl)
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.InstallationsApi
     * @see com.nextchaptersoftware.api.InstallationsApiDelegateInterface.listIntegrationInstallationsV3
     */
    fun listIntegrationInstallationsV3(
        orgId: OrgId,
        provider: Provider? = null,
        ifModifiedSince: Instant? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/installationsV3") {
            provider?.let {
                parameter("provider", it.asApiModel().enumValue)
            }
            ifModifiedSince?.also {
                header(CustomHeaders.IF_MODIFIED_SINCE, it.toString())
            }
        }
        block(response)
    }

    fun dismissIntegrationConnection(
        orgId: OrgId,
        installationId: InstallationId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/installationsV2/$installationId/dismiss")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.getSlackTeams
     */
    fun getSlackTeams(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/slack")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.getRecommendedSlackChannels
     */
    fun getRecommendedSlackChannels(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/slack/$slackTeamId/channels/recommended")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.searchSlackChannels
     */
    fun searchSlackChannels(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        request: SearchSlackChannelsRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/slack/$slackTeamId/channels/search") {
            setBody(request)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.getSlackConfigurationV3
     */
    fun getSlackConfigurationV3(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/slack/$slackTeamId/configurationV3")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.getSlackConfigurationV4
     */
    fun getSlackConfigurationV4(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/slack/$slackTeamId/configurationV4")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.postSlackConfigurationV3
     */
    fun postSlackConfigurationV3(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        request: SlackConfigurationV3,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/slack/$slackTeamId/configurationV3") {
            setBody(request)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.SlackApi
     * @see com.nextchaptersoftware.api.SlackApiDelegateInterface.postSlackConfigurationV4
     */
    fun postSlackConfigurationV4(
        orgId: OrgId,
        slackTeamId: SlackTeamId,
        request: SlackConfigurationV4,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/slack/$slackTeamId/configurationV4") {
            setBody(request)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.getThreadsForMe
     */
    fun getThreadsForMe(
        orgId: OrgId,
        ifModifiedSince: Instant? = null,
        limit: Int? = null,
        repoIds: List<RepoId> = emptyList(),
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val path = "$basePath/teams/${orgId.value}/threads/mine"
        val response = get(path) {
            limit?.also {
                parameter("limit", it)
            }
            if (repoIds.isNotEmpty()) {
                parameter("repoIds", commaSeparatedList(repoIds))
            }
            ifModifiedSince?.also {
                header(CustomHeaders.IF_MODIFIED_SINCE, it.toString())
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.getQuestions
     */
    fun getQuestions(
        orgId: OrgId,
        limit: Int? = null,
        teamMemberIds: List<UUID> = emptyList(),
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val path = "$basePath/teams/${orgId.value}/questions"
        val response = get(path) {
            limit?.also {
                parameter("limit", it)
            }
            teamMemberIds.nullIfEmpty()?.also {
                parameter("teamMemberIds", commaSeparatedList(it))
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.getThreadsArchived
     */
    fun getThreadsArchived(
        orgId: OrgId,
        limit: Int? = null,
        repoIds: List<RepoId>,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val path = "$basePath/teams/${orgId.value}/threads/archived"
        val response = get(path) {
            limit?.also {
                parameter("limit", it)
            }
            if (repoIds.isNotEmpty()) {
                parameter("repoIds", commaSeparatedList(repoIds))
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MessagesApi
     * @see com.nextchaptersoftware.api.MessagesApiDelegateInterface.createMessageV3
     */
    fun createMessageV3(
        orgId: OrgId,
        createMessageRequest: CreateMessageRequestV3,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/${orgId.value}/messagesV3/${createMessageRequest.id}") {
            setBody(createMessageRequest)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MessagesApi
     * @see com.nextchaptersoftware.api.MessagesApiDelegateInterface.updateMessageV3
     */
    fun updateMessageV3(
        orgId: OrgId,
        updateMessageRequest: UpdateMessageRequestV3,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/messagesV3/${updateMessageRequest.id}") {
            setBody(updateMessageRequest)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MessagesApi
     * @see com.nextchaptersoftware.api.MessagesApiDelegateInterface.deleteMessage
     */
    fun deleteMessage(
        orgId: OrgId,
        messageId: MessageId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = delete("$basePath/teams/$orgId/messages/$messageId")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MessagesApi
     * @see com.nextchaptersoftware.api.MessagesApiDelegateInterface.updateMessageFeedback
     */
    fun updateMessageFeedback(
        orgId: OrgId,
        messageId: MessageId,
        updateMessageFeedbackRequest: UpdateMessageFeedbackRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = patch("$basePath/teams/$orgId/messages/$messageId/feedback") {
            setBody(updateMessageFeedbackRequest)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.createThreadV3
     */
    fun createThreadV3(
        orgId: OrgId,
        createThreadRequest: CreateThreadRequestV3,
        threadId: ThreadId,
        block: suspend HttpResponse.() -> Unit,
    ) {
        client {
            val response = post("$basePath/teams/${orgId.value}/threadsV3/$threadId") {
                setBody(createThreadRequest)
            }
            block(response)
        }
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.updateThread
     */
    fun updateThread(
        orgId: OrgId,
        updateThreadRequest: UpdateThreadRequest,
        threadId: ThreadId,
        block: suspend HttpResponse.() -> Unit,
    ) {
        client {
            val response = put("$basePath/teams/${orgId.value}/threads/$threadId") {
                setBody(updateThreadRequest)
            }
            block(response)
        }
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.updateThreadPrivacy
     */
    fun updateThreadPrivacy(
        orgId: OrgId,
        updateThreadPrivacyRequest: UpdateThreadPrivacyRequest,
        threadId: ThreadId,
        block: suspend HttpResponse.() -> Unit,
    ) {
        client {
            val response = put("$basePath/teams/${orgId.value}/threads/$threadId/private") {
                setBody(updateThreadPrivacyRequest)
            }
            block(response)
        }
    }

    /**
     * @see com.nextchaptersoftware.api.SourceMarksApi
     * @see com.nextchaptersoftware.api.SourceMarksApiDelegateInterface.getRepoSourceMarks
     */
    fun getRepoSourceMarks(
        orgId: OrgId,
        repoId: RepoId,
        ifModifiedSince: String? = null,
        block: suspend HttpResponse.() -> Unit,
    ) =
        client {
            val response = get("$basePath/teams/$orgId/repos/$repoId/sourcemarks") {
                ifModifiedSince?.let {
                    header(CustomHeaders.IF_MODIFIED_SINCE, it)
                }
            }
            block(response)
        }

    /**
     * @see com.nextchaptersoftware.api.UnreadsApi
     * @see com.nextchaptersoftware.api.UnreadsApiDelegateInterface.getUnreads
     */
    fun getUnreads(
        orgId: OrgId,
        repoIds: List<RepoId>,
        ifModifiedSince: Instant? = null,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/unreads") {
            if (repoIds.isNotEmpty()) {
                parameter("repoIds", commaSeparatedList(repoIds))
            }
            ifModifiedSince?.let {
                header(CustomHeaders.IF_MODIFIED_SINCE, it.toString())
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.UnreadsApi
     * @see com.nextchaptersoftware.api.UnreadsApiDelegateInterface.updateThreadUnread
     */
    fun updateThreadUnread(
        orgId: OrgId,
        threadId: ThreadId,
        latestReadMessageId: MessageId?,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/threads/$threadId/unread") {
            setBody(UpdateThreadUnreadRequest(latestReadMessage = latestReadMessageId?.value))
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.UnreadsApi
     * @see com.nextchaptersoftware.api.UnreadsApiDelegateInterface.clearUnreads
     */
    fun clearUnreads(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = delete("$basePath/teams/$orgId/unreads")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamMembersApi
     * @see com.nextchaptersoftware.api.TeamMembersApiDelegateInterface.listTeamMembers
     */
    fun testAuthentication(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/membersV4") {
            parameter("includeBot", true)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ThreadsApi
     * @see com.nextchaptersoftware.api.ThreadsApiDelegateInterface.searchThreads
     */
    fun searchThreads(
        orgId: OrgId,
        repoIds: List<RepoId> = emptyList(),
        q: String,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/${orgId.value}/threads/search") {
            parameter("q", q)
            repoIds.forEach { repoId -> parameter("repoIds", repoId.toString()) }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.InsightsApi
     * @see com.nextchaptersoftware.api.InsightsApiDelegateInterface.searchInsights
     */
    fun searchInsights(
        orgId: OrgId,
        body: SearchInsightsRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/insights/search") {
            setBody(body)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.InsightsApi
     * @see com.nextchaptersoftware.api.InsightsApiDelegateInterface.getInsightCountsV2
     */
    fun getInsightCounts(
        orgId: OrgId,
        body: InsightCountsRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/insights/countsV2") {
            setBody(body)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PullRequestsApi
     * @see com.nextchaptersoftware.api.PullRequestsApiDelegateInterface.fetchPullRequests
     */
    fun fetchPullRequests(
        orgId: OrgId,
        prIds: List<PullRequestId>,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/${orgId.value}/pullRequests") {
            setBody<List<PullRequestId>>(prIds)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PullRequestsApi
     * @see com.nextchaptersoftware.api.PullRequestsApiDelegateInterface.getPullRequestsForCommits
     */
    fun getPullRequestsForCommits(
        orgId: OrgId,
        repoId: RepoId,
        commitHashes: List<String>,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/pullRequestsForCommits") {
            setBody(PullRequestsForCommitsRequest(repoId = repoId.value, commitHashes = commitHashes))
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PullRequestsApi
     * @see com.nextchaptersoftware.api.PullRequestsApiDelegateInterface.getPullRequestInfo
     */
    fun getPullRequestInfo(
        orgId: OrgId,
        pullRequestId: PullRequestId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/pullRequests/$pullRequestId/info")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.PullRequestsApi
     * @see com.nextchaptersoftware.api.PullRequestsApiDelegateInterface.deletePullRequestBlock
     */
    fun deletePullRequestBlock(
        orgId: OrgId,
        pullRequestId: PullRequestId,
        pullRequestBlockId: PullRequestCommentId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = delete("$basePath/teams/$orgId/pullRequests/$pullRequestId/blocks/$pullRequestBlockId")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.VersionsApi
     * @see com.nextchaptersoftware.api.VersionsApiDelegateInterface.getLatestVersionInfo
     */
    fun getLatestVersionInfo(
        productAgent: String?,
        productSha: String?,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/versionInfo/latest") {
            productAgent?.let {
                header(CustomHeaders.PRODUCT_AGENT, it)
            }
            productSha?.let {
                header(CustomHeaders.PRODUCT_SHA, it)
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.VersionsApi
     * @see com.nextchaptersoftware.api.VersionsApiDelegateInterface.getLatestPublicVersionInfo
     */
    fun getLatestPublicVersionInfo(
        productAgent: String?,
        productSha: String?,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/versionInfo/public") {
            productAgent?.let {
                header(CustomHeaders.PRODUCT_AGENT, it)
            }
            productSha?.let {
                header(CustomHeaders.PRODUCT_SHA, it)
            }
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MetricsApi
     * @see com.nextchaptersoftware.api.MetricsApiDelegateInterface.viewThread
     */
    fun putViewThread(
        orgId: OrgId,
        threadId: ThreadId,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/threads/$threadId/viewed") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MetricsApi
     * @see com.nextchaptersoftware.api.MetricsApiDelegateInterface.viewPullRequest
     */
    fun putViewPullRequest(
        orgId: OrgId,
        pullRequestId: PullRequestId,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/pullRequests/$pullRequestId/viewed") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MetricsApi
     * @see com.nextchaptersoftware.api.MetricsApiDelegateInterface.viewContent
     */
    fun viewContent(
        orgId: OrgId,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/viewContent") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MetricsApi
     * @see com.nextchaptersoftware.api.MetricsApiDelegateInterface.viewIdeSidebar
     */
    fun viewIdeSidebar(
        orgId: OrgId,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/viewIdeSidebar") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.MetricsApi
     * @see com.nextchaptersoftware.api.MetricsApiDelegateInterface.viewIdeInsights
     */
    fun viewIdeInsights(
        orgId: OrgId,
        productAgent: AgentType,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = put("$basePath/teams/$orgId/viewIdeInsights") {
            header(CustomHeaders.PRODUCT_AGENT, productAgent)
        }
        block(response)
    }

    private fun <ID : Comparable<ID>> commaSeparatedList(ids: List<ID>): String? {
        return when {
            ids.isEmpty() -> null
            else -> ids.joinToString(",") { it.toString() }
        }
    }

    /**
     * @see com.nextchaptersoftware.api.WebIngestionApi
     * @see com.nextchaptersoftware.api.WebIngestionApiDelegateInterface.getWebIngestions
     */
    fun getWebIngestions(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/webIngestions")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.WebIngestionApi
     * @see com.nextchaptersoftware.api.WebIngestionApiDelegateInterface.getWebIngestionConfiguration
     */
    fun getWebIngestionConfiguration(
        orgId: OrgId,
        webIngestionId: UUID,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/webIngestions/$webIngestionId/configuration")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.WebIngestionApi
     * @see com.nextchaptersoftware.api.WebIngestionApiDelegateInterface.patchWebIngestionConfiguration
     */
    fun patchWebIngestionConfiguration(
        orgId: OrgId,
        webIngestionId: UUID,
        body: UpdateWebIngestionConfigurationRequest,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = patch("$basePath/teams/$orgId/webIngestions/$webIngestionId/configuration") {
            setBody(body)
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.TeamsApi
     * @see com.nextchaptersoftware.api.TeamsApiDelegateInterface.getTeamStats
     */
    fun getTeamStats(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/teamStats")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.KeysApi
     * @see com.nextchaptersoftware.api.KeysApiDelegateInterface.getApiKeys
     */
    fun getApiKeys(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/keys")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.KeysApi
     * @see com.nextchaptersoftware.api.KeysApiDelegateInterface.createApiKey
     */
    fun createApiKey(
        orgId: OrgId,
        name: String,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = post("$basePath/teams/$orgId/keys") {
            setBody(CreateApiKeyRequest(name = name))
        }
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.KeysApi
     * @see com.nextchaptersoftware.api.KeysApiDelegateInterface.deleteApiKey
     */
    fun deleteApiKey(
        orgId: OrgId,
        apiKeyId: OrgApiKeyId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = delete("$basePath/teams/$orgId/keys/$apiKeyId")
        block(response)
    }

    fun listSsoProviders(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/ssoProviders")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ConfigApi
     * @see com.nextchaptersoftware.api.ConfigApiDelegateInterface.getGlobalConfig
     */
    fun getGlobalConfig(
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/configs")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ConfigApi
     * @see com.nextchaptersoftware.api.ConfigApiDelegateInterface.getMetaConfig
     */
    fun getMetaConfig(
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/meta")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ConfigApi
     * @see com.nextchaptersoftware.api.ConfigApiDelegateInterface.getFeatureSettings
     */
    fun getFeatureSettings(
        orgId: OrgId,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = get("$basePath/teams/$orgId/settings")
        block(response)
    }

    /**
     * @see com.nextchaptersoftware.api.ConfigApi
     * @see com.nextchaptersoftware.api.ConfigApiDelegateInterface.patchFeatureSettings
     */
    fun patchFeatureSettings(
        orgId: OrgId,
        body: FeatureSettings,
        block: suspend HttpResponse.() -> Unit,
    ) = client {
        val response = patch("$basePath/teams/$orgId/settings") {
            setBody(body)
        }
        block(response)
    }
}
