package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.models.InstallationRequestItem
import com.nextchaptersoftware.api.models.InstallationsResponse
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.models.QueryInstallationRequest
import com.nextchaptersoftware.api.services.install.ScmInstallationDelegateInterface
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.apiservice.test.utils.ApiAuthContext
import com.nextchaptersoftware.apiservice.test.utils.UnblockedApiClient
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makeRepo
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.common.getDatabase
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.personId
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.scm.ScmNoAuthApiFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import com.nextchaptersoftware.scm.models.ScmOrg
import io.ktor.client.call.body
import io.ktor.http.HttpStatusCode
import kotlinx.coroutines.currentCoroutineContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.eq

class InstallationsApiDelegateImplTest : DatabaseTestsBase() {
    private lateinit var client: UnblockedApiClient
    private lateinit var identityId: IdentityId
    private lateinit var org: OrgDAO
    private lateinit var scmTeam: ScmTeamDAO
    private lateinit var scmUserApi: ScmUserApi

    suspend fun setup() {
        val identity = makeIdentity(person = makePerson())
        identityId = identity.id.value
        org = makeOrg(
            displayName = "Next Chapter Software Inc",
        )
        scmTeam = makeScmTeam(
            org = org,
            displayName = "Next Chapter Software Inc",
            providerDisplayName = "Next Chapter Software Inc",
        )
        makeMember(scmTeam = scmTeam, identity = identity)

        val scmInstallationDelegate = mock<ScmInstallationDelegateInterface>()
        scmUserApi = mock()

        `when`(
            scmInstallationDelegate.getAuthorizedInstallations(
                scm = any(),
                identity = eq(identity.asDataModel()),
            ),
        ).thenReturn(
            listOf(
                ScmInstallationAccount(
                    account = ScmAccount.Org(
                        ScmOrg(
                            avatarUrl = "https://avatars.githubusercontent.com/u/********?v=4".asUrl,
                            displayName = "Next Chapter Software Inc",
                            externalId = "********",
                            fullPath = "NextChapterSoftware",
                            htmlUrl = "https://github.com/NextChapterSoftware/unblocked.git".asUrl,
                            login = "NextChapterSoftware",
                        ),
                    ),
                    allReposAccessible = false,
                    externalInstallationId = "3430433",
                    supportsCI = true,
                ),
            ),
        )

        val scmInstallationService = ScmInstallationService(
            scmInstallationDelegate = scmInstallationDelegate,
            scmWebFactory = ScmWebFactory(ScmConfig.INSTANCE),
            scmNoAuthApiFactory = ScmNoAuthApiFactory(ScmConfig.INSTANCE),
        )

        client = UnblockedApiClient(
            database = currentCoroutineContext().getDatabase(),
            authContext = ApiAuthContext.Authenticated(
                identityId = identity.id.value,
                personId = checkNotNull(identity.personId),
                orgIds = setOf(org.idValue),
            ),
            scmInstallationService = scmInstallationService,
        )
    }

    @Suppress("LongMethod")
    @Test
    fun getInstallations() = suspendingDatabaseTest {
        setup()
        makeRepo(
            scmTeam = scmTeam,
            provider = Provider.GitHub,
            httpUrl = "https://github.com/NextChapterSoftware/unblocked.git",
            externalName = "unblocked",
            externalOwner = "NextChapterSoftware",
            isScmConnected = true,
        )

        client.findInstallationsAndRepos(
            QueryInstallationRequest(
                requests = listOf(
                    InstallationRequestItem("**************:NextChapterSoftware/unblocked.git"),
                    InstallationRequestItem("https://github.com/octokit/graphql-schema"),
                ),
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val installationsResponse: InstallationsResponse = body()
            assertThat(installationsResponse.title).isEqualTo("Install the Unblocked GitHub App")
            assertThat(installationsResponse.description).isEqualTo(
                "Connect the following repositories to see insights from pull requests and code conversations for this project.",
            )

            val installationGroups = installationsResponse.installationGroups
            assertThat(installationGroups).hasSize(1)

            val installationGroup = installationGroups.first()
            assertThat(installationGroup.provider).isEqualTo(ApiProvider.github)
            assertThat(installationGroup.enterpriseProviderId).isNull()

            val installationAndRepos = installationGroup.installationsAndRepos
            assertThat(installationAndRepos).hasSize(2)

            installationAndRepos.first { it.installation.isInstalled }.also {
                assertThat(it.installation.teamId).isEqualTo(scmTeam.orgId.value)
                assertThat(it.installation.provider).isEqualTo(ApiProvider.github)
                assertThat(it.installation.displayName).isEqualTo("Next Chapter Software Inc")
                assertThat(it.installation.installUrl).isNotNull
                assertThat(it.installation.avatarUrl).isNotEmpty

                assertThat(it.reposNotInstalled).isEmpty()
                assertThat(it.reposInstalled).hasSize(1)
                it.reposInstalled.first().also { repo ->
                    assertThat(repo.provider).isEqualTo(ApiProvider.github)
                    assertThat(repo.fullName).isEqualTo("NextChapterSoftware/unblocked")
                    assertThat(repo.ownerName).isEqualTo("NextChapterSoftware")
                    assertThat(repo.repoName).isEqualTo("unblocked")
                    assertThat(repo.httpUrl).isEqualTo("https://github.com/NextChapterSoftware/unblocked")
                    assertThat(repo.sshUrl).isEqualTo("ssh://github.com/NextChapterSoftware/unblocked.git")
                    assertThat(repo.scpUrl).isEqualTo("**************:NextChapterSoftware/unblocked.git")
                    assertThat(repo.isEmpty).isFalse()
                    assertThat(repo.hasCompletedProcessing).isFalse()
                }
            }

            installationAndRepos.first { !it.installation.isInstalled }.also {
                assertThat(it.installation.teamId).isNull()
                assertThat(it.installation.provider).isEqualTo(ApiProvider.github)
                assertThat(it.installation.displayName).startsWith("Octokit")
                assertThat(it.installation.installUrl).isEqualTo(
                    "https://github.com/apps/local-un-blocked/installations/new/permissions?target_id=3430433",
                )
                assertThat(it.installation.avatarUrl).isEqualTo("https://avatars.githubusercontent.com/u/3430433?v=4")

                assertThat(it.reposInstalled).isEmpty()
                assertThat(it.reposNotInstalled).hasSize(1)
                it.reposNotInstalled.first().also { repo ->
                    assertThat(repo.fullName).isEqualTo("octokit/graphql-schema")
                    assertThat(repo.ownerName).isEqualTo("octokit")
                    assertThat(repo.repoName).isEqualTo("graphql-schema")
                    assertThat(repo.httpUrl).isEqualTo("https://github.com/octokit/graphql-schema")
                    assertThat(repo.sshUrl).isEqualTo("ssh://github.com/octokit/graphql-schema.git")
                    assertThat(repo.scpUrl).isEqualTo("**************:octokit/graphql-schema.git")
                }
            }
        }
    }

    @Test
    fun `getInstallations case insensitive`() = suspendingDatabaseTest {
        setup()
        makeRepo(
            scmTeam = scmTeam,
            provider = Provider.GitHub,
            httpUrl = "https://github.com/NextChapterSoftware/unblocked.git",
            externalName = "unblocked",
            externalOwner = "NextChapterSoftware",
            isScmConnected = true,
        )

        client.findInstallationsAndRepos(
            QueryInstallationRequest(
                requests = listOf(
                    InstallationRequestItem("**************:nextchaptersoftware/Unblocked.git"),
                ),
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val installationsResponse: InstallationsResponse = body()
            assertThat(installationsResponse.title).isEqualTo("Install the Unblocked GitHub App")
            assertThat(installationsResponse.description).isEqualTo(
                "Connect the following repositories to see insights from pull requests and code conversations for this project.",
            )

            val installationGroups = installationsResponse.installationGroups
            assertThat(installationGroups).hasSize(1)

            val installationGroup = installationGroups.first()
            assertThat(installationGroup.provider).isEqualTo(ApiProvider.github)
            assertThat(installationGroup.enterpriseProviderId).isNull()
            assertThat(installationGroup.installationsAndRepos).hasSize(1)

            val installationAndRepos = installationGroup.installationsAndRepos
            assertThat(installationAndRepos).hasSize(1)

            val installationAndRepo = installationAndRepos.first()

            installationAndRepo.installation.also {
                assertThat(it.isInstalled).isTrue
                assertThat(it.teamId).isEqualTo(scmTeam.orgId.value)
                assertThat(it.provider).isEqualTo(ApiProvider.github)
                assertThat(it.displayName).startsWith("Next Chapter Software Inc")
                assertThat(it.installUrl).isNotNull
                assertThat(it.avatarUrl).isNotEmpty
            }
            assertThat(installationAndRepo.reposNotInstalled).isEmpty()
            assertThat(installationAndRepo.reposInstalled).hasSize(1)
            installationAndRepo.reposInstalled.first().also { repo ->
                assertThat(repo.provider).isEqualTo(ApiProvider.github)
                assertThat(repo.fullName).isEqualTo("NextChapterSoftware/unblocked")
                assertThat(repo.ownerName).isEqualTo("NextChapterSoftware")
                assertThat(repo.repoName).isEqualTo("unblocked")
                assertThat(repo.httpUrl).isEqualTo("https://github.com/NextChapterSoftware/unblocked")
                assertThat(repo.sshUrl).isEqualTo("ssh://github.com/NextChapterSoftware/unblocked.git")
                assertThat(repo.scpUrl).isEqualTo("**************:NextChapterSoftware/unblocked.git")
                assertThat(repo.isEmpty).isFalse()
                assertThat(repo.hasCompletedProcessing).isFalse()
            }
        }
    }

    @Test
    fun `getInstallations unknown personal org`() = suspendingDatabaseTest {
        setup()
        makeRepo(
            scmTeam = scmTeam,
            provider = Provider.GitHub,
            httpUrl = "https://github.com/NextChapterSoftware/unblocked.git",
            externalName = "unblocked",
            externalOwner = "NextChapterSoftware",
            isScmConnected = true,
        )

        val requestedUrl = "**************:nat/reeepo.git"

        client.findInstallationsAndRepos(
            QueryInstallationRequest(
                requests = listOf(
                    InstallationRequestItem(requestedUrl),
                ),
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val installationsResponse: InstallationsResponse = body()
            assertThat(installationsResponse.title).isEqualTo("Install the Unblocked GitHub App")
            assertThat(installationsResponse.description).isEqualTo(
                "Connect the following repositories to see insights from pull requests and code conversations for this project.",
            )

            val installationGroups = installationsResponse.installationGroups
            assertThat(installationGroups).hasSize(1)

            val installationGroup = installationGroups.first()
            assertThat(installationGroup.provider).isEqualTo(ApiProvider.github)
            assertThat(installationGroup.enterpriseProviderId).isNull()
            assertThat(installationGroup.installationsAndRepos).hasSize(1)

            val installationAndRepos = installationGroup.installationsAndRepos
            assertThat(installationAndRepos).hasSize(1)

            val installationAndRepo = installationAndRepos.first()

            installationAndRepo.installation.also {
                assertThat(it.isInstalled).isFalse()
                assertThat(it.teamId).isNull()
                assertThat(it.provider).isEqualTo(ApiProvider.github)
                assertThat(it.displayName).isEqualTo("Nat Friedman")
                assertThat(it.installUrl).isEqualTo(
                    "https://github.com/apps/local-un-blocked/installations/new/permissions?target_id=56260",
                )
                assertThat(it.avatarUrl).isEqualTo("https://avatars.githubusercontent.com/u/56260?v=4")
            }
            assertThat(installationAndRepo.reposInstalled).isEmpty()
            assertThat(installationAndRepo.reposNotInstalled).hasSize(1)
            installationAndRepo.reposNotInstalled.first().also { repo ->
                assertThat(repo.fullName).isEqualTo("nat/reeepo")
                assertThat(repo.ownerName).isEqualTo("nat")
                assertThat(repo.repoName).isEqualTo("reeepo")
                assertThat(repo.httpUrl).isEqualTo("https://github.com/nat/reeepo")
                assertThat(repo.sshUrl).isEqualTo("ssh://github.com/nat/reeepo.git")
                assertThat(repo.scpUrl).isEqualTo("**************:nat/reeepo.git")
            }
        }
    }

    @Test
    fun `getInstallations unknown org`() = suspendingDatabaseTest {
        setup()

        makeRepo(
            scmTeam = scmTeam,
            provider = Provider.GitHub,
            httpUrl = "https://github.com/NextChapterSoftware/unblocked.git",
            externalName = "unblocked",
            externalOwner = "NextChapterSoftware",
            isScmConnected = true,
        )

        val requestedUrl = "**************:octokit/poop.git"

        client.findInstallationsAndRepos(
            QueryInstallationRequest(
                requests = listOf(
                    InstallationRequestItem(requestedUrl),
                ),
            ),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val installationsResponse: InstallationsResponse = body()
            assertThat(installationsResponse.title).isEqualTo("Install the Unblocked GitHub App")
            assertThat(installationsResponse.description).isEqualTo(
                "Connect the following repositories to see insights from pull requests and code conversations for this project.",
            )

            val installationGroups = installationsResponse.installationGroups
            assertThat(installationGroups).hasSize(1)

            val installationGroup = installationGroups.first()
            assertThat(installationGroup.provider).isEqualTo(ApiProvider.github)
            assertThat(installationGroup.enterpriseProviderId).isNull()
            assertThat(installationGroup.installationsAndRepos).hasSize(1)

            val installationAndRepos = installationGroup.installationsAndRepos
            assertThat(installationAndRepos).hasSize(1)

            val installationAndRepo = installationAndRepos.first()

            installationAndRepo.installation.also {
                assertThat(it.isInstalled).isFalse()
                assertThat(it.teamId).isNull()
                assertThat(it.provider).isEqualTo(ApiProvider.github)
                assertThat(it.displayName).isEqualTo("Octokit")
                assertThat(it.installUrl).isEqualTo(
                    "https://github.com/apps/local-un-blocked/installations/new/permissions?target_id=3430433",
                )
                assertThat(it.avatarUrl).isEqualTo("https://avatars.githubusercontent.com/u/3430433?v=4")
            }
            assertThat(installationAndRepo.reposInstalled).isEmpty()
            assertThat(installationAndRepo.reposNotInstalled).hasSize(1)
            installationAndRepo.reposNotInstalled.first().also { repo ->
                assertThat(repo.fullName).isEqualTo("octokit/poop")
                assertThat(repo.ownerName).isEqualTo("octokit")
                assertThat(repo.repoName).isEqualTo("poop")
                assertThat(repo.httpUrl).isEqualTo("https://github.com/octokit/poop")
                assertThat(repo.sshUrl).isEqualTo("ssh://github.com/octokit/poop.git")
                assertThat(repo.scpUrl).isEqualTo("**************:octokit/poop.git")
            }
        }
    }
}
