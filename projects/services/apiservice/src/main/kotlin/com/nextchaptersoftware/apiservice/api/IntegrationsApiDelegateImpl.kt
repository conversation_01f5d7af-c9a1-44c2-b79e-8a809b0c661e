package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.IntegrationsApiDelegateInterface
import com.nextchaptersoftware.api.integration.converters.asApiModel
import com.nextchaptersoftware.api.integration.services.IntegrationService
import com.nextchaptersoftware.api.models.Integration
import com.nextchaptersoftware.models.identityId
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.personId
import io.ktor.http.Url
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class IntegrationsApiDelegateImpl(
    private val integrationService: IntegrationService,
) : IntegrationsApiDelegateInterface {

    override suspend fun listIntegrationsV2(context: RoutingContext, input: Resources.listIntegrationsV2): List<Integration> {
        val orgId = context.orgId
        val personId = context.personId()

        return integrationService.listIntegrations(
            orgId = orgId,
            identityId = context.identityId,
            personId = personId,
            overrideAuthRedirectUrl = input.overrideRedirectUrl?.let { Url(it) },
            dataIntegrationRedirectUrl = input.dataIntegrationRedirectUrl?.let { Url(it) },
            clientState = input.clientState,
        ).map {
            it.asApiModel()
        }
    }
}
