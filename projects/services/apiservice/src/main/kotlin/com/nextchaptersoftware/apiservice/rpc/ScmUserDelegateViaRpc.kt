package com.nextchaptersoftware.apiservice.rpc

import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.ScmUserCalls
import com.nextchaptersoftware.scm.delegates.ScmUserDelegate
import com.nextchaptersoftware.scm.models.ScmInstallationAccount

internal class ScmUserDelegateViaRpc : ScmUserDelegate {

    override suspend fun getScmInstallationAccount(
        orgId: OrgId?,
        identity: Identity,
        externalInstallationId: String,
    ): ScmInstallationAccount? {
        return RpcFacade
            .withProxyProvider()
            .forApiService()
            .use {
                it.scmUserGetScmInstallationAccount(
                    params = ScmUserCalls.ScmUserGetScmInstallationAccountParams(
                        orgId = orgId,
                        identityId = identity.id,
                        externalInstallationId = externalInstallationId,
                    ),
                )
            }
    }
}
