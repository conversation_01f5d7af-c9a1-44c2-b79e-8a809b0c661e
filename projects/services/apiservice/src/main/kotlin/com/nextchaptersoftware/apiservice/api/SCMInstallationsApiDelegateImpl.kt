package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.SCMInstallationsApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.models.InstallationAndRepos
import com.nextchaptersoftware.api.models.InstallationConnectRequest
import com.nextchaptersoftware.api.models.InstallationGroup
import com.nextchaptersoftware.api.models.InstallationV2
import com.nextchaptersoftware.api.models.InstallationsResponse
import com.nextchaptersoftware.api.models.ListInstallationsResponse
import com.nextchaptersoftware.api.models.Provider as ApiProvider
import com.nextchaptersoftware.api.models.QueryInstallationRequest
import com.nextchaptersoftware.api.models.RepoSelection
import com.nextchaptersoftware.api.models.ScmAccount as ApiScmAccount
import com.nextchaptersoftware.api.models.ScmAccountRequest
import com.nextchaptersoftware.api.models.ScmInstance
import com.nextchaptersoftware.api.models.ScmRepo
import com.nextchaptersoftware.api.models.ScmRepoChanges
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.models.converters.asApiScmRepo
import com.nextchaptersoftware.api.models.converters.asProvider
import com.nextchaptersoftware.api.services.EmailService
import com.nextchaptersoftware.api.services.install.FullyQualifiedInstallationId
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.billing.services.OrgValidation
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.billing.utils.BillingCreateEmailService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PlanCapabilityType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.Repo
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.toRepo
import com.nextchaptersoftware.db.sql.WhereExtensions.AllOp
import com.nextchaptersoftware.db.sql.WhereExtensions.whereAll
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.MemberStore
import com.nextchaptersoftware.db.stores.OrgStore
import com.nextchaptersoftware.db.stores.PersonStore
import com.nextchaptersoftware.db.stores.RepoStore
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.installationStore
import com.nextchaptersoftware.ktor.ForbiddenException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.RepoUrl
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.infoAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.maintenance.MemberMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.models.clientconfig.ClientCapabilityType
import com.nextchaptersoftware.models.identity
import com.nextchaptersoftware.models.identityOrNull
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.orgIdOrNull
import com.nextchaptersoftware.models.orgIds
import com.nextchaptersoftware.models.personId
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.delegates.ScmUserDelegate
import com.nextchaptersoftware.scm.models.ScmAccount
import com.nextchaptersoftware.scm.queue.enqueue.ScmEventProducer
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.segment.SegmentProvider
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.types.Hash
import com.nextchaptersoftware.utils.KotlinUtils.required
import io.ktor.http.HttpStatusCode
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging
import org.jetbrains.exposed.sql.JoinType
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.SqlExpressionBuilder.inList
import org.jetbrains.exposed.sql.and
import org.jetbrains.exposed.sql.selectAll
import org.openapitools.server.Resources

private val LOGGER = KotlinLogging.logger {}

class SCMInstallationsApiDelegateImpl(
    private val accessValidation: AccessValidation,
    private val billingCreateEmailService: BillingCreateEmailService,
    private val capabilityValidation: CapabilityValidation,
    private val clientConfigService: ClientConfigService,
    private val emailService: EmailService,
    private val identityStore: IdentityStore = Stores.identityStore,
    private val memberMaintenance: MemberMaintenance = MemberMaintenance(),
    private val orgStore: OrgStore = Stores.orgStore,
    private val orgValidation: OrgValidation = OrgValidation(),
    private val personStore: PersonStore = Stores.personStore,
    private val repoAccessService: RepoAccessService,
    private val repoMaintenance: RepoMaintenance,
    private val repoStore: RepoStore = Stores.repoStore,
    private val scmEventProducer: ScmEventProducer,
    private val scmInstallService: ScmInstallationService,
    private val scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    private val scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    private val scmUserDelegate: ScmUserDelegate,
    private val scmWebFactory: ScmWebFactory,
    private val segmentProvider: SegmentProvider,
    private val slackNotifier: SlackNotifier,
) : SCMInstallationsApiDelegateInterface {

    private data class RepoRequest(
        val repoUrl: RepoUrl,
        val rootHashes: List<Hash>,
    )

    override suspend fun listInstallations(
        context: RoutingContext,
        input: Resources.listInstallations,
    ): ListInstallationsResponse {
        val identity = context.identityOrNull() ?: throw NotFoundException()

        if (!identity.provider.isScmProvider && identity.provider.isSignInCapable) {
            return ListInstallationsResponse(
                provider = identity.provider.asApiModel(),
                installations = emptyList(),
                installUrl = null,
            )
        }

        check(identity.provider.isScmProvider) { "Expected SCM provider" }
        val identityScm = Scm.fromIdentity(identity)

        input.provider?.also {
            val requestedScm = Scm.fromProvider(it.asProvider(), input.enterpriseProviderId?.let(::EnterpriseAppConfigId))
            if (identityScm.uniqueSignature != requestedScm.uniqueSignature) {
                return ListInstallationsResponse(
                    provider = identity.provider.asApiModel(),
                    installations = emptyList(),
                    installUrl = null,
                )
            }
        }

        val scmTeamIds = scmTeamStore.listScmTeamIdsForOrgs(context.orgIds)
        val installations = findInstallationsForIdentity(identity, scmTeamIds)

        return ListInstallationsResponse(
            provider = identityScm.provider.asApiModel(),
            installUrl = scmInstallService.getGeneralInstallUrl(identityScm)?.asString,
            installations = installations,
        )
    }

    override suspend fun listScmAccounts(
        context: RoutingContext,
        input: Resources.listScmAccounts,
    ): List<ApiScmAccount> {
        val identity = identityStore.findById(
            identityId = input.connectingIdentity.let(::IdentityId),
        ) ?: throw ForbiddenException()

        val scm = Scm.fromProvider(input.provider.asProvider(), input.enterpriseProviderId?.let(::EnterpriseAppConfigId))
        run {
            val identityScm = Scm.fromIdentity(identity)
            require(scm.uniqueSignature == identityScm.uniqueSignature) {
                "Requested SCM does not match identity SCM"
            }
        }

        return listAvailableScmAccounts(scm, identity, input.externalInstallationId)
    }

    private suspend fun listAvailableScmAccounts(
        scm: Scm,
        identity: Identity,
        externalInstallationId: String?,
    ): List<ApiScmAccount> {
        val scmAccounts = withTimeout(10.seconds) {
            var accounts = scmInstallService.getAuthorizedInstallations(scm = scm, identity = identity)
            externalInstallationId?.also {
                while (accounts.none { it.externalInstallationId == externalInstallationId }) {
                    LOGGER.infoAsync("externalInstallationId" to externalInstallationId) { "Waiting for expected SCM account" }
                    delay(1.seconds)
                    accounts = scmInstallService.getAuthorizedInstallations(scm, identity)
                }
            }
            accounts
        }

        val teamsByExternalIds = scmTeamStore.findByProviderExternalIds(
            provider = scm.provider,
            providerEnterpriseId = scm.providerEnterpriseId,
            providerExternalIds = scmAccounts.map { it.account.externalId },
        )

        return scmAccounts
            .filterNot { it.account.externalId in teamsByExternalIds }
            .map {
                ApiScmAccount(
                    allReposAccessible = it.allReposAccessible,
                    avatarUrl = it.account.avatarUrl.asString,
                    displayName = it.account.displayName ?: it.account.login,
                    externalId = it.account.externalId,
                    externalInstallationId = it.externalInstallationId,
                    fullPath = it.account.fullPath,
                    htmlUrl = it.account.htmlUrl.asString,
                    isPersonalAccount = it.account is ScmAccount.User,
                    provider = scm.provider.asApiModel(),
                    enterpriseProviderId = scm.providerEnterpriseId?.value,
                    isCiEligible = isScmAccountCiEligible(orgId = null, provider = scm.provider),
                    needsCiScopes = !it.supportsCI,
                )
            }.also { accounts ->
                LOGGER.debugAsync("providerExternalIds" to accounts.joinToString { it.displayName }) { "Get SCM accounts for identity" }
            }
    }

    private suspend fun isScmAccountCiEligible(orgId: OrgId?, provider: Provider): Boolean {
        return when (provider) {
            Provider.GitHub, Provider.GitHubEnterprise -> {
                clientConfigService.computeMergedCapabilityForType(
                    personId = null,
                    orgId = orgId ?: OrgId.random(), // When orgId is null, only true if globally enabled.
                    type = ClientCapabilityType.FeatureConfigureCI,
                )
            }

            else -> false
        }
    }

    override suspend fun patchInstallation(
        context: RoutingContext,
        input: Resources.patchInstallation,
        body: InstallationConnectRequest,
    ): InstallationV2 {
        return upsertScmInstallation(
            context = context,
            installRepositoriesIds = body.installRepositoriesIds,
            fullyQualifiedScmInstallationId = FullyQualifiedInstallationId.fromString(input.fullyQualifiedScmInstallationKey),
        )
    }

    override suspend fun patchOrgInstallationScmRepos(
        context: RoutingContext,
        input: Resources.patchOrgInstallationScmRepos,
        body: ScmRepoChanges,
    ) {
        val orgId = context.orgId
        accessValidation.expectAdminRole(context)

        val installationId = input.installationId.let(::InstallationId)
        val scmTeam = scmTeamStore.findByInstallationId(installationId = installationId, orgId = orgId).required()

        LOGGER.infoAsync(
            "repoIdsAdded" to body.repoIdsAdded,
            "repoIdsRemoved" to body.repoIdsRemoved,
            "installationId" to input.installationId,
        ) { "patchInstallationScmRepos" }

        // deselects repos
        repoMaintenance.deselectRepos(scmTeam.id, body.repoIdsRemoved.map(::RepoId))

        // selects repos that have already been instantiated
        repoStore.selectReposByExternalId(scmTeam, body.repoIdsAdded)

        // selects repos that have not already been instantiated and need to be fetched from the SCM
        scmEventProducer.sendInstallReposEvent(
            teamId = scmTeam.id,
            additionalRepoExternalIds = body.repoIdsAdded,
        )

        installationStore.touchModifiedAt(installationId = installationId)
    }

    private suspend fun createTeam(
        identity: Identity,
        scm: Scm,
        orgId: OrgId?,
        providerExternalInstallationId: String,
        scmAccount: ScmAccount,
    ): ScmTeam {
        // Create the team
        val result = scmTeamLifecycleMaintenance.upsertTeam(
            scm = scm,
            scmAccount = scmAccount,
            orgId = orgId,
            createIfNecessary = true,
            providerExternalInstallationId = providerExternalInstallationId,
            providerInstallationValid = true,
        )

        val newTeam = result.scmTeam ?: throw ForbiddenException()

        // Must create at least one team member so that team auth is possible
        val createdByMember = memberMaintenance.upsertMember(
            orgId = newTeam.orgId,
            installationId = newTeam.installationId,
            provider = scm.provider,
            providerRole = ProviderRole.Owner,
            externalId = identity.externalId,
            displayName = identity.displayName,
            avatarUrl = identity.avatarUrl,
            htmlUrl = identity.htmlUrl,
            username = identity.username,
            externalTeamId = scm.uniqueSignature,
            isBot = identity.isBot,
            isCurrentMember = true,
            isPrimaryMember = true,
        ).asDataModel()

        scmTeamStore.updateCreatedBy(teamId = newTeam.id, createdByIdentityId = createdByMember.identityId)
        orgStore.updateCreatedBy(orgId = newTeam.orgId, createdByIdentityId = createdByMember.identityId)

        val person = personStore.findByIdentityId(
            identityId = createdByMember.identityId,
        )

        person?.also {
            segmentProvider.repositoryProviderAccessGranted(personId = it.id, provider = scm.provider)
        }

        if (result.orgCreated) {
            slackNotifier.announceNewOrg(orgId = newTeam.orgId, scmTeam = newTeam, personId = identity.person)
            emailService.sendTrialStartEmail(
                orgId = newTeam.orgId,
                orgMemberId = createdByMember.orgMemberId,
            )

            billingCreateEmailService.createEmails(
                orgId = newTeam.orgId,
            )
        }

        return newTeam
    }

    private suspend fun findInstallationsForIdentity(identity: Identity, teamIds: Collection<ScmTeamId>): List<InstallationV2> {
        val scm = Scm.fromIdentity(identity)

        val scmTeams = scmTeamStore.hydrateTeams(teamIds)
            .filter { it.provider == scm.provider }
            .filter { it.providerEnterpriseId == scm.providerEnterpriseId }

        return scmInstallService.getAuthorizedInstallations(scm, identity).map { installationAccount ->
            val scmTeam = scmTeams.firstOrNull { it.providerExternalId == installationAccount.account.externalId }
            when {
                scmTeam != null -> scmInstallService.getInstallationFromTeam(scmTeam, installationAccount.allReposAccessible)
                else -> scmInstallService.getInstallationFromScm(installationAccount, scm)
            }
        }.also { orgs ->
            LOGGER.debugAsync(
                "orgs" to orgs.joinToString { it.displayName },
                "provider" to identity.provider.displayName,
            ) { "Get orgs for identity" }
        }
    }

    override suspend fun connectScmInstallation(
        context: RoutingContext,
        input: Resources.connectScmInstallation,
        body: InstallationConnectRequest,
    ): InstallationV2 {
        return upsertScmInstallation(
            context = context,
            installRepositoriesIds = body.installRepositoriesIds,
            fullyQualifiedScmInstallationId = FullyQualifiedInstallationId.fromString(input.fullyQualifiedScmInstallationKey),
        )
    }

    private suspend fun upsertScmInstallation(
        context: RoutingContext,
        installRepositoriesIds: List<String>?,
        fullyQualifiedScmInstallationId: FullyQualifiedInstallationId,
    ): InstallationV2 {
        accessValidation.expectAdminRole(context)

        val identity = context.identityOrNull() ?: throw NotFoundException()
        val providerExternalId = fullyQualifiedScmInstallationId.installationId
        val scm = fullyQualifiedScmInstallationId.scm

        LOGGER.infoAsync(
            "installRepositoriesIds" to installRepositoriesIds,
            "fullyQualifiedScmInstallationKey" to fullyQualifiedScmInstallationId.asString,
            "providerExternalId" to providerExternalId,
        ) { "upsertScmInstallation" }

        val scmInstallationAccount = scmInstallService.getAuthorizedInstallations(scm, identity)
            .firstOrNull { it.account.externalId == providerExternalId }
            ?: throw ForbiddenException("user is not authorized for this installation")

        val scmTeamIds = scmTeamStore.listScmTeamIdsForOrgs(context.orgIds)

        val existingScmTeam = scmTeamStore.hydrateTeams(scmTeamIds)
            .filter { it.provider == scm.provider }
            .filter { it.providerEnterpriseId == scm.providerEnterpriseId }
            .firstOrNull { it.providerExternalId == providerExternalId }

        val scmTeam = existingScmTeam ?: run {
            LOGGER.infoAsync("installationId" to fullyQualifiedScmInstallationId.asString) {
                "No team for upsertScmInstallation, so creating team"
            }
            createTeam(
                identity = identity,
                scm = scm,
                orgId = null,
                providerExternalInstallationId = scmInstallationAccount.externalInstallationId,
                scmAccount = scmInstallationAccount.account,
            )
        }

        installRepositoriesIds?.also {
            // selects repos that have already been instantiated
            repoStore.selectReposByExternalId(scmTeam, installRepositoriesIds)

            // selects repos that have not already been instantiated and need to be fetched from the SCM
            scmEventProducer.sendInstallReposEvent(
                teamId = scmTeam.id,
                additionalRepoExternalIds = installRepositoriesIds,
            )
        } ?: run {
            // refresh resources immediately so that the user can see the repos they have access to
            scmEventProducer.sendRefreshResourcesEvent(teamId = scmTeam.id)
        }

        return scmInstallService.getInstallationFromTeam(scmTeam = scmTeam, allReposAccessible = scmInstallationAccount.allReposAccessible)
    }

    override suspend fun createOrgScm(
        context: RoutingContext,
        input: Resources.createOrgScm,
        body: ScmAccountRequest,
    ): ScmInstance {
        val orgId = context.orgId
        orgValidation.checkAdditionalSCMTeamsAllowed(orgId)
        accessValidation.expectAdminRole(context)
        capabilityValidation.requiresCapability(orgId, PlanCapabilityType.MultiSCM)

        val loginIdentity = requireNotNull(context.identityOrNull())
        val scmIdentity = input.connectingIdentity.let {
            identityStore.findById(identityId = it.let(::IdentityId))
        } ?: throw ForbiddenException()

        if (loginIdentity.person != scmIdentity.person) {
            throw ForbiddenException("User is attempting to connect using an identity that does not belong to them")
        }

        val scm = Scm.fromIdentity(scmIdentity)

        val scmInstallationAccount = when (scm) {
            is Scm.GitHubEnterprise,
            Scm.GitHub,
                -> scmUserDelegate.getScmInstallationAccount(
                orgId = orgId,
                identity = scmIdentity,
                externalInstallationId = body.externalInstallationId,
            ) ?: throw NotFoundException()

            is Scm.BitbucketDataCenter,
            is Scm.GitLabSelfHosted,
            Scm.AzureDevOps,
            Scm.Bitbucket,
            Scm.GitLab,
                -> scmInstallService.getAuthorizedInstallations(scm = scm, identity = scmIdentity)
                .first { it.account.externalId == body.externalId }
        }

        return createTeam(
            identity = scmIdentity,
            scm = scm,
            orgId = orgId,
            scmAccount = scmInstallationAccount.account,
            providerExternalInstallationId = scmInstallationAccount.externalInstallationId,
        ).let { scmTeam ->
            ScmInstance(
                scmAccount = ApiScmAccount(
                    allReposAccessible = scmInstallationAccount.allReposAccessible,
                    avatarUrl = scmTeam.providerAvatarUrl.asString,
                    displayName = scmTeam.providerDisplayName ?: scmTeam.providerLogin,
                    externalId = scmTeam.providerExternalId,
                    externalInstallationId = scmInstallationAccount.externalInstallationId,
                    fullPath = scmTeam.providerLogin,
                    htmlUrl = scmTeam.providerHtmlUrl.asString,
                    isPersonalAccount = scmTeam.providerIsPersonalAccount,
                    provider = scmTeam.provider.asApiModel(),
                    enterpriseProviderId = scmTeam.providerEnterpriseId?.value,
                    isCiEligible = isScmAccountCiEligible(orgId = orgId, provider = scmTeam.provider),
                    needsCiScopes = !scmInstallationAccount.supportsCI,
                ),
                orgId = orgId.value,
                installationId = scmTeam.installationId.value,
                installUrl = scmInstallService.getInstallUrl(scm, scmTeam.providerExternalId)?.asString,
            )
        }
    }

    override suspend fun findInstallationsAndRepos(
        context: RoutingContext,
        input: Resources.findInstallationsAndRepos,
        body: QueryInstallationRequest,
    ): InstallationsResponse {
        val identity = context.identity()

        // parse inputs
        val repoRequests: List<RepoRequest> = body.requests.mapNotNull { requestItem ->
            val repoUrl = RepoUrl.parseOrNull(requestItem.repoUrl)
            val rootHashes = requestItem.rootCommitShas?.map { Hash.parse(it) }.orEmpty()
            repoUrl?.let { RepoRequest(it, rootHashes) }
        }

        // get all my teams
        val scmTeamIds = scmTeamStore.listScmTeamIdsForOrgs(context.orgIds)
        val teams = scmTeamStore.hydrateTeams(scmTeamIds)

        // find DB repos matching repoUrl or rootCommitSha
        val dbRepos: List<Repo> = suspendedTransaction {
            val teamClause = RepoModel.scmTeam inList teams.map { it.id }
            // FIXME richie not obvious what to do here wrt isUserSelected
            //
            //  isScmConnected   isUserSelected   action                                      response field
            //  --------------   --------------   -----------------------------------------   ----------------------
            //           false                x   prompt user to install (GitHub only)        reposNotInstalled
            //            true            false   prompt user to select it (all SCM cases)    reposNotSelected [new]
            //            true             true   return it                                   reposInstalled
            //
            // OR we just treat not selected as installed.
            // OR we just treat not selected as not installed.
            //
            val connectedClause = RepoModel.isScmConnected eq true
            RepoModel.selectAll().where { teamClause and connectedClause }.map { it.toRepo() }
        }.let { allDbRepos ->
            val rootHashes: List<Hash> = repoRequests.map { it.rootHashes }.flatten()
            val httpUrls: List<String> = repoRequests.map { it.repoUrl.canonicalHttpUrl.lowercase() }
            allDbRepos.filter { dbRepo ->
                httpUrls.contains(dbRepo.httpRepoUrl.canonicalHttpUrl.lowercase()) || rootHashes.contains(dbRepo.rootHash)
            }
        }

        // group by authority
        val installationGroups = repoRequests
            .groupBy { it.repoUrl.authority }
            .mapNotNull { (authority: String, repoRequests: List<RepoRequest>) ->
                Scm.fromHostname(authority)?.let { scm ->
                    scm.provider.asApiModel().let { provider ->
                        InstallationGroup(
                            provider = provider,
                            installationsAndRepos = lookupInstallations(
                                scmTeams = teams,
                                allDbRepos = dbRepos,
                                scm = scm,
                                repoRequests = repoRequests,
                                identity = identity,
                            ),
                        )
                    }
                }
            }

        if (installationGroups.isEmpty()) {
            LOGGER.warnAsync(
                "requests" to body.requests,
            ) { "findInstallationsAndRepos could not find team or installation" }
        }

        return InstallationsResponse(
            title = getTitle(installationGroups.map { it.provider }),
            description = getDescription(installationGroups.map { it.provider }),
            installationGroups = installationGroups,
        )
    }

    override suspend fun getOrgInstallationScm(context: RoutingContext, input: Resources.getOrgInstallationScm): ScmInstance {
        val orgId = context.orgId

        val scmTeam = scmTeamStore.findByInstallationId(
            installationId = input.installationId.let(::InstallationId),
            orgId = orgId,
        ) ?: throw NotFoundException()

        val scm = Scm.fromTeam(scmTeam)

        if (!scmTeam.isScmInstalled) {
            throw UserVisibleException(
                statusCode = HttpStatusCode.BadRequest,
                title = """${scmTeam.provider.displayName} has been disconnected""",
                detail = "The ${scmTeam.provider.displayName} has been disconnected and needs to be reconnected.",
                url = scmInstallService.getInstallUrl(scm, scmTeam.providerExternalId),
                reasonType = null,
            )
        }

        val scmInstallationAccount = scmInstallService.getScmTeamInstallation(scmTeam)

        return scmInstallService.getInstallationFromTeam(
            scmTeam = scmTeam,
            allReposAccessible = scmInstallationAccount.allReposAccessible,
        ).let {
            val scmWeb = scmWebFactory.from(scm)
            ScmInstance(
                scmAccount = ApiScmAccount(
                    allReposAccessible = it.allReposAccessible,
                    avatarUrl = it.avatarUrl,
                    displayName = it.displayName,
                    externalId = scmTeam.providerExternalId,
                    externalInstallationId = scmTeam.providerExternalInstallationId ?: throw NotFoundException(),
                    fullPath = it.fullPath,
                    htmlUrl = it.htmlUrl,
                    isPersonalAccount = it.isPersonalAccount,
                    provider = it.provider,
                    enterpriseProviderId = it.enterpriseProviderId,
                    isCiEligible = isScmAccountCiEligible(orgId = orgId, provider = scmTeam.provider),
                    needsCiScopes = !scmInstallationAccount.supportsCI,
                    appPermissionsUrl = scmWeb.appPermissionUrl(scmTeam)?.asString,
                ),
                orgId = scmTeam.id.value,
                installationId = scmTeam.installationId.value,
                installUrl = it.installUrl,
            )
        }
    }

    override suspend fun getOrgInstallationScmRepos(
        context: RoutingContext,
        input: Resources.getOrgInstallationScmRepos,
    ): RepoSelection {
        val orgId = context.orgId

        val scmTeam = requireNotNull(
            scmTeamStore.findByInstallationId(
                installationId = input.installationId.let(::InstallationId),
                orgId = orgId,
            ),
        )

        withTimeout(timeout = 10.seconds) {
            scmInstallService.refreshTeamResourcesIfUninitialized(scmTeam = scmTeam)
        }

        val (reposSelected, reposNotSelected) = run {
            val resolvedTeamId = scmTeam.id
            val personId = context.personId()
            val authorizedMemberId = getAuthorizedMemberId(personId = personId, scmTeam = scmTeam)

            val orgIsEnabled = orgStore.isEnabled(context.orgId)
            val selectedAccess = repoAccessService.getRepoAccessForScmTeam(scmTeamId = scmTeam.id, memberId = authorizedMemberId)
            val notSelectedAccess = repoAccessService.getRepoAccessForScmTeam(
                scmTeamId = scmTeam.id,
                memberId = authorizedMemberId,
                forceFeatureEnabled = orgIsEnabled,
                forceCompute = true,
            )

            repoStore.listConnectedRepos(resolvedTeamId).partition { it.isUserSelected }.let { (selected, notSelected) ->
                Pair(
                    selected.filter { selectedAccess.allows(it.id) },
                    notSelected.filter { notSelectedAccess.allows(it.id) },
                ).also { (reposSelected, reposNotSelected) ->
                    LOGGER.debugAsync(
                        "scmTeamId" to scmTeam.id,
                        "personId" to personId,
                        "authorizedMemberId" to authorizedMemberId,
                        "selectedCount" to selected.size,
                        "notSelectedCount" to notSelected.size,
                        "reposSelectedCount" to reposSelected.size,
                        "reposNotSelectedCount" to reposNotSelected.size,
                        "selectedAccess" to selectedAccess,
                        "notSelectedAccess" to notSelectedAccess,
                    ) {
                        "getOrgInstallationScmRepos"
                    }
                }
            }
        }

        return RepoSelection(
            reposSelected = reposSelected.map(Repo::asApiModel),
            reposNotSelected = reposNotSelected.map(Repo::asApiScmRepo),
        )
    }

    /**
     * Get the member id for the given person and SCM team,
     * or null the person has no identities that are members of the SCM team.
     */
    private suspend fun getAuthorizedMemberId(personId: PersonId, scmTeam: ScmTeam): MemberId? {
        return suspendedTransaction {
            MemberModel
                .join(joinType = JoinType.INNER, otherTable = IdentityModel, otherColumn = IdentityModel.id, onColumn = MemberModel.identity) {
                    AllOp(
                        IdentityModel.person eq personId,
                        IdentityModel.provider eq scmTeam.provider,
                        IdentityModel.externalTeamId eq scmTeam.providerUniqueSignature,
                    )
                }
                .join(
                    otherTable = ScmTeamModel,
                    otherColumn = ScmTeamModel.installation,
                    onColumn = MemberModel.installation,
                    joinType = JoinType.INNER,
                ) {
                    ScmTeamModel.id eq scmTeam.id
                }
                .select(MemberModel.id)
                .whereAll(
                    MemberStore.IS_PRIMARY_CURRENT_MEMBER,
                )
                .map { it[MemberModel.id].value }
                .firstOrNull()
        }
    }

    override suspend fun createScmInstallation(
        context: RoutingContext,
        input: Resources.createScmInstallation,
    ): InstallationV2 {
        val orgId = context.orgIdOrNull
        accessValidation.expectAdminRole(context)

        val identity = requireNotNull(context.identityOrNull())

        // This API is specially designed for GitHub providers only
        check(identity.provider in listOf(Provider.GitHub, Provider.GitHubEnterprise)) { "Expected GitHub provider" }

        val scm = Scm.fromIdentity(identity)

        val scmInstallationAccount = scmUserDelegate.getScmInstallationAccount(
            orgId = orgId,
            identity = identity,
            externalInstallationId = input.providerExternalInstallationId,
        ) ?: run {
            slackNotifier.announceFailedOnboard(identity = identity, provider = scm.provider)
            throw NotFoundException()
        }

        return createTeam(
            identity = identity,
            scm = scm,
            orgId = null,
            scmAccount = scmInstallationAccount.account,
            providerExternalInstallationId = input.providerExternalInstallationId,
        ).let {
            scmInstallService.getInstallationFromTeam(it, scmInstallationAccount.allReposAccessible)
        }
    }

    private fun getTitle(providers: List<ApiProvider>): String {
        return when (providers.size) {
            1 -> when (providers.first()) {
                ApiProvider.azureDevOps,
                    -> "Connect Azure Devops Repositories to Unblocked"

                ApiProvider.bitbucket,
                ApiProvider.bitbucketDataCenter,
                    -> "Connect Bitbucket Repositories to Unblocked"

                ApiProvider.githubEnterprise,
                ApiProvider.github,
                    -> "Install the Unblocked GitHub App"

                ApiProvider.gitlabSelfHosted,
                ApiProvider.gitlab,
                    -> "Connect GitLab Repositories to Unblocked"

                ApiProvider.asana,
                ApiProvider.aws,
                ApiProvider.awsIdentityCenter,
                ApiProvider.bitbucketPipelines,
                ApiProvider.buildKite,
                ApiProvider.circleci,
                ApiProvider.coda,
                ApiProvider.confluence,
                ApiProvider.confluenceDataCenter,
                ApiProvider.customIntegration,
                ApiProvider.githubActions,
                ApiProvider.gitlabPipelines,
                ApiProvider.google,
                ApiProvider.googleDriveWorkspace,
                ApiProvider.googleWorkspace,
                ApiProvider.jira,
                ApiProvider.jiraDataCenter,
                ApiProvider.linear,
                ApiProvider.microsoftEntra,
                ApiProvider.notion,
                ApiProvider.okta,
                ApiProvider.pingOne,
                ApiProvider.saml,
                ApiProvider.slack,
                ApiProvider.stackOverflowTeams,
                ApiProvider.unblocked,
                ApiProvider.web,
                    -> throw IllegalStateException("Can't happen")
            }

            else -> "Connect Repositories to Unblocked"
        }
    }

    private fun getDescription(providers: List<ApiProvider>): String {
        if (providers.isEmpty()) {
            return "No valid repositories found."
        }

        val pullRequests = providers
            .mapNotNull { provider ->
                when (provider) {
                    ApiProvider.azureDevOps,
                    ApiProvider.bitbucket,
                    ApiProvider.bitbucketDataCenter,
                    ApiProvider.github,
                    ApiProvider.githubEnterprise,
                        -> "pull requests"

                    ApiProvider.gitlab,
                    ApiProvider.gitlabSelfHosted,
                        -> "merge requests"

                    ApiProvider.asana,
                    ApiProvider.aws,
                    ApiProvider.awsIdentityCenter,
                    ApiProvider.bitbucketPipelines,
                    ApiProvider.buildKite,
                    ApiProvider.circleci,
                    ApiProvider.coda,
                    ApiProvider.confluence,
                    ApiProvider.confluenceDataCenter,
                    ApiProvider.customIntegration,
                    ApiProvider.githubActions,
                    ApiProvider.gitlabPipelines,
                    ApiProvider.google,
                    ApiProvider.googleDriveWorkspace,
                    ApiProvider.googleWorkspace,
                    ApiProvider.jira,
                    ApiProvider.jiraDataCenter,
                    ApiProvider.linear,
                    ApiProvider.microsoftEntra,
                    ApiProvider.notion,
                    ApiProvider.okta,
                    ApiProvider.pingOne,
                    ApiProvider.saml,
                    ApiProvider.slack,
                    ApiProvider.stackOverflowTeams,
                    ApiProvider.unblocked,
                    ApiProvider.web,
                        -> null
                }
            }
            .distinct()
            .joinToString()
        return "Connect the following repositories to see insights from $pullRequests and code conversations for this project."
    }

    @Suppress("LongMethod", "ThrowsCount", "CyclomaticComplexMethod")
    private suspend fun lookupInstallations(
        scmTeams: List<ScmTeam>,
        allDbRepos: List<Repo>,
        scm: Scm,
        repoRequests: List<RepoRequest>,
        identity: Identity,
    ): List<InstallationAndRepos> {
        check(repoRequests.distinctBy { it.repoUrl.authority }.size == 1) {
            "Expected repoRequests to have a common authority"
        }

        val scmInstallationAccounts = scmInstallService.getAuthorizedInstallations(scm = scm, identity = identity)

        // group by org (aka installation)
        return repoRequests
            .groupBy { it.repoUrl.repoOwner }
            .mapNotNull { (repoOwner: String, repoRequests: List<RepoRequest>) ->

                // match the repo requests to known DB repos
                val (reposInstalled: List<Repo>, reposNotInstalled: List<RepoUrl>) = repoRequests
                    .partition { repoRequest ->
                        allDbRepos.map { it.httpRepoUrl.canonicalHttpUrl.lowercase() }.contains(repoRequest.repoUrl.canonicalHttpUrl.lowercase())
                    }
                    .let { (known: List<RepoRequest>, unknown: List<RepoRequest>) ->
                        val knownRepos = known.mapNotNull { req ->
                            allDbRepos.find { it.httpRepoUrl.canonicalHttpUrl.lowercase() == req.repoUrl.canonicalHttpUrl.lowercase() }
                        }
                        val unknownRepos = unknown.map { it.repoUrl }
                        Pair(knownRepos, unknownRepos)
                    }

                // map each key to an installation
                val scmInstallationAccount = scmInstallationAccounts
                    .firstOrNull { it.account.login.contentEquals(repoOwner, ignoreCase = true) }
                    ?: scmInstallService.getPublicInstallation(scm, repoOwner)
                    ?: run {
                        LOGGER.warnAsync("scm" to scm.uniqueSignature, "provider" to scm.provider.displayName, "orgName" to repoOwner) {
                            "Could not find installation"
                        }
                        return@mapNotNull null
                    }

                val scmTeam = scmTeams
                    .find { it.id == reposInstalled.firstOrNull()?.teamId }
                    ?.also {
                        check(it.provider == scm.provider) { "Expected team provider to be the same as the SCM provider" }
                    }

                InstallationAndRepos(
                    installation = when (scmTeam) {
                        null -> scmInstallService.getInstallationFromScm(scmInstallationAccount, scm)
                        else -> scmInstallService.getInstallationFromTeam(scmTeam, scmInstallationAccount.allReposAccessible)
                    },
                    reposInstalled = reposInstalled.map { it.asApiModel },
                    reposNotInstalled = reposNotInstalled.map {
                        ScmRepo(
                            fullName = """${it.repoOwner}/${it.repoName}""",
                            ownerName = it.repoOwner,
                            repoName = it.repoName,
                            webUrl = it.canonicalHttpUrl,
                            httpUrl = it.canonicalHttpUrl,
                            sshUrl = it.canonicalSshUrl,
                            scpUrl = it.canonicalScpUrl,
                            createdAt = null, // unknown
                            lastActiveAt = null, // unknown
                            externalId = null, // unknown
                            isFork = null, // unknown
                        )
                    },
                )
            }
    }
}
