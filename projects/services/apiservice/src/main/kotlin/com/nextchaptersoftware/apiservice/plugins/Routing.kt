package com.nextchaptersoftware.apiservice.plugins

import com.nextchaptersoftware.api.ArchivedReferencesApi
import com.nextchaptersoftware.api.AsanaApi
import com.nextchaptersoftware.api.BotsApi
import com.nextchaptersoftware.api.CIApi
import com.nextchaptersoftware.api.CodaApi
import com.nextchaptersoftware.api.ConfigApi
import com.nextchaptersoftware.api.ConfluenceApi
import com.nextchaptersoftware.api.ConfluenceDataCenterApi
import com.nextchaptersoftware.api.CustomIntegrationApi
import com.nextchaptersoftware.api.DataSourcePresetsApi
import com.nextchaptersoftware.api.DemoApi
import com.nextchaptersoftware.api.EmailApi
import com.nextchaptersoftware.api.GoogleDriveApi
import com.nextchaptersoftware.api.GoogleDriveWorkspaceApi
import com.nextchaptersoftware.api.GroupsApi
import com.nextchaptersoftware.api.HealthApi
import com.nextchaptersoftware.api.InsightsApi
import com.nextchaptersoftware.api.InstallationsApi
import com.nextchaptersoftware.api.IntegrationsApi
import com.nextchaptersoftware.api.InviteApi
import com.nextchaptersoftware.api.InviteesApi
import com.nextchaptersoftware.api.JiraApi
import com.nextchaptersoftware.api.JiraDataCenterApi
import com.nextchaptersoftware.api.KeysApi
import com.nextchaptersoftware.api.LinearApi
import com.nextchaptersoftware.api.MCPApi
import com.nextchaptersoftware.api.MessagesApi
import com.nextchaptersoftware.api.MetricsApi
import com.nextchaptersoftware.api.NotionApi
import com.nextchaptersoftware.api.PaymentInvitesApi
import com.nextchaptersoftware.api.PersonsApi
import com.nextchaptersoftware.api.PlansApi
import com.nextchaptersoftware.api.ProductFeedbackApi
import com.nextchaptersoftware.api.PullRequestsApi
import com.nextchaptersoftware.api.RegisteredDomainsApi
import com.nextchaptersoftware.api.ReposApi
import com.nextchaptersoftware.api.SCMConfigurationsApi
import com.nextchaptersoftware.api.SCMInstallationsApi
import com.nextchaptersoftware.api.SearchApi
import com.nextchaptersoftware.api.SegmentsApi
import com.nextchaptersoftware.api.SingleSignOnApi
import com.nextchaptersoftware.api.SingleStatsApi
import com.nextchaptersoftware.api.SlackApi
import com.nextchaptersoftware.api.SourceMarksApi
import com.nextchaptersoftware.api.StackOverflowForTeamsApi
import com.nextchaptersoftware.api.TeamMembersApi
import com.nextchaptersoftware.api.TeamsApi
import com.nextchaptersoftware.api.ThreadsApi
import com.nextchaptersoftware.api.TimeSeriesApi
import com.nextchaptersoftware.api.UnreadsApi
import com.nextchaptersoftware.api.VersionsApi
import com.nextchaptersoftware.api.WebIngestionApi
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.integration.extension.services.CollectionServiceImpl
import com.nextchaptersoftware.api.integration.extension.services.DataSourcePresetsConfigurationDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.PaymentInviteConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.PlanConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.asana.AsanaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.coda.CodaConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.confluence.ConfluenceConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.google.GoogleDriveConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.jira.JiraConfigurationDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.linear.LinearConfigurationService
import com.nextchaptersoftware.api.integration.extension.services.slack.SlackService
import com.nextchaptersoftware.api.integration.extension.services.stackoverflow.StackOverflowTeamsService
import com.nextchaptersoftware.api.integration.extension.services.web.WebIngestionConfigurationService
import com.nextchaptersoftware.api.integration.installation.factory.CIInstallationFactory
import com.nextchaptersoftware.api.integration.installation.services.InstallationService
import com.nextchaptersoftware.api.integration.installation.services.InstallationSuppressionService
import com.nextchaptersoftware.api.integration.services.IntegrationService
import com.nextchaptersoftware.api.public.key.ApiKeyServiceProvider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.installSerializer
import com.nextchaptersoftware.api.services.ArchivedReferenceService
import com.nextchaptersoftware.api.services.EmailService
import com.nextchaptersoftware.api.services.McpToolInfoService
import com.nextchaptersoftware.api.services.PersonEmailPreferencesService
import com.nextchaptersoftware.api.services.PersonMcpToolOverrideService
import com.nextchaptersoftware.api.services.PersonService
import com.nextchaptersoftware.api.services.ProviderProgressService
import com.nextchaptersoftware.api.services.PullRequestService
import com.nextchaptersoftware.api.services.SampleQuestionService
import com.nextchaptersoftware.api.services.SemanticSearchFileReferenceService
import com.nextchaptersoftware.api.services.SessionEventService
import com.nextchaptersoftware.api.services.install.ScmInstallationService
import com.nextchaptersoftware.api.threads.services.ThreadService
import com.nextchaptersoftware.apiservice.api.ArchivedReferencesApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.AsanaApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.BotsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.CIApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.CodaApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ConfigApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ConfluenceApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ConfluenceDataCenterApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.CustomIntegrationApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.DataSourcePresetsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.DemoApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.EmailApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.GoogleDriveApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.GoogleDriveWorkspaceApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.GroupsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.HealthApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.InsightsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.InstallationsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.IntegrationsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.InviteApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.InviteesApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.JiraApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.JiraDataCenterApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.KeysApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.LinearApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.MCPApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.MessagesApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.MetricsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.NotionApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.PaymentInvitesApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.PersonsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.PlansApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ProductFeedbackApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.PullRequestsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.RegisteredDomainsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ReposApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SCMConfigurationsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SCMInstallationsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SearchApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SegmentsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SingleSignOnApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SingleStatsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SlackApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.SourceMarksApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.StackOverflowTeamsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.TeamMembersApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.TeamsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.ThreadsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.TimeSeriesApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.UnreadsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.VersionsApiDelegateImpl
import com.nextchaptersoftware.apiservice.api.WebIngestionApiDelegateImpl
import com.nextchaptersoftware.apiservice.rpc.CIProjectManagementDelegateViaRpc
import com.nextchaptersoftware.apiservice.rpc.CITokenManagementDelegateViaRpc
import com.nextchaptersoftware.auth.saml.SamlSettingsProviderService
import com.nextchaptersoftware.auth.saml.config.SamlConfig
import com.nextchaptersoftware.billing.services.downgrade.CapabilityValidation
import com.nextchaptersoftware.billing.utils.BillingCreateEmailService
import com.nextchaptersoftware.billing.utils.OrgBillingSeatService
import com.nextchaptersoftware.bot.services.BotAccountService
import com.nextchaptersoftware.ci.events.CIProjectEventEnqueueService
import com.nextchaptersoftware.ci.services.CIRepoControlService
import com.nextchaptersoftware.ci.services.CIReportService
import com.nextchaptersoftware.clientconfig.ClientConfigService
import com.nextchaptersoftware.config.VersioningConfig
import com.nextchaptersoftware.embedding.service.store.EmbeddingStoreFacade
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.feedback.services.MessageFeedbackService
import com.nextchaptersoftware.maintenance.events.queue.enqueue.MaintenanceEventEnqueueService
import com.nextchaptersoftware.maintenance.installation.StandardUninstallService
import com.nextchaptersoftware.maintenance.org.OrgDeleteService
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.metrics.MetricsService
import com.nextchaptersoftware.ml.inference.services.inferences.MLInferenceService
import com.nextchaptersoftware.notification.events.queue.enqueue.NotificationEventEnqueueService
import com.nextchaptersoftware.notification.events.redis.store.InviteRedisStore
import com.nextchaptersoftware.notification.events.services.InviteService
import com.nextchaptersoftware.notification.services.PeerInviteSuggestionService
import com.nextchaptersoftware.orchestration.enablement.CompositePromotionCriterion
import com.nextchaptersoftware.orchestration.enablement.PullRequestIngestionCriterion
import com.nextchaptersoftware.orchestration.enablement.RepoCodeIngestionCriterion
import com.nextchaptersoftware.plan.capabilities.PlanCapabilitiesService
import com.nextchaptersoftware.product.feedback.ProductFeedbackService
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scm.ScmWebFactory
import com.nextchaptersoftware.scm.delegates.ScmRepoDelegate
import com.nextchaptersoftware.scm.delegates.ScmUserDelegate
import com.nextchaptersoftware.scm.queue.enqueue.ScmEventProducer
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.search.events.queue.enqueue.SearchPriorityEventEnqueueService
import com.nextchaptersoftware.search.services.query.PostgresQueryService
import com.nextchaptersoftware.security.HMACAuthenticator
import com.nextchaptersoftware.segment.SegmentProvider
import com.nextchaptersoftware.semantic.bot.services.MessageMentionService
import com.nextchaptersoftware.semantic.bot.services.MessageService
import com.nextchaptersoftware.semantic.bot.services.NotifyBotService
import com.nextchaptersoftware.service.ServiceHealthApi
import com.nextchaptersoftware.service.lifecycle.ServiceLifecycle
import com.nextchaptersoftware.slack.bot.events.queue.enqueue.PendingSlackQuestionService
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.version.VersionService
import com.nextchaptersoftware.web.ingestion.WebIngestionUrlValidationService
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.resources.Resources
import io.ktor.server.routing.route
import io.ktor.server.routing.routing

@Suppress("LongMethod", "LongParameterList")
fun Application.configureRouting(
    apiKeyServiceProvider: ApiKeyServiceProvider,
    asanaConfigurationService: AsanaConfigurationService,
    archivedReferenceService: ArchivedReferenceService,
    botAccountService: BotAccountService,
    ciProjectEventEnqueueService: CIProjectEventEnqueueService,
    codaConfigurationService: CodaConfigurationService,
    confluenceConfigurationService: ConfluenceConfigurationService,
    dataSourcePresetsConfigurationDelegate: DataSourcePresetsConfigurationDelegateInterface,
    emailService: EmailService,
    embeddingStoreFacade: EmbeddingStoreFacade,
    googleDriveConfigurationService: GoogleDriveConfigurationService,
    inferenceService: MLInferenceService,
    installationService: InstallationService,
    integrationService: IntegrationService,
    intercomHMACAuthenticator: HMACAuthenticator,
    inviteService: InviteService,
    jiraConfigurationDelegate: JiraConfigurationDelegateInterface,
    loginUrlService: LoginUrlService,
    maintenanceEventEnqueueService: MaintenanceEventEnqueueService,
    messageFeedbackService: MessageFeedbackService,
    messageMentionService: MessageMentionService,
    messageService: MessageService,
    metricsService: MetricsService,
    notificationEventEnqueueService: NotificationEventEnqueueService,
    notifyBotService: NotifyBotService,
    paymentInviteConfigurationService: PaymentInviteConfigurationService,
    pendingSlackQuestionService: PendingSlackQuestionService,
    personEmailPreferencesService: PersonEmailPreferencesService,
    personService: PersonService,
    planCapabilitiesService: PlanCapabilitiesService,
    planConfigurationService: PlanConfigurationService,
    postgresQueryService: PostgresQueryService,
    pullRequestService: PullRequestService,
    repoAccessService: RepoAccessService,
    samlConfig: SamlConfig,
    sampleQuestionService: SampleQuestionService,
    scmEventProducer: ScmEventProducer,
    scmInstallService: ScmInstallationService,
    scmRepoDelegate: ScmRepoDelegate,
    scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    scmUserDelegate: ScmUserDelegate,
    scmWebFactory: ScmWebFactory,
    searchPriorityEventEnqueueService: SearchPriorityEventEnqueueService,
    segmentProvider: SegmentProvider,
    semanticSearchFileReferenceService: SemanticSearchFileReferenceService,
    serviceLifecycle: ServiceLifecycle,
    sessionEventService: SessionEventService,
    slackNotifier: SlackNotifier,
    slackService: SlackService,
    stackOverflowTeamsService: StackOverflowTeamsService,
    threadService: ThreadService,
    uninstallService: StandardUninstallService,
    urlBuilderProvider: UrlBuilderProvider,
    versionService: VersionService,
    versionConfig: VersioningConfig,
    webIngestionConfigurationService: WebIngestionConfigurationService,
    webIngestionUrlValidationService: WebIngestionUrlValidationService,
) {
    install(Resources) {
        installSerializer()
    }

    val healthApiDelegateImpl by lazy {
        HealthApiDelegateImpl(ServiceHealthApi(serviceLifecycle = serviceLifecycle))
    }

    val teamPromotionCriterion by lazy {
        CompositePromotionCriterion(
            criteria = listOf(
                RepoCodeIngestionCriterion(),
                PullRequestIngestionCriterion(),
            ),
        )
    }

    val accessValidation by lazy {
        AccessValidation()
    }

    val capabilityValidation by lazy {
        CapabilityValidation(planCapabilitiesService = planCapabilitiesService)
    }

    val ciRepoControlService by lazy {
        CIRepoControlService()
    }
    val ciReportService by lazy {
        CIReportService(
            repoAccessService = repoAccessService,
        )
    }
    val ciInstallationFactory by lazy {
        CIInstallationFactory(
            ciRepoControlService = ciRepoControlService,
        )
    }

    val repoMaintenance by lazy {
        RepoMaintenance()
    }

    val orgDeleteService by lazy {
        OrgDeleteService(
            embeddingStoreFacade = embeddingStoreFacade,
            slackNotifier = slackNotifier,
            uninstallService = uninstallService,
        )
    }
    val samlSettingsProviderService by lazy {
        SamlSettingsProviderService()
    }

    val billingCreateEmailService by lazy {
        BillingCreateEmailService()
    }

    val orgBillingSeatService by lazy {
        OrgBillingSeatService(
            notificationEventEnqueueService = notificationEventEnqueueService,
        )
    }

    val inviteRedisStore by lazy {
        InviteRedisStore(redisApi = Redis.API)
    }

    val clientConfigService by lazy {
        ClientConfigService()
    }

    val collectionService by lazy {
        CollectionServiceImpl()
    }

    val installationSuppressionService by lazy {
        InstallationSuppressionService()
    }

    val productFeedbackService by lazy {
        ProductFeedbackService(slackNotifier = slackNotifier)
    }

    val personMcpToolOverrideService by lazy {
        PersonMcpToolOverrideService()
    }

    val mcpToolInfoService by lazy {
        McpToolInfoService(personMcpToolOverrideService = personMcpToolOverrideService)
    }

    routing {
        route("/api") {
            HealthApi(healthApiDelegateImpl)
            BotsApi(
                BotsApiDelegateImpl(
                    accessValidation = accessValidation,
                    loginUrlService = loginUrlService,
                ),
            )
            EmailApi(
                EmailApiDelegateImpl(
                    notificationEventEnqueueService = notificationEventEnqueueService,
                ),
            )
            GroupsApi(
                GroupsApiDelegateImpl(),
            )
            InviteApi(
                InviteApiDelegateImpl(
                    inviteRedisStore = inviteRedisStore,
                    inviteService = inviteService,
                    urlBuilderProvider = urlBuilderProvider,
                ),
            )
            InviteesApi(
                InviteesApiDelegateImpl(
                    notificationEventEnqueueService = notificationEventEnqueueService,
                    peerInviteSuggestionService = PeerInviteSuggestionService(notificationEventEnqueueService = notificationEventEnqueueService),
                ),
            )
            MCPApi(
                MCPApiDelegateImpl(
                    searchPriorityEventEnqueueService = searchPriorityEventEnqueueService,
                    mcpToolInfoService = mcpToolInfoService,
                ),
            )
            MessagesApi(
                MessagesApiDelegateImpl(
                    messageService = messageService,
                    messageMentionService = messageMentionService,
                    messageFeedbackService = messageFeedbackService,
                    urlBuilderProvider = urlBuilderProvider,
                    emailService = emailService,
                    metricsService = metricsService,
                    slackNotifier = slackNotifier,
                    inferenceService = inferenceService,
                    notifyBotService = notifyBotService,
                    scmWebFactory = scmWebFactory,
                    botAccountService = botAccountService,
                ),
            )
            PersonsApi(
                PersonsApiDelegateImpl(
                    personService = personService,
                    intercomHMACAuthenticator = intercomHMACAuthenticator,
                    personEmailPreferencesService = personEmailPreferencesService,
                    urlBuilderProvider = urlBuilderProvider,
                ),
            )
            PullRequestsApi(
                PullRequestsApiDelegateImpl(
                    pullRequestService = pullRequestService,
                    urlBuilderProvider = urlBuilderProvider,
                    scmWebFactory = scmWebFactory,
                    botAccountService = botAccountService,
                ),
            )
            ReposApi(
                ReposApiDelegateImpl(
                    repoAccessService = repoAccessService,
                ),
            )
            TeamsApi(
                TeamsApiDelegateImpl(
                    accessValidation = accessValidation,
                    orgDeleteService = orgDeleteService,
                    providerProgressService = ProviderProgressService(
                        repoProcessingComplete = teamPromotionCriterion::satisfiedCriterion,
                    ),
                    scmInstallService = scmInstallService,
                    scmRepoDelegate = scmRepoDelegate,
                    inviteService = inviteService,
                    urlBuilderProvider = urlBuilderProvider,
                ),
            )
            TeamMembersApi(
                TeamMembersApiDelegateImpl(
                    accessValidation = accessValidation,
                    botAccountService = botAccountService,
                    capabilityValidation = capabilityValidation,
                    urlBuilderProvider = urlBuilderProvider,
                    orgBillingSeatService = orgBillingSeatService,
                ),
            )
            ThreadsApi(
                ThreadsApiDelegateImpl(
                    threadService = threadService,
                    postgresQueryService = postgresQueryService,
                    urlBuilderProvider = urlBuilderProvider,
                    emailService = emailService,
                    metricsService = metricsService,
                    notifyBotService = notifyBotService,
                    scmWebFactory = scmWebFactory,
                    botAccountService = botAccountService,
                ),
            )
            UnreadsApi(
                UnreadsApiDelegateImpl(),
            )
            SourceMarksApi(
                SourceMarksApiDelegateImpl(
                    repoAccessService = repoAccessService,
                ),
            )
            SCMConfigurationsApi(
                SCMConfigurationsApiDelegateImpl(
                    accessValidation = accessValidation,
                    metricsService = metricsService,
                ),
            )
            RegisteredDomainsApi(
                RegisteredDomainsApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                ),
            )
            SingleSignOnApi(
                SingleSignOnApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    loginUrlService = loginUrlService,
                    samlConfig = samlConfig,
                    samlSettingsProviderService = samlSettingsProviderService,
                ),
            )
            SCMInstallationsApi(
                SCMInstallationsApiDelegateImpl(
                    accessValidation = accessValidation,
                    billingCreateEmailService = billingCreateEmailService,
                    capabilityValidation = capabilityValidation,
                    clientConfigService = clientConfigService,
                    emailService = emailService,
                    repoAccessService = repoAccessService,
                    repoMaintenance = repoMaintenance,
                    scmEventProducer = scmEventProducer,
                    scmInstallService = scmInstallService,
                    scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
                    scmUserDelegate = scmUserDelegate,
                    scmWebFactory = scmWebFactory,
                    segmentProvider = segmentProvider,
                    slackNotifier = slackNotifier,
                ),
            )
            IntegrationsApi(
                IntegrationsApiDelegateImpl(
                    integrationService = integrationService,
                ),
            )
            InstallationsApi(
                InstallationsApiDelegateImpl(
                    accessValidation = accessValidation,
                    installationService = installationService,
                    pendingSlackQuestionService = pendingSlackQuestionService,
                    uninstallService = uninstallService,
                    installationSuppressionService = installationSuppressionService,
                ),
            )
            SearchApi(
                SearchApiDelegateImpl(
                    sampleQuestionService = sampleQuestionService,
                    urlBuilderProvider = urlBuilderProvider,
                    semanticSearchFileReferenceService = semanticSearchFileReferenceService,
                ),
            )
            SlackApi(
                SlackApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    slackService = slackService,
                    urlBuilderProvider = urlBuilderProvider,
                    notificationEventEnqueueService = notificationEventEnqueueService,
                ),
            )
            ConfigApi(
                ConfigApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    maintenanceEventEnqueueService = maintenanceEventEnqueueService,
                ),
            )
            VersionsApi(
                VersionsApiDelegateImpl(
                    versionService = versionService,
                    versionConfig = versionConfig,
                ),
            )
            MetricsApi(MetricsApiDelegateImpl(metricsService = metricsService, sessionEventService = sessionEventService))
            InsightsApi(
                InsightsApiDelegateImpl(
                    postgresQueryService = postgresQueryService,
                    urlBuilderProvider = urlBuilderProvider,
                    metricsService = metricsService,
                    scmWebFactory = scmWebFactory,
                    botAccountService = botAccountService,
                ),
            )
            JiraApi(
                JiraApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    jiraConfigurationDelegate = jiraConfigurationDelegate,
                ),
            )
            JiraDataCenterApi(
                JiraDataCenterApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    jiraConfigurationDelegate = jiraConfigurationDelegate,
                ),
            )
            ConfluenceApi(
                ConfluenceApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    confluenceConfigurationService = confluenceConfigurationService,
                ),
            )
            ConfluenceDataCenterApi(
                ConfluenceDataCenterApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    confluenceConfigurationService = confluenceConfigurationService,
                ),
            )
            AsanaApi(
                AsanaApiDelegateImpl(
                    asanaConfigurationService = asanaConfigurationService,
                ),
            )
            StackOverflowForTeamsApi(
                StackOverflowTeamsApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    stackOverflowTeamsService = stackOverflowTeamsService,
                ),
            )
            WebIngestionApi(
                WebIngestionApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    configurationService = webIngestionConfigurationService,
                    urlValidationService = webIngestionUrlValidationService,
                    uninstallService = uninstallService,
                ),
            )
            LinearApi(
                LinearApiDelegateImpl(
                    accessValidation = accessValidation,
                    linearConfigurationService = LinearConfigurationService(),
                ),
            )
            NotionApi(
                NotionApiDelegateImpl(
                    urlBuilderProvider = urlBuilderProvider,
                ),
            )
            GoogleDriveApi(
                GoogleDriveApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    urlBuilderProvider = urlBuilderProvider,
                    googleDriveConfigurationService = googleDriveConfigurationService,
                ),
            )
            GoogleDriveWorkspaceApi(
                GoogleDriveWorkspaceApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    googleDriveConfigurationService = googleDriveConfigurationService,
                ),
            )
            KeysApi(
                KeysApiDelegateImpl(
                    accessValidation = accessValidation,
                    apiKeyServiceProvider = apiKeyServiceProvider,
                    capabilityValidation = capabilityValidation,
                ),
            )
            SegmentsApi(
                SegmentsApiDelegateImpl(
                    accessValidation = accessValidation,
                    slackService = slackService,
                ),
            )
            SingleStatsApi(
                SingleStatsApiDelegateImpl(),
            )
            TimeSeriesApi(
                TimeSeriesApiDelegateImpl(),
            )
            ArchivedReferencesApi(
                ArchivedReferencesApiDelegateImpl(
                    accessValidation = accessValidation,
                    archivedReferenceService = archivedReferenceService,
                ),
            )
            PlansApi(
                PlansApiDelegateImpl(
                    planConfigurationService = planConfigurationService,
                    slackNotifier = slackNotifier,
                ),
            )
            PaymentInvitesApi(
                PaymentInvitesApiDelegateImpl(
                    paymentInviteConfigurationService = paymentInviteConfigurationService,
                ),
            )
            DemoApi(
                DemoApiDelegateImpl(
                    slackNotifier = slackNotifier,
                ),
            )
            CodaApi(
                CodaApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    codaConfigurationService = codaConfigurationService,
                ),
            )
            CIApi(
                CIApiDelegateImpl(
                    accessValidation = accessValidation,
                    capabilityValidation = capabilityValidation,
                    ciInstallationFactory = ciInstallationFactory,
                    ciProjectEventEnqueueService = ciProjectEventEnqueueService,
                    ciProjectManagementDelegateInterface = CIProjectManagementDelegateViaRpc(),
                    ciRepoControlService = ciRepoControlService,
                    ciReportService = ciReportService,
                    ciTokenManagementDelegateInterface = CITokenManagementDelegateViaRpc(),
                    scmInstallService = scmInstallService,
                    scmWebFactory = scmWebFactory,
                    slackNotifier = slackNotifier,
                ),
            )
            CustomIntegrationApi(
                CustomIntegrationApiDelegateImpl(
                    collectionService = collectionService,
                    slackNotifier = slackNotifier,
                ),
            )
            DataSourcePresetsApi(
                DataSourcePresetsApiDelegateImpl(
                    capabilityValidation = capabilityValidation,
                    dataSourcePresetsConfigurationDelegate = dataSourcePresetsConfigurationDelegate,
                    urlBuilderProvider = urlBuilderProvider,
                ),
            )
            ProductFeedbackApi(
                ProductFeedbackApiDelegateImpl(
                    productFeedbackService = productFeedbackService,
                ),
            )
        }

        // Path used for exposing health endpoint to Kubernetes
        route("/api/health/apiservice") {
            HealthApi(healthApiDelegateImpl)
        }
    }
}
