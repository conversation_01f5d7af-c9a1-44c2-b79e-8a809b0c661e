package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.CustomIntegrationApiDelegateInterface
import com.nextchaptersoftware.api.integration.extension.services.CollectionService
import com.nextchaptersoftware.api.models.CustomDocumentEvent
import com.nextchaptersoftware.api.models.CustomDocumentState
import com.nextchaptersoftware.api.models.CustomIntegration
import com.nextchaptersoftware.api.models.PatchCustomIntegrationRequest
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.stores.CollectionDocumentStore
import com.nextchaptersoftware.db.stores.InstallationStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.models.personId
import com.nextchaptersoftware.slack.notify.SlackNotifier
import com.nextchaptersoftware.utils.asApiDateTime
import io.ktor.http.toURI
import io.ktor.server.routing.RoutingContext
import org.openapitools.server.Resources

class CustomIntegrationApiDelegateImpl(
    private val collectionDocumentStore: CollectionDocumentStore = Stores.collectionDocumentStore,
    private val installationStore: InstallationStore = Stores.installationStore,
    private val collectionService: CollectionService,
    private val slackNotifier: SlackNotifier,
) : CustomIntegrationApiDelegateInterface {
    override suspend fun patchCustomIntegration(
        context: RoutingContext,
        input: Resources.patchCustomIntegration,
        body: PatchCustomIntegrationRequest,
    ) {
        val orgId = context.orgId
        val installationId = input.installationId.let(::InstallationId)

        runSuspendCatching {
            when (collectionService.get(orgId = context.orgId, installationId = installationId)) {
                null -> collectionService.create(
                    orgId = orgId,
                    installationId = installationId,
                    name = body.name,
                    description = body.description,
                    iconUrl = body.iconUrl,
                ).also {
                    installationStore.findById(orgId = orgId, installationId = installationId)?.let {
                        slackNotifier.announceIntegrationAdded(installation = it, personId = context.personId())
                    }
                }

                else -> collectionService.update(
                    orgId = orgId,
                    installationId = installationId,
                    name = body.name,
                    description = body.description,
                    iconUrl = body.iconUrl,
                )
            }
        }.getOrElse {
            throw it as? UserVisibleException ?: BadRequestException()
        }
    }

    override suspend fun getCustomIntegration(context: RoutingContext, input: Resources.getCustomIntegration): CustomIntegration {
        val collection = collectionService.get(orgId = context.orgId, installationId = input.installationId.let(::InstallationId))
            ?: throw NotFoundException()

        val latestDocumentEvents = collectionDocumentStore.lastNDocuments(collectionId = collection.id).map {
            CustomDocumentEvent(
                id = it.documentId,
                fileName = it.title,
                documentUri = it.uri.asUrl.toURI(),
                modifiedAt = it.updatedAt.asApiDateTime(),
                state = when (it.createdAt == it.updatedAt) {
                    true -> CustomDocumentState.added
                    else -> CustomDocumentState.updated
                },
            )
        }

        return CustomIntegration(
            id = collection.installationId.value, // this is correct
            name = collection.name,
            description = collection.description,
            iconUrl = collection.iconUrl.toString(),
            documentCount = collectionDocumentStore.count(collectionId = collection.id),
            lastDocumentModifiedAt = latestDocumentEvents.firstOrNull()?.modifiedAt,
            latestDocumentEvents = latestDocumentEvents,
        )
    }
}
