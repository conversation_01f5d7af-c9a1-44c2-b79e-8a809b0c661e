package com.nextchaptersoftware.apiservice.api

import com.nextchaptersoftware.api.BotsApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.AccessValidation
import com.nextchaptersoftware.api.auth.services.url.login.DataIntegrationLoginSource
import com.nextchaptersoftware.api.auth.services.url.login.LoginUrlService
import com.nextchaptersoftware.api.auth.services.url.login.ScmLoginSource
import com.nextchaptersoftware.api.models.Bot
import com.nextchaptersoftware.api.models.BotOAuthUrlResponse
import com.nextchaptersoftware.api.models.BotSelectionRequest
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.bot.services.InstallationBotService
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.models.orgId
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import io.ktor.http.Url
import io.ktor.server.routing.RoutingContext
import kotlinx.datetime.Instant
import org.openapitools.server.Resources

class BotsApiDelegateImpl(
    private val accessValidation: AccessValidation,
    private val identityStore: IdentityStore = Stores.identityStore,
    private val installationBotService: InstallationBotService = InstallationBotService(),
    private val loginUrlService: LoginUrlService,
) : BotsApiDelegateInterface {

    companion object {
        private const val DEFAULT_BOT_FIND_LIMIT = 5
    }

    override suspend fun findBots(
        context: RoutingContext,
        input: Resources.findBots,
    ): List<Bot> {
        val botCandidates = installationBotService.findBotCandidates(
            input.installationId.let(::InstallationId),
            match = input.match,
            limit = input.limit ?: DEFAULT_BOT_FIND_LIMIT,
        )

        return botCandidates.map { botIdentity ->
            botIdentity.asApiBotModel
        }
    }

    override suspend fun getBot(
        context: RoutingContext,
        input: Resources.getBot,
    ): Bot {
        val botIdentity = installationBotService.getInstallationBot(input.installationId.let(::InstallationId))
            ?: throw NotFoundException()

        return botIdentity.asApiBotModel
    }

    override suspend fun getBotOauthUrl(
        context: RoutingContext,
        input: Resources.getBotOauthUrl,
    ): BotOAuthUrlResponse {
        val installationId = input.installationId.let(::InstallationId)
        val botIdentityId = input.botIdentityId.let(::IdentityId)

        installationBotService.assertBotIsEligible(installationId, botIdentityId)

        val botIdentity = identityStore.findById(identityId = botIdentityId).required()

        val oauthUrl = buildBotOauthUrl(
            orgId = context.orgId,
            identity = botIdentity,
            clientState = input.clientState,
            overrideRedirectUrl = input.overrideRedirectUrl?.let { Url(it) },
        )

        return BotOAuthUrlResponse(
            oauthUrl = oauthUrl.asString,
        )
    }

    override suspend fun selectBot(
        context: RoutingContext,
        input: Resources.selectBot,
        body: BotSelectionRequest,
    ) {
        accessValidation.expectAdminRole(context)
        installationBotService.selectInstallationBot(
            installationId = input.installationId.let(::InstallationId),
            botIdentityId = body.botIdentityId.let(::IdentityId),
        )
    }

    override suspend fun deselectBot(
        context: RoutingContext,
        input: Resources.deselectBot,
    ) {
        accessValidation.expectAdminRole(context)
        installationBotService.deselectInstallationBot(
            installationId = input.installationId.let(::InstallationId),
        )
    }

    private val Identity.asApiBotModel: Bot
        get() {
            val isConnected = when {
                !hasAccessToken -> false
                promptToReconnect -> false
                (refreshTokenExpiresAt ?: Instant.DISTANT_FUTURE) < Instant.nowWithMicrosecondPrecision() -> false
                else -> true
            }

            return Bot(
                botIdentityId = id.value,
                botIdentity = asApiModel(),
                isConnected = isConnected,
            )
        }

    private fun buildBotOauthUrl(
        orgId: OrgId,
        identity: Identity,
        clientState: String?,
        overrideRedirectUrl: Url?,
    ): Url {
        val loginSource = when {
            identity.provider.isScmProvider -> ScmLoginSource(Scm.fromIdentity(identity))
            identity.provider.isDataSourceProvider && identity.provider.hasUserOAuth -> DataIntegrationLoginSource(identity.provider)
            else -> error("Unsupported bot provider")
        }

        return loginUrlService.buildLoginUrl(
            loginSource = loginSource,
            clientSecret = null,
            agentType = null,
            authRedirectOverrideUrl = overrideRedirectUrl?.asString,
            clientState = clientState,
            orgId = orgId,
            targetIdentityId = identity.id,
            skipAccountLinking = true,
        )
    }
}
