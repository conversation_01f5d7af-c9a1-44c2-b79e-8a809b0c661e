package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.auth.services.identity.ProviderIdentityAuthExchangeService
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.rpc.calls.ProviderIdentityCalls
import com.nextchaptersoftware.rpc.calls.ProviderIdentityCalls.ProviderExchangeAuthCodeForIdentityResult
import com.nextchaptersoftware.rpc.calls.ProviderIdentityCalls.ProviderIdentityExchangeAuthCodeForIdentityParams

class ProviderIdentityHandler(
    private val providerIdentityAuthExchangeService: ProviderIdentityAuthExchangeService,
) : ProviderIdentityCalls {
    override suspend fun providerIdentityExchangeAuthCodeForIdentity(
        params: ProviderIdentityExchangeAuthCodeForIdentityParams,
    ): ProviderExchangeAuthCodeForIdentityResult {
        return providerIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
            code = params.code,
            state = params.state,
            signedInPersonId = params.signedInPersonId,
            oAuthApiType = checkNotNull(params.oAuthApiType.unwrap() as? Provider) { "oAuthApiType must be of Provider type" },
            overrideOAuthRedirectUrl = params.overrideOAuthRedirectUrl,
            orgId = params.orgId,
            sessionId = null,
        ).let { identity ->
            ProviderExchangeAuthCodeForIdentityResult(
                identityId = identity.id,
            )
        }
    }
}
