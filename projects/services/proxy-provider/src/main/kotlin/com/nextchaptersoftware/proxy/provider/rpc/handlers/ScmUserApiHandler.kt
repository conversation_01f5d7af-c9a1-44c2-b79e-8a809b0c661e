package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.rpc.calls.ScmUserCalls
import com.nextchaptersoftware.rpc.calls.ScmUserCalls.ScmUserGetScmInstallationAccountParams
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger { }

internal class ScmUserApiHandler(
    private val scmAppApiFactory: ScmAppApiFactory,
    private val scmUserApiFactory: ScmUserApiFactory,
) : ScmUserCalls {

    override suspend fun scmUserGetScmInstallationAccount(
        params: ScmUserGetScmInstallationAccountParams,
    ): ScmInstallationAccount? {
        val identity = params.requiredIdentity()
        val scm = Scm.fromIdentity(identity)

        return getUserInstallation(
            externalInstallationId = params.externalInstallationId,
            identityId = identity.id,
            scm = scm,
            orgId = params.orgId,
        ) ?: getAppInstallation(
            orgId = params.orgId,
            externalInstallationId = params.externalInstallationId,
            scm = scm,
        )
    }

    private suspend fun getUserInstallation(
        externalInstallationId: String,
        orgId: OrgId?,
        identityId: IdentityId,
        scm: Scm,
    ): ScmInstallationAccount? {
        return scmUserApiFactory.getApiFromIdentity(
            orgId = orgId,
            identityId = identityId,
            scm = scm,
        ).use {
            it.installation(externalInstallationId)
        }.also {
            if (it == null) {
                LOGGER.errorAsync(
                    "expectedInstallationId" to externalInstallationId,
                ) { "Failed to get expected USER installation" }
            }
        }
    }

    private suspend fun getAppInstallation(
        orgId: OrgId?,
        externalInstallationId: String,
        scm: Scm,
    ): ScmInstallationAccount? {
        return scmAppApiFactory.getApiOrNull(
            orgId = orgId,
            scm = scm,
        )?.v3App()?.use {
            runSuspendCatching {
                it.installation(externalInstallationId)
            }.onFailure {
                LOGGER.errorAsync(
                    t = it,
                    "expectedInstallationId" to externalInstallationId,
                ) { "Failed to get expected APP installation" }
            }.getOrNull()
        }?.asScmInstallationAccount
    }
}
