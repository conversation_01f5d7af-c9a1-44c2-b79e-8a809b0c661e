package com.nextchaptersoftware.proxy.provider.rpc.handlers

import com.nextchaptersoftware.api.auth.services.identity.ScmIdentityAuthExchangeService
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls.ScmExchangeAuthCodeForIdentityResult
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls.ScmIdentityExchangeAuthCodeForIdentityParams
import com.nextchaptersoftware.scm.Scm

class ScmIdentityHandler(
    private val scmIdentityAuthExchangeService: ScmIdentityAuthExchangeService,
) : ScmIdentityCalls {

    override suspend fun scmIdentityExchangeAuthCodeForIdentity(
        params: ScmIdentityExchangeAuthCodeForIdentityParams,
    ): ScmExchangeAuthCodeForIdentityResult {
        return scmIdentityAuthExchangeService.exchangeAuthCodeForIdentity(
            code = params.code,
            state = params.state,
            signedInPersonId = params.signedInPersonId,
            oAuthApiType = checkNotNull(params.oAuthApiType.unwrap() as? Scm) { "oAuthApiType must be of Scm type" },
            overrideOAuthRedirectUrl = params.overrideOAuthRedirectUrl,
            orgId = params.orgId,
            sessionId = null,
        ).let { identity ->
            ScmExchangeAuthCodeForIdentityResult(
                identityId = identity.id,
            )
        }
    }
}
