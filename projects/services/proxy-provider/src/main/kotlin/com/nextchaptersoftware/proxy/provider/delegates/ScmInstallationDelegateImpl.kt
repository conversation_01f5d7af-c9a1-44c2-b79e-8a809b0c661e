package com.nextchaptersoftware.proxy.provider.delegates

import com.nextchaptersoftware.api.services.install.ScmInstallationDelegateInterface
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.maintenance.scm.ScmTeamMaintenance
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmTeamApiFactory
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.models.ScmInstallationAccount
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.toList

internal class ScmInstallationDelegateImpl(
    private val scmTeamApiFactory: ScmTeamApiFactory,
    private val scmTeamMaintenance: ScmTeamMaintenance,
    private val scmUserApiFactory: ScmUserApiFactory,
) : ScmInstallationDelegateInterface {

    override suspend fun getAuthorizedInstallations(
        scm: Scm,
        identity: Identity,
    ): List<ScmInstallationAccount> {
        return scmUserApiFactory.getApiFromIdentity(
            orgId = null,
            identityId = identity.id,
            scm = scm,
        ).use { api ->
            when (scm) {
                Scm.GitHub,
                is Scm.GitHubEnterprise,
                    -> api.accessibleInstallations(identity.externalId)

                Scm.AzureDevOps,
                Scm.Bitbucket,
                Scm.GitLab,
                is Scm.GitLabSelfHosted,
                is Scm.BitbucketDataCenter,
                    -> api.accounts().map {
                    ScmInstallationAccount(
                        account = it,
                        // These SCMs do not have the concept of an "installation ID" so we use the external ID instead.
                        externalInstallationId = it.externalId,
                        // Always true, because these SCMs do not have the concept of SCM-side repo scoping.
                        allReposAccessible = true,
                        // False, for now, because we don't have support CI for these SCMs yet.
                        supportsCI = false,
                    )
                }.toList()
            }
        }
    }

    override suspend fun getScmTeamInstallation(scmTeam: ScmTeam): ScmInstallationAccount {
        return scmTeamApiFactory.getApiFromTeam(scmTeam, Scm.fromTeam(scmTeam))
            .installationAccount()
    }

    override suspend fun refreshTeamResources(
        scmTeam: ScmTeam,
    ) {
        scmTeamMaintenance.refreshTeamResources(
            scm = Scm.fromTeam(scmTeam),
            scmTeam = scmTeam,
        )
    }
}
