package com.nextchaptersoftware.authservice.api

import com.auth0.jwt.JWT
import com.nextchaptersoftware.access.RestrictedAccessException
import com.nextchaptersoftware.access.RestrictedAccessServiceInterface
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.State.newState
import com.nextchaptersoftware.api.auth.services.identity.IdentityAuthExchangeService
import com.nextchaptersoftware.api.models.AgentType.dashboard
import com.nextchaptersoftware.api.models.AuthToken
import com.nextchaptersoftware.api.models.LoginOptionsResponseV2
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.PreAuthToken
import com.nextchaptersoftware.api.models.Provider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthApiType
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchange
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.auth.oauth.OAuthTokens
import com.nextchaptersoftware.authservice.test.utils.UnblockedAuthApiClient
import com.nextchaptersoftware.config.GlobalConfig
import com.nextchaptersoftware.db.ModelBuilders
import com.nextchaptersoftware.db.ModelBuilders.makeGitHubEnterpriseAppConfig
import com.nextchaptersoftware.db.ModelBuilders.makeIdentity
import com.nextchaptersoftware.db.ModelBuilders.makeMember
import com.nextchaptersoftware.db.ModelBuilders.makeOrg
import com.nextchaptersoftware.db.ModelBuilders.makeOrgMember
import com.nextchaptersoftware.db.ModelBuilders.makePerson
import com.nextchaptersoftware.db.ModelBuilders.makePrefilledAuthState
import com.nextchaptersoftware.db.ModelBuilders.makeSamlIdpMetadata
import com.nextchaptersoftware.db.ModelBuilders.makeScmTeam
import com.nextchaptersoftware.db.ModelBuilders.makeSlackTeam
import com.nextchaptersoftware.db.ModelBuilders.makeTeamSettings
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AuthenticationStateDAO
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider as DbProvider
import com.nextchaptersoftware.db.models.SamlIdpMetadataId
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.utils.DatabaseTestsBase
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.notification.events.redis.store.InviteRedisStore
import com.nextchaptersoftware.notification.events.redis.store.SlackOrgInvite
import com.nextchaptersoftware.redis.Redis
import com.nextchaptersoftware.scm.ScmAuthApiFactory
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.models.ScmAuthUser
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.security.store.TokenChainRedisStore
import com.nextchaptersoftware.types.EmailAddress
import com.nextchaptersoftware.utils.Base64.urlSafeBase64Encode
import com.nextchaptersoftware.utils.nowWithMicrosecondPrecision
import com.sksamuel.hoplite.Secret
import io.ktor.client.call.body
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.URLProtocol
import io.ktor.http.Url
import io.ktor.http.setCookie
import io.ktor.server.auth.jwt.JWTCredential
import io.ktor.server.util.url
import java.util.UUID
import kotlin.time.Duration.Companion.days
import kotlinx.datetime.Instant
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.mock

@Suppress("LargeClass")
class AuthApiDelegateImplTest : DatabaseTestsBase() {

    private val authenticationConfig = GlobalConfig.INSTANCE.authentication
    private val jwt = Jwt(authenticationConfig = authenticationConfig)
    private val tokenChainRedisStore = TokenChainRedisStore(Redis.API)
    private val sessionEventStore = Stores.sessionEventStore
    private val inviteRedisStore: InviteRedisStore = InviteRedisStore()

    private val orgId = OrgId.random()

    private fun buildEncodedState(
        nonce: UUID,
        provider: Provider,
    ): String = OAuthState(
        nonce = nonce,
        provider = provider,
    ).encode().urlSafeBase64Encode()

    @Test
    fun `login path returns redirect url and cookies`() = suspendingDatabaseTest {
        val authState = makePrefilledAuthState()
        UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = GlobalConfig.INSTANCE.authentication.copy(
                    sameSite = "Strict",
                ),
            ),
            scmConfig = ScmConfig.INSTANCE.copy(
                githubCloud = checkNotNull(ScmConfig.INSTANCE.githubCloud).copy(
                    oauth = checkNotNull(ScmConfig.INSTANCE.githubCloud).oauth.copy(
                        clientId = "clientId",
                    ),
                ),
            ),
        ).login(
            clientSecret = authState.secret,
            agentType = dashboard,
            provider = Provider.github,
            clientState = "https://completion.url",
        ) {
            assertThat(HttpStatusCode.Found).isEqualTo(status)

            val cookies = setCookie()
            val secretCookie = cookies.first { it.name == LoginService.CLIENT_SERVICE_COOKIE_NAME }
            val agentTypeCookie = cookies.first { it.name == LoginService.AGENT_TYPE_COOKIE_NAME }
            assertThat(authState.secret.toString()).isEqualTo(secretCookie.value)
            assertThat(secretCookie.maxAge).isGreaterThan(0)
            assertThat("/api/login/exchangeV2").isEqualTo(secretCookie.path)
            assertThat("Strict").isEqualTo(secretCookie.extensions["SameSite"])
            assertThat(secretCookie.httpOnly).isTrue
            assertThat("dashboard").isEqualTo(agentTypeCookie.value)

            val redirectUrl = headers[HttpHeaders.Location]
            assertThat(
                url {
                    host = "github.com"
                    protocol = URLProtocol.HTTPS
                    pathSegments = listOf("login", "oauth", "authorize")
                    parameters["client_id"] = "clientId"
                    parameters["state"] = OAuthState(
                        nonce = authState.nonce,
                        provider = Provider.github,
                        clientState = "https://completion.url",
                    ).encode().urlSafeBase64Encode()
                    parameters["redirect_uri"] = url {
                        host = "localhost"
                        port = 9000
                        protocol = URLProtocol.HTTP
                        pathSegments = listOf("login", "exchange")
                    }
                },
            ).isEqualTo(
                redirectUrl,
            )
        }
    }

    @Test
    fun `loginOptionsV3 includes SAML SSO`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = authenticationConfig,
            ),
        ).loginOptionsV3 {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val response = this.body<LoginOptionsResponseV2>()

            assertThat(response.ssoProviders).isEmpty()
            assertThat(response.availableSSOProviders.map { it.provider }).containsExactlyInAnyOrder(
                Provider.saml,
            )

            assertThat(response.enterpriseProviders).isEmpty()
            assertThat(response.availableEnterpriseProviders.map { it.provider }).containsExactly(
                Provider.bitbucketDataCenter,
                Provider.githubEnterprise,
                Provider.gitlabSelfHosted,
            )

            assertThat(response.publicProviders.map { it.provider }).containsExactly(
                Provider.azureDevOps,
                Provider.bitbucket,
                Provider.github,
                Provider.gitlab,
                Provider.slack,
            )
        }
    }

    @Test
    fun `loginOptions with teamId`() = suspendingDatabaseTest {
        val org = makeOrg()
        makeScmTeam(
            org = org,
            provider = DbProvider.GitLab,
        )
        makeScmTeam(
            org = org,
            provider = DbProvider.GitHubEnterprise,
            providerEnterprise = makeGitHubEnterpriseAppConfig(org = org, provider = DbProvider.GitHubEnterprise),
        )
        val saml = makeSamlIdpMetadata(org = org)

        val client = UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = authenticationConfig,
            ),
        )

        // Org must exist
        client.loginOptionsV3(orgId = OrgId.random()) {
            assertThat(status).isEqualTo(HttpStatusCode.BadRequest)
        }

        // OrgId and ssoProviderIds are mutually exclusive
        client.loginOptionsV3(orgId = org.idValue, ssoProviderIds = listOf(SamlIdpMetadataId.random())) {
            assertThat(status).isEqualTo(HttpStatusCode.BadRequest)
        }

        // OrgId and enterpriseProviderIds are mutually exclusive
        client.loginOptionsV3(orgId = org.idValue, enterpriseProviderIds = listOf(EnterpriseAppConfigId.random())) {
            assertThat(status).isEqualTo(HttpStatusCode.BadRequest)
        }

        // Response should include only the providers that are applicable to the org
        client.loginOptionsV3(orgId = org.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val response = this.body<LoginOptionsResponseV2>()

            assertThat(response.availableEnterpriseProviders).isEmpty()
            assertThat(response.availableSSOProviders.map { it.provider }).isEmpty()
            assertThat(response.enterpriseProviders.map { it.provider }).containsExactlyInAnyOrder(Provider.githubEnterprise)
            assertThat(response.publicProviders.map { it.provider }).containsExactly(Provider.gitlab)
            assertThat(response.ssoProviders.map { it.provider }).containsExactlyInAnyOrder(Provider.googleWorkspace)
        }

        // Once SSO has been enforced, the only available provider should be the SAML provider
        suspendedTransaction { saml.isEnforced = true }
        client.loginOptionsV3(orgId = org.idValue) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val response = this.body<LoginOptionsResponseV2>()

            assertThat(response.availableEnterpriseProviders).isEmpty()
            assertThat(response.availableSSOProviders.map { it.provider }).isEmpty()
            assertThat(response.enterpriseProviders.map { it.provider }).isEmpty()
            assertThat(response.publicProviders.map { it.provider }).isEmpty()
            assertThat(response.ssoProviders.map { it.provider }).containsExactlyInAnyOrder(Provider.googleWorkspace)
        }
    }

    @Test
    fun `loginOptions with teamId and inviteId`() = suspendingDatabaseTest {
        val org = makeOrg()

        val invite = SlackOrgInvite(
            orgId = org.idValue,
            inviteId = UUID.randomUUID(),
            completionUrl = "https://slack.com/dont/care".asUrl,
        )
        inviteRedisStore.setInviteId(invite, 1.days)
        makeSlackTeam(org = org)
        makeScmTeam(
            org = org,
            provider = DbProvider.GitLab,
        )
        makeScmTeam(
            org = org,
            provider = DbProvider.GitHubEnterprise,
            providerEnterprise = makeGitHubEnterpriseAppConfig(org = org, provider = DbProvider.GitHubEnterprise),
        )
        val saml = makeSamlIdpMetadata(org = org)

        val client = UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = authenticationConfig,
            ),
        )

        // Response should include only Slack
        client.loginOptionsV3(orgId = org.idValue, inviteId = invite.inviteId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val response = this.body<LoginOptionsResponseV2>()

            assertThat(response.availableEnterpriseProviders).isEmpty()
            assertThat(response.availableSSOProviders.map { it.provider }).isEmpty()
            assertThat(response.enterpriseProviders.map { it.provider }).isEmpty()
            assertThat(response.publicProviders.map { it.provider }).containsExactly(Provider.slack)
            assertThat(response.ssoProviders.map { it.provider }).isEmpty()
        }

        // Once SSO has been enforced, response should still include only Slack
        suspendedTransaction { saml.isEnforced = true }
        client.loginOptionsV3(orgId = org.idValue, inviteId = invite.inviteId) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val response = this.body<LoginOptionsResponseV2>()

            assertThat(response.availableEnterpriseProviders).isEmpty()
            assertThat(response.availableSSOProviders.map { it.provider }).isEmpty()
            assertThat(response.enterpriseProviders.map { it.provider }).isEmpty()
            assertThat(response.publicProviders.map { it.provider }).containsExactly(Provider.slack)
            assertThat(response.ssoProviders.map { it.provider }).isEmpty()
        }
    }

    @Nested
    inner class LoginAzureDevOpsTest {

        private fun `loginOptionsV3 -- default options`(
            overrideEnvironment: String,
        ) = suspendingDatabaseTest {
            UnblockedAuthApiClient(
                database = database,
                scmConfig = ScmConfig.getTestInstance(
                    overrideEnvironment = overrideEnvironment,
                    overrideUser = "nobody",
                ),
            ).loginOptionsV3 {
                assertThat(status).isEqualTo(HttpStatusCode.OK)
                val response = body<LoginOptionsResponseV2>()
                assertThat(response.publicProviders)
                    .flatMap({ it.provider })
                    .containsExactlyInAnyOrder(
                        Provider.azureDevOps,
                        Provider.bitbucket,
                        Provider.github,
                        Provider.gitlab,
                        Provider.slack,
                    )
            }
        }

        @Test
        fun `loginOptionsV3 -- LOCAL`() {
            `loginOptionsV3 -- default options`(
                overrideEnvironment = "local",
            )
        }

        @Test
        fun `config -- DEV`() {
            `loginOptionsV3 -- default options`(
                overrideEnvironment = "dev",
            )
        }

        @Test
        fun `config -- PROD`() {
            `loginOptionsV3 -- default options`(
                overrideEnvironment = "prod",
            )
        }
    }

    @Suppress("LongMethod")
    @Test
    @Execution(ExecutionMode.SAME_THREAD)
    fun `login exchange path returns auth token`() = suspendingDatabaseTest {
        val person = makePerson()
        val identity = makeIdentity(
            person = person,
        )
        val org = makeOrg(id = orgId)
        val scmTeam = makeScmTeam(org = org)
        val orgMember = makeOrgMember(org = org, person = person)
        makeMember(scmTeam = scmTeam, orgMember = orgMember, identity = identity)
        val mockIdentityAuthExchangeService: IdentityAuthExchangeService<in OAuthApiType> = mock {
            `when`(
                it.exchangeAuthCodeForIdentity(
                    code = anyOrNull(),
                    state = anyOrNull(),
                    signedInPersonId = anyOrNull(),
                    oAuthApiType = anyOrNull(),
                    overrideOAuthRedirectUrl = anyOrNull(),
                    orgId = anyOrNull(),
                    sessionId = anyOrNull(),
                ),
            ).thenReturn(identity.asDataModel())
        }

        val authState = AuthenticationStateDAO.newState()

        UnblockedAuthApiClient(
            database = database,
            overrideIdentityAuthExchangeService = mockIdentityAuthExchangeService,
        ).exchange(
            state = buildEncodedState(authState.nonce, Provider.bitbucket),
            code = "code",
            secret = authState.secret,
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val responseToken: AuthToken = this.body()
            assertThat(responseToken.token).isNotNull
            assertThat(responseToken.refreshToken).isNotNull
            assertDoesNotThrow {
                jwt.authTokenVerifier.verify(responseToken.token)
                jwt.refreshTokenVerifier.verify(responseToken.refreshToken)
            }

            assertNotNull(
                jwt.authTokenValidator(
                    orgId = orgId.value,
                    credential = JWTCredential(JWT().decodeJwt(responseToken.token)),
                    method = HttpMethod.Get,
                    path = "/api/login/exchangeV2",
                ),
            )

            assertNotNull(
                jwt.authTokenValidator(
                    orgId = org.id.value.value,
                    credential = JWTCredential(JWT().decodeJwt(responseToken.token)),
                    method = HttpMethod.Get,
                    path = "/api/login/exchangeV2",
                ),
            )

            assertNotNull(
                jwt.refreshTokenValidator(
                    credential = JWTCredential(JWT().decodeJwt(responseToken.refreshToken)),
                    tokenChainRedisStore = tokenChainRedisStore,
                ),
            )

            val personId = suspendedTransaction {
                authState.refresh(true)
                authState.identity?.person?.id
            }
            assertThat(personId)
                .isNotNull
                .isEqualTo(person.id)
        }
    }

    @Test
    fun `login exchange fails when access is restricted`() = suspendingDatabaseTest {
        val mockOAuthApi = object : OAuthApi {
            override suspend fun exchangeForToken(context: OAuthTokenExchangeContext): OAuthTokenExchange {
                return OAuthTokenExchange(
                    oAuthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                    redirectUrl = null,
                )
            }

            override suspend fun refreshAccessTokens(refreshToken: Secret): OAuthTokens {
                TODO()
            }
        }

        val mockScmUserApiFactory = mock<ScmUserApiFactory>()
        val mockScmUserApi = mock<ScmUserApi>()
        `when`(
            mockScmUserApiFactory.getApiFromTokens(
                orgId = anyOrNull(),
                tokens = any(),
                scm = any(),
                clientEngine = any(),
            ),
        ).thenReturn(mockScmUserApi)
        `when`(mockScmUserApi.user()).thenReturn(
            ScmAuthUser(
                externalId = "externalId",
                avatarUrl = Url("https://example.com/avatar"),
                htmlUrl = Url("https://example.com/html"),
                username = "username",
                displayName = "displayName",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                emails = listOf(EmailAddress.of("<EMAIL>")),
                oauthTokens = OAuthTokens(accessToken = Secret("accessToken")),
                isBot = false,
            ),
        )

        val authState = AuthenticationStateDAO.newState()
        val mockScmAuthApiFactory = mock<ScmAuthApiFactory>()
        `when`(mockScmAuthApiFactory.getApi(orgId = anyOrNull(), oAuthApiType = any())).thenReturn(mockOAuthApi)

        val restrictedAccessService = mock<RestrictedAccessServiceInterface>()
        `when`(restrictedAccessService.checkUserAccess(any(), any(), anyOrNull())).thenAnswer {
            throw RestrictedAccessException("user not allowed")
        }

        UnblockedAuthApiClient(
            database = database,
            restrictedAccessService = restrictedAccessService,
        ).exchange(
            state = buildEncodedState(authState.nonce, Provider.github),
            code = "code",
            secret = authState.secret,
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `login exchange path without secret cookie fails with bad request`() = suspendingDatabaseTest {
        val authState = AuthenticationStateDAO.newState()
        UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = GlobalConfig.INSTANCE.authentication.copy(
                    useSecretCookie = true,
                ),
            ),
        ).exchangeNoCookie(
            state = buildEncodedState(authState.nonce, Provider.github),
            code = "code",
        ) {
            assertThat(HttpStatusCode.BadRequest).isEqualTo(status)
        }
    }

    @Test
    fun `login exchange path with wrong secret cookie fails with unauthorized`() = suspendingDatabaseTest {
        val authState = AuthenticationStateDAO.newState()
        UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = GlobalConfig.INSTANCE.authentication.copy(
                    useSecretCookie = true,
                ),
            ),
        ).exchange(
            state = buildEncodedState(authState.nonce, Provider.github),
            code = "code",
            secret = UUID.randomUUID(),
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `login exchange path without auth state fails with forbidden`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = database,
        ).exchange(
            state = buildEncodedState(UUID.randomUUID(), Provider.github),
            code = "code",
            secret = UUID.randomUUID(),
        ) {
            assertThat(HttpStatusCode.Forbidden).isEqualTo(status)
        }
    }

    @Test
    fun `login exchange path with missing identity fails with unauthorized`() = suspendingDatabaseTest {
        val authState = AuthenticationStateDAO.newState()
        UnblockedAuthApiClient(
            database = database,
            config = GlobalConfig.INSTANCE.copy(
                authentication = GlobalConfig.INSTANCE.authentication.copy(
                    useSecretCookie = true,
                ),
            ),
        ).exchange(
            state = buildEncodedState(authState.nonce, Provider.github),
            code = "code",
            secret = authState.secret,
        ) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting authenticated teamId endpoint with valid token succeeds`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val personId = PersonId.random()

        suspendedTransaction {
            val identity = makeIdentity(id = identityId, person = makePerson(id = personId))
            val org = makeOrg(id = orgId)
            val scmTeam = makeScmTeam(org = org)
            makeMember(identity = identity, scmTeam = scmTeam)
        }

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateAuthToken(
                identityId = identityId.value,
                personId = personId.value,
                tokenChainId = UUID.randomUUID(),
                orgIds = setOf(orgId.value),
                orgsMvra = emptySet(),
                orgsAuthReq = emptySet(),
                readOnly = false,
            ),
        ).testAuth(orgId) {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            assertThat(headers.contains("Access-Control-Allow-Origin")).isTrue
        }
    }

    @Test
    fun `hitting authenticated orgId endpoint with valid token succeeds`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val personId = PersonId.random()

        suspendedTransaction {
            val identity = makeIdentity(id = identityId, person = makePerson(id = personId))
            val org = makeOrg(id = orgId)
            val scmTeam = makeScmTeam(org = org)
            makeMember(identity = identity, scmTeam = scmTeam)
        }

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateAuthToken(
                identityId = identityId.value,
                personId = personId.value,
                tokenChainId = UUID.randomUUID(),
                orgIds = setOf(orgId.value),
                orgsMvra = emptySet(),
                orgsAuthReq = emptySet(),
                readOnly = false,
            ),
        ).testAuth(orgId = orgId) {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            assertThat(headers.contains("Access-Control-Allow-Origin")).isTrue
        }
    }

    @Test
    fun `hitting authenticated endpoint with valid token but wrong team fails with 403`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = jwt.generateAuthToken(
                identityId = IdentityId.random().value,
                personId = PersonId.random().value,
                tokenChainId = UUID.randomUUID(),
                orgIds = setOf(UUID.randomUUID()),
                orgsMvra = emptySet(),
                orgsAuthReq = emptySet(),
                readOnly = false,
            ),
        ).testAuth(orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.Forbidden)
        }
    }

    @Test
    fun `hitting authenticated endpoint with null userId in token fails with 401`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = jwt.testGenerateTokenWithEmptySubject(),
        ).testAuth(orgId = OrgId.random()) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting authenticated endpoint with the wrong token type`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = jwt.generateExchangeToken(),
        ).testAuth(orgId = OrgId.random()) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting authenticated endpoint without a token fails with 401`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
        ).testAuth(orgId) {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting preauth endpoint returns an exchange token containing a secret`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = database,
        ).preAuth(
            provider = Provider.github,
        ) {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            val responseToken: PreAuthToken = this.body()
            assertThat(responseToken.token).isNotNull
            val token = JWTCredential(JWT().decodeJwt(responseToken.token))
            assertDoesNotThrow {
                jwt.exchangeTokenVerifier.verify(responseToken.token)
            }

            assertNotNull(
                jwt.exchangeTokenValidator(token),
            )
        }
    }

    @Test
    fun `hitting preauth exchange endpoint with valid exchange token returns authToken`() = suspendingDatabaseTest {
        val secret = UUID.randomUUID()
        val exchangeToken = jwt.generateExchangeToken(secret)

        suspendedTransaction {
            val identity = makeIdentity(
                trx = this,
                displayName = "Foo Bar",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                person = makePerson(trx = this, id = PersonId.random(), customDisplayName = "First Last"),
            )
            ModelBuilders.makeAuthenticationState(
                trx = this,
                identity = identity,
                nonce = UUID.randomUUID(),
                secret = secret,
            )
        }

        UnblockedAuthApiClient(
            database = database,
            authToken = exchangeToken,
        ).preAuthExchange {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
            val responseToken: AuthToken = this.body()
            assertThat(responseToken.token).isNotNull
            assertThat(responseToken.refreshToken).isNotNull
            val token = JWTCredential(JWT().decodeJwt(responseToken.token))
            val refreshToken = JWTCredential(JWT().decodeJwt(responseToken.refreshToken))
            assertDoesNotThrow {
                jwt.authTokenVerifier.verify(responseToken.token)
                jwt.refreshTokenVerifier.verify(responseToken.refreshToken)
            }

            assertNotNull(
                jwt.authTokenValidator(
                    orgId = null,
                    credential = token,
                    method = HttpMethod.Get,
                    path = "/api/preauth/exchange",
                ),
            )

            assertNotNull(
                jwt.refreshTokenValidator(refreshToken, tokenChainRedisStore),
            )
        }
    }

    @Test
    fun `hitting preauth exchange endpoint with missing identity returns not found`() = suspendingDatabaseTest {
        val secret = UUID.randomUUID()
        val exchangeToken = jwt.generateExchangeToken(secret)
        ModelBuilders.makeAuthenticationState(secret = secret)

        UnblockedAuthApiClient(
            database = database,
            authToken = exchangeToken,
        ).preAuthExchange {
            assertThat(status).isEqualTo(HttpStatusCode.NotFound)
        }
    }

    @Test
    fun `hitting preauth exchange endpoint with invalid token results in UnauthorizedError`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = jwt.generateAuthToken(
                identityId = UUID.randomUUID(),
                personId = PersonId.random().value,
                tokenChainId = UUID.randomUUID(),
                orgIds = null,
                orgsMvra = emptySet(),
                orgsAuthReq = emptySet(),
                readOnly = false,
            ),
        ).preAuthExchange {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh endpoint with missing access token fails with 401 -- SCM`() = suspendingDatabaseTest {
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            provider = DbProvider.GitHub,
            person = makePerson(),
            rawAccessToken = null, // <--- no access token
        )

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identity.idValue.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh endpoint with missing access token does NOT fail with 401 -- SAML`() = suspendingDatabaseTest {
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            provider = DbProvider.MicrosoftEntra,
            person = makePerson(),
            rawAccessToken = null, // <--- no access token
        )

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identity.idValue.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
        }
    }

    @Test
    fun `hitting refresh endpoint with missing access token does NOT fail with 401 -- Slack`() = suspendingDatabaseTest {
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            provider = DbProvider.Slack,
            person = makePerson(),
            rawAccessToken = null, // <--- no access token
        )

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identity.idValue.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.OK)
        }
    }

    @Test
    fun `hitting refresh endpoint with valid refresh token succeeds with new tokens`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val personId = PersonId.random()
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            id = identityId,
            displayName = "Foo Bar",
            primaryEmail = EmailAddress.of("<EMAIL>"),
            externalId = "3806658",
            person = makePerson(id = personId, customDisplayName = "First Last"),
            rawAccessToken = "token".toByteArray(),
        )
        val scmTeam = makeScmTeam(org = makeOrg(enabledAt = Instant.nowWithMicrosecondPrecision()))
        val scmTeam2 = makeScmTeam()
        makeTeamSettings(scmTeam = scmTeam2)
        makeMember(scmTeam = scmTeam, identity = identity)
        makeMember(scmTeam = scmTeam2, identity = identity)
        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identityId.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            val responseToken: AuthToken = this.body()
            assertThat(responseToken.token).isNotNull
            assertThat(responseToken.refreshToken).isNotNull
            val token = JWTCredential(JWT().decodeJwt(responseToken.token))
            val refreshToken = JWTCredential(JWT().decodeJwt(responseToken.refreshToken))
            assertDoesNotThrow {
                jwt.authTokenVerifier.verify(responseToken.token)
                jwt.refreshTokenVerifier.verify(responseToken.refreshToken)
            }
            assertNotNull(
                jwt.authTokenValidator(
                    orgId = scmTeam.orgId.value,
                    credential = token,
                    method = HttpMethod.Get,
                    path = "/api/login/refresh",
                ),
            )

            assertNotNull(
                jwt.authTokenValidator(
                    orgId = scmTeam2.orgId.value,
                    credential = token,
                    method = HttpMethod.Get,
                    path = "/api/login/refresh",
                ),
            )

            assertNotNull(
                jwt.refreshTokenValidator(refreshToken, tokenChainRedisStore),
            )

            assertThat(identityId.toString()).isEqualTo(token.subject)
            assertThat(tokenChainId.toString()).isEqualTo(refreshToken[Jwt.Claim.TokenChainId.value])
        }
    }

    @Test
    fun `hitting refresh endpoint sessionId associates with person`() = suspendingDatabaseTest {
        val sessionId = UUID.randomUUID()
        val identityId = IdentityId.random()
        val personId = PersonId.random()
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            id = identityId,
            displayName = "Foo Bar",
            primaryEmail = EmailAddress.of("<EMAIL>"),
            externalId = "3806658",
            person = makePerson(id = personId, customDisplayName = "First Last"),
            rawAccessToken = "token".toByteArray(),
        )
        val scmTeam = makeScmTeam(org = makeOrg(enabledAt = Instant.nowWithMicrosecondPrecision()))
        val scmTeam2 = makeScmTeam()
        makeTeamSettings(scmTeam = scmTeam2)
        makeMember(scmTeam = scmTeam, identity = identity)
        makeMember(scmTeam = scmTeam2, identity = identity)
        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identityId.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken(sessionId = sessionId) {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            val doesAssociationExist = sessionEventStore.doesAssociationExistForSession(sessionId = sessionId)
            assertThat(doesAssociationExist).isTrue()
        }
    }

    @Test
    fun `hitting refresh endpoint without person fails with 401`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        val identity = makeIdentity(
            id = identityId,
            displayName = "Foo Bar",
            primaryEmail = EmailAddress.of("<EMAIL>"),
            externalId = "3806658",
            person = null,
        )
        val scmTeam = makeScmTeam(org = makeOrg(enabledAt = Instant.nowWithMicrosecondPrecision()))
        makeMember(scmTeam = scmTeam, identity = identity, isCurrentMember = true, isPrimaryMember = true)
        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identityId.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh endpoint with missing identity results in 401`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identityId.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh token endpoint with invalid token results in UnauthorizedError`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = jwt.generateAuthToken(
                identityId = IdentityId.random().value,
                personId = PersonId.random().value,
                tokenChainId = UUID.randomUUID(),
                orgIds = null,
                orgsMvra = emptySet(),
                orgsAuthReq = emptySet(),
                readOnly = false,
            ),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh token endpoint with drifted NBF results in UnauthorizedError`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        suspendedTransaction {
            makeIdentity(
                trx = this,
                id = identityId,
                displayName = "Foo Bar",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                externalId = "3806658",
                person = makePerson(trx = this, id = PersonId.random(), customDisplayName = "First Last"),
            )
        }

        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.testGenerateNBFDriftedRefreshToken(identityId.value),
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh endpoint with an empty token results in 401`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
            authToken = "",
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting refresh token endpoint without a token fails with 401`() = suspendingDatabaseTest {
        UnblockedAuthApiClient(
            database = null,
        ).refreshToken {
            assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
        }
    }

    @Test
    fun `hitting logout endpoint always returns 204`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        suspendedTransaction {
            makeIdentity(
                trx = this,
                id = identityId,
                displayName = "Foo Bar",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                externalId = "3806658",
                person = makePerson(trx = this, id = PersonId.random(), customDisplayName = "First Last"),
                rawAccessToken = "token".toByteArray(),
            )
        }
        UnblockedAuthApiClient(
            database = database,
            authToken = jwt.generateRefreshToken(identityId = identityId.value, tokenChainId = tokenChainId, maxExpiresAt = null),
        ).refreshToken {
            assertThat(HttpStatusCode.OK).isEqualTo(status)
            val responseToken: AuthToken = this.body()

            UnblockedAuthApiClient(
                database = database,
                authToken = responseToken.refreshToken,
            ).logout {
                assertThat(HttpStatusCode.NoContent).isEqualTo(status)

                UnblockedAuthApiClient(
                    database = database,
                    authToken = responseToken.refreshToken,
                ).refreshToken {
                    assertThat(HttpStatusCode.Unauthorized).isEqualTo(status)
                }

                UnblockedAuthApiClient(
                    database = database,
                    authToken = responseToken.refreshToken,
                ).logout {
                    assertThat(HttpStatusCode.NoContent).isEqualTo(status)
                }
            }
        }
    }

    @Test
    fun `service bootstrap should abort when verifier keys are missing`() = suspendingDatabaseTest {
        val identityId = IdentityId.random()
        val tokenChainId = UUID.randomUUID()
        suspendedTransaction {
            makeIdentity(
                trx = this,
                id = identityId,
                displayName = "Foo Bar",
                primaryEmail = EmailAddress.of("<EMAIL>"),
                externalId = "3806658",
                person = makePerson(trx = this, id = PersonId.random(), customDisplayName = "First Last"),
            )
        }

        val config = GlobalConfig.INSTANCE

        assertThrows<NullPointerException> {
            UnblockedAuthApiClient(
                database = database,
                config = config.copy(
                    authentication = config.authentication.copy(
                        tokenPrivateKey = Secret(""),
                        tokenPublicKey = Secret(""),
                    ),
                ),
                authToken = jwt.generateRefreshToken(
                    identityId = identityId.value,
                    tokenChainId = tokenChainId,
                    maxExpiresAt = null,
                ),
            ).refreshToken {
                assertThat(status).isEqualTo(HttpStatusCode.Unauthorized)
            }
        }
    }
}
