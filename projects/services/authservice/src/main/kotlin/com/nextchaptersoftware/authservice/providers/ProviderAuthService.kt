package com.nextchaptersoftware.authservice.providers

import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import io.ktor.http.Url

interface ProviderAuthService {

    suspend fun authExchange(
        orgId: OrgId?,
        provider: Provider,
        context: OAuthTokenExchangeContext,
    ): Url?
}
