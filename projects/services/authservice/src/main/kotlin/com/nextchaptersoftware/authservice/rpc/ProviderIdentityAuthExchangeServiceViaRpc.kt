package com.nextchaptersoftware.authservice.rpc

import com.nextchaptersoftware.api.auth.services.identity.IdentityAuthExchangeService
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.ProviderIdentityCalls
import com.nextchaptersoftware.rpc.calls.wrapSerializable
import io.ktor.http.Url
import java.util.UUID

internal class ProviderIdentityAuthExchangeServiceViaRpc : IdentityAuthExchangeService<Provider> {
    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Provider,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        return RpcFacade
            .withProxyProvider()
            .forAuthService()
            .use {
                it.providerIdentityExchangeAuthCodeForIdentity(
                    params = ProviderIdentityCalls.ProviderIdentityExchangeAuthCodeForIdentityParams(
                        code = code,
                        state = state,
                        orgId = orgId,
                        signedInPersonId = signedInPersonId,
                        oAuthApiType = oAuthApiType.wrapSerializable(),
                        overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                    ),
                )
            }
            .asIdentity()
    }
}
