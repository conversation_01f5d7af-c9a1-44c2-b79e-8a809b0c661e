package com.nextchaptersoftware.authservice.providers

import com.nextchaptersoftware.asana.auth.oauth.AsanaProviderAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthApi
import com.nextchaptersoftware.auth.oauth.OAuthApiFactory
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.confluence.auth.oauth.ConfluenceProviderAuthApi
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.google.auth.oauth.GoogleProviderAuthApi
import com.nextchaptersoftware.jira.auth.oauth.JiraProviderAuthApi
import com.nextchaptersoftware.linear.auth.oauth.LinearProviderAuthApi
import com.nextchaptersoftware.notion.auth.oauth.NotionProviderAuthApi
import com.nextchaptersoftware.slack.auth.oauth.SlackProviderAuthApi
import io.ktor.http.Url

class ProviderAuthApiFactory(
    private val asanaProviderAuthApi: AsanaProviderAuthApi,
    private val slackProviderAuthApi: SlackProviderAuthApi,
    private val confluenceProviderAuthApi: ConfluenceProviderAuthApi,
    private val jiraProviderAuthApi: JiraProviderAuthApi,
    private val notionProviderAuthApi: NotionProviderAuthApi,
    private val linearProviderAuthApi: LinearProviderAuthApi,
    private val googleProviderAuthApi: GoogleProviderAuthApi,
) : OAuthApiFactory<OrgId, Provider>, ProviderAuthService {

    override suspend fun getApi(orgId: OrgId?, oAuthApiType: Provider): OAuthApi {
        return when (oAuthApiType) {
            Provider.Asana -> asanaProviderAuthApi

            Provider.Confluence -> confluenceProviderAuthApi

            Provider.GoogleDrive -> googleProviderAuthApi

            Provider.Jira -> jiraProviderAuthApi

            Provider.Linear -> linearProviderAuthApi

            Provider.Notion -> notionProviderAuthApi

            Provider.Slack -> slackProviderAuthApi

            Provider.GoogleDriveWorkspace -> googleProviderAuthApi

            Provider.Aws,
            Provider.AwsIdentityCenter,
            Provider.AzureDevOps,
            Provider.Bitbucket,
            Provider.BitbucketDataCenter,
            Provider.BitbucketPipelines,
            Provider.Buildkite,
            Provider.CircleCI,
            Provider.Coda,
            Provider.ConfluenceDataCenter,
            Provider.CustomIntegration,
            Provider.GenericSaml,
            Provider.GitHub,
            Provider.GitHubActions,
            Provider.GitHubEnterprise,
            Provider.GitLab,
            Provider.GitLabPipelines,
            Provider.GitLabSelfHosted,
            Provider.GoogleWorkspace,
            Provider.JiraDataCenter,
            Provider.MicrosoftEntra,
            Provider.Okta,
            Provider.PingOne,
            Provider.StackOverflowTeams,
            Provider.Unblocked,
            Provider.Web,
                -> error("${oAuthApiType.displayName} is not supported")
        }
    }

    override suspend fun authExchange(
        orgId: OrgId?,
        provider: Provider,
        context: OAuthTokenExchangeContext,
    ): Url? {
        return getApi(orgId = orgId, oAuthApiType = provider)
            .exchangeForToken(context)
            .redirectUrl
    }
}
