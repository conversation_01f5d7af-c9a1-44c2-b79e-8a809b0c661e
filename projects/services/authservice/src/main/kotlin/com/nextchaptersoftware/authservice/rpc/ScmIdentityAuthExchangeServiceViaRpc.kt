package com.nextchaptersoftware.authservice.rpc

import com.nextchaptersoftware.api.auth.services.identity.IdentityAuthExchangeService
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.ScmIdentityCalls.ScmIdentityExchangeAuthCodeForIdentityParams
import com.nextchaptersoftware.rpc.calls.wrapSerializable
import com.nextchaptersoftware.scm.Scm
import io.ktor.http.Url
import java.util.UUID

internal class ScmIdentityAuthExchangeServiceViaRpc : IdentityAuthExchangeService<Scm> {
    override suspend fun exchangeAuthCodeForIdentity(
        code: String,
        state: String?,
        signedInPersonId: PersonId?,
        oAuthApiType: Scm,
        overrideOAuthRedirectUrl: Url?,
        orgId: OrgId?,
        sessionId: UUID?,
    ): Identity {
        return RpcFacade
            .withProxyProvider()
            .forAuthService()
            .use {
                it.scmIdentityExchangeAuthCodeForIdentity(
                    params = ScmIdentityExchangeAuthCodeForIdentityParams(
                        code = code,
                        state = state,
                        signedInPersonId = signedInPersonId,
                        orgId = orgId,
                        oAuthApiType = oAuthApiType.wrapSerializable(),
                        overrideOAuthRedirectUrl = overrideOAuthRedirectUrl,
                    ),
                )
            }
            .asIdentity()
    }
}
