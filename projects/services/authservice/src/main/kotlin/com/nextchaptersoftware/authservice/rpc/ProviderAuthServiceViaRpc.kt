package com.nextchaptersoftware.authservice.rpc

import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.authservice.providers.ProviderAuthService
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.rpc.RpcFacade
import com.nextchaptersoftware.rpc.calls.OAuthCalls.OAuthExchangeParams
import io.ktor.http.Url

internal class ProviderAuthServiceViaRpc : ProviderAuthService {

    override suspend fun authExchange(
        orgId: OrgId?,
        provider: Provider,
        context: OAuthTokenExchangeContext,
    ): Url? {
        return when (provider) {
            Provider.Asana,
            Provider.Confluence,
            Provider.GoogleDrive,
            Provider.<PERSON>,
            Provider.<PERSON><PERSON>,
            Provider.Not<PERSON>,
            Provider.<PERSON>lack,
            Provider.GoogleDriveWorkspace,
                -> oauthExchange(orgId = orgId, provider = provider, context = context)

            Provider.Aws,
            Provider.AwsIdentityCenter,
            Provider.AzureDevOps,
            Provider.Bitbucket,
            Provider.BitbucketDataCenter,
            Provider.BitbucketPipelines,
            Provider.Buildkite,
            Provider.CircleCI,
            Provider.Coda,
            Provider.ConfluenceDataCenter,
            Provider.CustomIntegration,
            Provider.GenericSaml,
            Provider.GitHub,
            Provider.GitHubActions,
            Provider.GitHubEnterprise,
            Provider.GitLab,
            Provider.GitLabPipelines,
            Provider.GitLabSelfHosted,
            Provider.GoogleWorkspace,
            Provider.JiraDataCenter,
            Provider.MicrosoftEntra,
            Provider.Okta,
            Provider.PingOne,
            Provider.StackOverflowTeams,
            Provider.Unblocked,
            Provider.Web,
                -> error("${provider.displayName} is not supported")
        }
    }

    private suspend fun oauthExchange(
        orgId: OrgId?,
        provider: Provider,
        context: OAuthTokenExchangeContext,
    ): Url? {
        return RpcFacade
            .withProxyProvider()
            .forAuthService()
            .use {
                it.oauthExchange(
                    params = OAuthExchangeParams(
                        orgId = orgId,
                        provider = provider.asApiModel(),
                        context = context,
                    ),
                ).url
            }
    }
}
