package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.api.AuthInstallApiDelegateInterface
import com.nextchaptersoftware.api.models.converters.asProvider
import com.nextchaptersoftware.auth.oauth.OAuthException
import com.nextchaptersoftware.auth.oauth.OAuthTokenExchangeContext
import com.nextchaptersoftware.authservice.providers.ProviderAuthService
import com.nextchaptersoftware.environment.UrlBuilderProvider
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.log.kotlin.infoAsync
import io.ktor.server.routing.RoutingContext
import mu.KotlinLogging
import org.openapitools.server.Resources.authExchange

private val LOGGER = KotlinLogging.logger { }

class AuthInstallApiDelegateImpl(
    private val urlBuilderProvider: UrlBuilderProvider,
    private val providerAuthService: ProviderAuthService,
) : AuthInstallApiDelegateInterface {

    override suspend fun authExchange(context: RoutingContext, input: authExchange): String {
        val provider = input.provider.asProvider()

        val redirectUrl = runSuspendCatching {
            providerAuthService.authExchange(
                orgId = null,
                provider = provider,
                context = OAuthTokenExchangeContext(
                    code = input.code,
                    state = input.state,
                ),
            )
        }.getOrElse {
            when (it) {
                is OAuthException -> it.redirectUrl
                else -> throw it
            }
        }

        LOGGER.infoAsync("input" to input, "redirectUrl" to redirectUrl) {
            "Successfully exchanged auth code"
        }

        return (redirectUrl ?: urlBuilderProvider.dashboard().build()).asString
    }
}
