package com.nextchaptersoftware.authservice.api

import com.nextchaptersoftware.api.AuthApiDelegateInterface
import com.nextchaptersoftware.api.auth.services.AuthorizeAs
import com.nextchaptersoftware.api.auth.services.LoginOptionsService
import com.nextchaptersoftware.api.auth.services.LoginService
import com.nextchaptersoftware.api.auth.services.PreAuthService
import com.nextchaptersoftware.api.auth.services.State.fetchByNonce
import com.nextchaptersoftware.api.auth.services.State.fetchBySecret
import com.nextchaptersoftware.api.auth.services.State.newState
import com.nextchaptersoftware.api.models.AgentType
import com.nextchaptersoftware.api.models.AuthConnection
import com.nextchaptersoftware.api.models.AuthToken
import com.nextchaptersoftware.api.models.HttpMethod as ApiHttpMethod
import com.nextchaptersoftware.api.models.LoginOptionsResponseV2
import com.nextchaptersoftware.api.models.OAuthState
import com.nextchaptersoftware.api.models.PreAuthToken
import com.nextchaptersoftware.api.models.ScopedResource as ApiScopedResource
import com.nextchaptersoftware.api.models.ScopedToken
import com.nextchaptersoftware.api.models.converters.asApiModel
import com.nextchaptersoftware.api.models.converters.asProvider
import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encode
import com.nextchaptersoftware.api.services.PersonService
import com.nextchaptersoftware.api.services.SegmentService
import com.nextchaptersoftware.api.services.SessionEventService
import com.nextchaptersoftware.api.services.VersionObsoleteService
import com.nextchaptersoftware.auth.saml.SamlAuthProviderService
import com.nextchaptersoftware.auth.saml.request.AuthnRequestParamsBuilder
import com.nextchaptersoftware.auth.saml.request.AuthnRequestParamsExtensionsBuilder
import com.nextchaptersoftware.auth.saml.utils.ApplicationCallSamlExtensions.getServletRequest
import com.nextchaptersoftware.auth.saml.utils.ApplicationCallSamlExtensions.getServletResponse
import com.nextchaptersoftware.cas.RedisCAS
import com.nextchaptersoftware.config.AuthenticationConfig
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.AuthenticationStateDAO
import com.nextchaptersoftware.db.models.AuthenticationStateModel
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.ProductAgentType
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.SamlIdpMetadataId
import com.nextchaptersoftware.db.models.fromProductAgentHeader
import com.nextchaptersoftware.db.stores.IdentityStore
import com.nextchaptersoftware.db.stores.SamlIdpMetadataStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.insider.InsiderServiceInterface
import com.nextchaptersoftware.kotlinx.coroutines.runSuspendCatching
import com.nextchaptersoftware.ktor.BadRequestException
import com.nextchaptersoftware.ktor.ForbiddenException
import com.nextchaptersoftware.ktor.NotFoundException
import com.nextchaptersoftware.ktor.ServiceException
import com.nextchaptersoftware.ktor.UnauthorizedException
import com.nextchaptersoftware.ktor.UserVisibleException
import com.nextchaptersoftware.ktor.UserVisibleReason
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asString
import com.nextchaptersoftware.ktor.utils.UrlExtensions.asUrl
import com.nextchaptersoftware.log.kotlin.errorAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.models.personId
import com.nextchaptersoftware.repo.RepoAccessService
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.config.ScmConfig
import com.nextchaptersoftware.scm.github.GitHubUnverifiedEmailException
import com.nextchaptersoftware.security.ScopedResource
import com.nextchaptersoftware.security.jwt.Jwt
import com.nextchaptersoftware.security.jwt.JwtUtils.AuthorizationHeaderMissingException
import com.nextchaptersoftware.security.jwt.JwtUtils.authorizationToken
import com.nextchaptersoftware.slack.auth.services.UnknownSlackInstallationException
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.nextchaptersoftware.utils.asUUIDOrNull
import com.nextchaptersoftware.utils.nullIfEmpty
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpMethod
import io.ktor.http.HttpStatusCode
import io.ktor.http.ParametersBuilder
import io.ktor.http.Url
import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.ApplicationRequest
import io.ktor.server.request.header
import io.ktor.server.routing.RoutingContext
import io.ktor.util.decodeBase64String
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.datetime.Instant
import mu.KotlinLogging
import org.jetbrains.exposed.sql.update
import org.openapitools.server.Resources

private val LOGGER = KotlinLogging.logger {}

class AuthApiDelegateImpl(
    private val authenticationConfig: AuthenticationConfig,
    private val identityStore: IdentityStore = Stores.identityStore,
    private val insiderService: InsiderServiceInterface,
    private val loginOptionsService: LoginOptionsService,
    private val loginService: LoginService,
    private val personService: PersonService,
    private val preAuthService: PreAuthService,
    private val repoAccessService: RepoAccessService,
    private val samlAuthProviderService: SamlAuthProviderService,
    private val samlIdpMetadataStore: SamlIdpMetadataStore = Stores.samlIdpMetadataStore,
    private val samlRelayStateStore: RedisCAS,
    private val scmConfig: ScmConfig,
    private val segmentService: SegmentService,
    private val sessionEventService: SessionEventService,
    private val versionObsoleteService: VersionObsoleteService,
) : AuthApiDelegateInterface {

    override suspend fun loginOptionsV3(
        context: RoutingContext,
        input: Resources.loginOptionsV3,
    ): LoginOptionsResponseV2 {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)
        return loginOptionsService.getLoginOptions(input = input, headers = context.call.request.headers)
    }

    override suspend fun logout(context: RoutingContext, input: Resources.logout) {
        val authorizationToken = try {
            context.call.request.authorizationToken()
        } catch (ex: AuthorizationHeaderMissingException) {
            LOGGER.errorAsync(ex) { "Auth token missing" }
            return
        }
        loginService.logout(authorizationToken)
    }

    override suspend fun login(context: RoutingContext, input: Resources.login): String {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)

        val provider = input.provider.asProvider()

        val state = input.clientSecret?.let { AuthenticationStateDAO.fetchBySecret(secret = it) }
            ?: AuthenticationStateDAO.newState()

        val agentType = input.agentType ?: AgentType.dashboard

        context.call.response.cookies.append(loginService.createSecretCookie(state.secret))
        context.call.response.cookies.append(loginService.createAgentTypeCookie(agentType))

        if (provider.isSingleSignOn) {
            val ssoProviderId = input.ssoProviderId.required().let(::SamlIdpMetadataId)
            return ssoLogin(
                call = context.call,
                provider = input.provider,
                authRedirectOverrideUrl = input.overrideRedirectUrl?.let { Url(it) } ?: input.redirectUrl?.asUrl,
                clientState = input.clientState,
                nonce = state.nonce,
                ssoEmail = input.ssoEmail,
                ssoProviderId = ssoProviderId,
            ).asString
        }

        val oAuthApiType = if (provider.isScmProvider) {
            Scm.fromProvider(provider, input.enterpriseProviderId?.let(::EnterpriseAppConfigId))
        } else {
            provider
        }

        val extraParameters = ParametersBuilder().apply {
            if (provider == Provider.Slack) {
                input.teamId
                    ?.let { Stores.slackTeamStore.findByOrg(orgId = OrgId(it)) }
                    ?.slackExternalTeamId
                    ?.let {
                        append("team", it)
                    }
            }
        }.build()

        return loginService.buildRedirectUrl(
            oAuthApiType = oAuthApiType,
            nonce = state.nonce,
            authRedirectOverrideUrl = input.redirectUrl?.asUrl,
            clientState = input.clientState,
            state = null,
            extraParameters = extraParameters,
            targetIdentityId = input.targetIdentityId?.let(::IdentityId),
            skipAccountLinking = input.skipAccountLinking,
        ).toString()
    }

    private suspend fun ssoLogin(
        call: ApplicationCall,
        provider: com.nextchaptersoftware.api.models.Provider,
        authRedirectOverrideUrl: Url?,
        clientState: String?,
        nonce: UUID,
        ssoEmail: String?,
        ssoProviderId: SamlIdpMetadataId,
    ): Url {
        val saml = samlIdpMetadataStore.findById(ssoProviderId).required()
        val samlAuth = samlAuthProviderService.getSamlAuth(
            samlIdpMetadata = saml,
            httpServletRequest = call.getServletRequest(),
            httpServletResponse = call.getServletResponse(),
        )

        val authnRequestParams = AuthnRequestParamsBuilder()
            .setNameIdPolicy(true)
            .nameIdValueReq(ssoEmail)
            .build()

        val authnRequestParamsExtensions = AuthnRequestParamsExtensionsBuilder()
            .apply { ssoEmail?.also { loginHint(email = ssoEmail) } }
            .build()

        // We cannot pass the authState to the SAML provider, because SAML 2.0 restricts the size of the relayState to 80 characters.
        // Instead, we store the clientState in a CAS key and pass the key to the SAML provider.
        val relayStateKey = SamlRelayState(
            authState = OAuthState(
                nonce = nonce,
                provider = provider,
                enterpriseProviderId = null,
                ssoProviderId = ssoProviderId.value,
                clientState = clientState,
                state = null,
            ),
            overrideAuthRedirectUrl = authRedirectOverrideUrl,
        ).let { samlRelayStateStore.store(it.encode()) }

        return withContext(Dispatchers.IO) {
            samlAuth.login(
                // @param relayState
                relayStateKey.asString,
                // @param authnRequestParams
                authnRequestParams,
                // @param stay: true means return a URL string, instead of redirecting
                true,
                // @param parameters
                authnRequestParamsExtensions,
            )
        }.asUrl
    }

    override suspend fun authConnectionExchange(
        context: RoutingContext,
        input: Resources.authConnectionExchange,
    ): AuthConnection {
        val oAuthState: OAuthState = input.state.decodeBase64String().decode()
        val nonce = oAuthState.nonce ?: throw BadRequestException("Missing nonce")
        AuthenticationStateDAO.fetchByNonce(nonce = nonce) ?: throw ForbiddenException()

        val provider = oAuthState.provider?.asProvider() ?: throw BadRequestException("Missing provider")

        val identity = runSuspendCatching {
            loginService.exchangeAuthCodeForIdentity(
                code = input.code,
                state = oAuthState.clientState,
                signedInPersonId = context.personId(),
                oAuthApiType = when (provider.isScmProvider) {
                    true -> Scm.fromProvider(provider, oAuthState.enterpriseProviderId?.let(::EnterpriseAppConfigId))
                    else -> provider
                },
                overrideOAuthRedirectUrl = getOverrideAuthRedirectUrl(context.call.request, provider),
                orgId = null,
            )
        }.getOrElse {
            (it.cause as? GitHubUnverifiedEmailException)?.also { ex ->
                throwGitHubUnverifiedEmailException(ex, HttpStatusCode.Forbidden)
            }
            (it.cause as? UnknownSlackInstallationException)?.also { ex ->
                throwUnknownSlackInstallationException(ex, HttpStatusCode.Forbidden)
            }
            LOGGER.errorAsync(it) { "code exchange failed" }
            throw ForbiddenException()
        }

        // Validate that the identity matches the expected target identity ID
        oAuthState.targetIdentityId?.also { targetIdentityId ->
            if (identity.id != targetIdentityId) {
                throw BadRequestException("Target identity ID mismatch")
            }
        }

        return AuthConnection(
            connectingIdentityId = identity.id.value,
            identityDisplayName = identity.displayName ?: identity.username,
            identityAvatarUrl = identity.avatarUrl.asString,
            identityHtmlUrl = identity.htmlUrl.asString,
            provider = identity.provider.asApiModel(),
            enterpriseId = when (identity.provider.isScmProvider && identity.provider.isEnterprise) {
                true -> Scm.fromIdentity(identity).providerEnterpriseId?.value
                false -> null
            },
        )
    }

    override suspend fun exchangeAuthCodeV2(context: RoutingContext, input: Resources.exchangeAuthCodeV2): AuthToken {
        val oAuthState: OAuthState = input.state.decodeBase64String().decode()

        val nonce = oAuthState.nonce ?: throw BadRequestException("Missing nonce")

        val authState = AuthenticationStateDAO.fetchByNonce(nonce = nonce)
            ?: throw ForbiddenException("Missing auth state")

        val provider = oAuthState.provider?.asProvider() ?: throw BadRequestException("Missing provider")

        // There can never be a secret cookie if the sign-in is initiated from the IdP (eg: Okta dashboard), so skip this check for SSO providers.
        if (provider.isSingleSignOn.not() && authenticationConfig.useSecretCookie) {
            val secretCookie = context.call.request.cookies[LoginService.CLIENT_SERVICE_COOKIE_NAME]
                ?: throw BadRequestException()

            if (authState.secret.toString() != secretCookie) {
                throw UnauthorizedException()
            }
        }

        val oAuthApiType = if (provider.isScmProvider) {
            Scm.fromProvider(provider, oAuthState.enterpriseProviderId?.let(::EnterpriseAppConfigId))
        } else {
            provider
        }

        val identity = runSuspendCatching {
            loginService.exchangeAuthCodeForIdentity(
                code = input.code,
                state = oAuthState.clientState,
                signedInPersonId = null,
                orgId = null,
                oAuthApiType = oAuthApiType,
                overrideOAuthRedirectUrl = getOverrideAuthRedirectUrl(context.call.request, provider),
                sessionId = input.sessionIdentifier,
            )
        }.getOrElse {
            (it.cause as? GitHubUnverifiedEmailException)?.also { ex ->
                throwGitHubUnverifiedEmailException(ex, HttpStatusCode.Unauthorized)
            }
            (it.cause as? UnknownSlackInstallationException)?.also { ex ->
                throwUnknownSlackInstallationException(ex, HttpStatusCode.Unauthorized)
            }
            LOGGER.errorAsync(it) { "code exchange failed" }
            throw UnauthorizedException()
        }

        suspendedTransaction {
            AuthenticationStateModel.update({ AuthenticationStateModel.id eq authState.id }) {
                it[AuthenticationStateModel.identity] = identity.id
            }
        }

        runSuspendCatching {
            val personId = identity.person.required()
            segmentService.signIn(
                personId = personId,
                agentType = ProductAgentType.Dashboard,
                sessionId = input.sessionIdentifier,
            )
        }

        return authTokenForIdentity(
            identity = identity,
            tokenChainId = UUID.randomUUID(),
            maxRefreshTokenExpiresAt = null,
        ) ?: throw ServiceException("Failed to generate auth token")
    }

    /**
     * Allow local web stack to sign in to a DEV stack.
     */
    private fun getOverrideAuthRedirectUrl(request: ApplicationRequest, provider: Provider): Url? {
        val isLocalWebOrigin = request.header(HttpHeaders.Origin) == "http://localhost:9000"
        if (!isLocalWebOrigin) {
            return null
        }

        return when (provider) {
            Provider.GitLab -> scmConfig.gitlabCloud?.authRedirectOverrideUrl?.asUrl
            Provider.GitLabSelfHosted -> scmConfig.gitlabSelfHosted.authRedirectOverrideUrl?.asUrl
            Provider.BitbucketDataCenter -> scmConfig.bitbucketDataCenter.authRedirectOverrideUrl?.asUrl
            else -> null
        }
    }

    override suspend fun preAuth(
        context: RoutingContext,
        input: Resources.preAuth,
    ): PreAuthToken {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)

        val state = AuthenticationStateDAO.newState()
        return preAuthService.generateExchangeToken(state.secret)
    }

    override suspend fun preAuthTokenExchange(
        context: RoutingContext,
        input: Resources.preAuthTokenExchange,
    ): AuthToken {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)

        val authorizationToken = try {
            context.call.request.authorizationToken()
        } catch (ex: AuthorizationHeaderMissingException) {
            LOGGER.errorAsync(ex) { "Auth token missing" }
            throw UnauthorizedException()
        }
        val clientSecret = authorizationToken[Jwt.Claim.ClientSecret.value]
            ?: throw BadRequestException()

        val state = AuthenticationStateDAO.fetchBySecret(secret = UUID.fromString(clientSecret))
            ?: throw ForbiddenException()

        val identity = suspendedTransaction {
            state.identity?.asDataModel()
        } ?: throw NotFoundException()

        return authTokenForIdentity(
            identity = identity,
            tokenChainId = UUID.randomUUID(),
            maxRefreshTokenExpiresAt = null,
        ) ?: throw ServiceException("Failed to generate auth token")
    }

    @Suppress("CyclomaticComplexMethod")
    override suspend fun refreshToken(
        context: RoutingContext,
        input: Resources.refreshToken,
        xUnblockedProductAgent: String?,
    ): AuthToken {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)

        val authorizationToken = try {
            context.call.request.authorizationToken()
        } catch (ex: AuthorizationHeaderMissingException) {
            LOGGER.errorAsync(ex) { "Auth token missing" }
            throw UnauthorizedException()
        }

        val identityId = authorizationToken.subject?.asUUIDOrNull()?.let(::IdentityId)
            ?: throw UnauthorizedException("Missing identity id from token")

        val tokenChainId = authorizationToken[Jwt.Claim.TokenChainId.value]?.asUUIDOrNull()
            ?: throw UnauthorizedException("Missing token chain id")

        val identity = lookupIdentity(identityId)
            ?: throw UnauthorizedException("Missing identity")

        if (identity.hasAccessToken.not()) {
            // SAML accounts do not have access tokens
            // Slack accounts do not necessarily have access tokens
            if (identity.provider.isScmProvider) {
                LOGGER.warnAsync("identityId" to identity.id) { "Logging out user due to missing access token" }
                throw UnauthorizedException("Identity not authenticated")
            }
        }

        if (identity.provider == Provider.Bitbucket) {
            if (identity.accessTokenScope?.contains("pipeline") == false) {
                LOGGER.warnAsync("identityId" to identity.id) { "Logging out user due to missing Bitbucket Pipelines scope" }
                throw UnauthorizedException("Missing Bitbucket Pipelines scope")
            }
        }

        val maxRefreshTokenExpiresAt = identity.refreshTokenExpiresAt

        runSuspendCatching {
            input.sessionIdentifier?.let { sessionId ->
                identity.person?.let {
                    sessionEventService.associateSession(sessionId = sessionId, personId = it)
                }
            }
        }

        runSuspendCatching {
            identity.person?.let { personId ->
                xUnblockedProductAgent?.let {
                    val agentType = ProductAgentType.fromProductAgentHeader(it)
                    personService.setHasInstalled(trx = null, personId = personId, agent = agentType)
                }
            }
        }

        return authTokenForIdentity(
            identity = identity,
            tokenChainId = tokenChainId,
            maxRefreshTokenExpiresAt = maxRefreshTokenExpiresAt,
        ) ?: throw ServiceException("Failed to generate auth token")
    }

    override suspend fun getScopedAccess(
        context: RoutingContext,
        input: Resources.getScopedAccess,
        body: ApiScopedResource,
    ): ScopedToken {
        versionObsoleteService.rejectObsoleteOrNotFoundProductVersions(context.call.request)

        val authorizationToken = try {
            context.call.request.authorizationToken()
        } catch (ex: AuthorizationHeaderMissingException) {
            LOGGER.errorAsync(ex) { "Auth token missing" }
            throw UnauthorizedException()
        }

        val identityId = authorizationToken.subject?.asUUIDOrNull()?.let(::IdentityId)
            ?: throw UnauthorizedException("Missing identity id from token")

        val tokenChainId = authorizationToken[Jwt.Claim.TokenChainId.value]?.asUUIDOrNull()
            ?: throw UnauthorizedException("Missing token chain id")

        val scope = ScopedResource(method = body.method.asHttpMethod, path = body.resourcePath)
        if (!scope.isAllowed) {
            LOGGER.errorAsync { "Scope is not allowed" }
            throw BadRequestException()
        }

        val identity = lookupIdentity(identityId)
            ?: throw UnauthorizedException("Identity not found")

        return getScopedToken(
            identity = identity,
            tokenChainId = tokenChainId,
            scope = scope,
        )
    }

    private suspend fun getScopedToken(
        identity: Identity,
        tokenChainId: UUID,
        scope: ScopedResource,
    ): ScopedToken {
        return AuthorizeAs.from(insiderService, identityStore, identity).let { authorizeAs ->
            lookupIdentity(authorizeAs.apparentIdentityId)?.let { apparentIdentity ->
                val memberAccessList = identityStore.getMemberAccessList(apparentIdentity.id)

                memberAccessList
                    .map { it.value }
                    .filter { it == scope.getOrgId() }
                    .ifEmpty {
                        LOGGER.errorAsync { "Identity is not authorized for scoped resource" }
                        throw UnauthorizedException()
                    }

                loginService.generateScopedAuthToken(
                    authorizeAs = authorizeAs,
                    orgIds = memberAccessList,
                    orgsMvra = emptySet(),
                    orgsAuthReq = emptySet(),
                    tokenChainId = tokenChainId,
                    scopes = listOf(scope),
                )
            }
        } ?: throw BadRequestException()
    }

    private suspend fun authTokenForIdentity(
        identity: Identity,
        tokenChainId: UUID,
        maxRefreshTokenExpiresAt: Instant?,
    ): AuthToken? {
        return AuthorizeAs.from(insiderService, identityStore, identity).let { authorizeAs ->
            lookupIdentity(authorizeAs.apparentIdentityId)?.let { apparentIdentity ->
                val orgIds = identityStore.getMemberAccessList(apparentIdentity.id)
                val orgsMvra = repoAccessService.computeMustValidateRepoAccess(orgIds)

                val orgsAuthReq = samlIdpMetadataStore.findSamlEnforcedOrgIds(
                    orgIds = orgIds,
                    excludeEntityId = apparentIdentity.ssoEntityId,
                )

                loginService.generateAuthToken(
                    authorizeAs = authorizeAs,
                    orgIds = orgIds,
                    orgsMvra = orgsMvra,
                    orgsAuthReq = orgsAuthReq,
                    tokenChainId = tokenChainId,
                    maxRefreshTokenExpiresAt = maxRefreshTokenExpiresAt,
                )
            }
        }
    }

    private suspend fun lookupIdentity(identityId: IdentityId) = suspendedTransaction {
        IdentityDAO.findById(identityId)?.asDataModel()
    }

    private suspend fun throwGitHubUnverifiedEmailException(
        exception: GitHubUnverifiedEmailException,
        statusCode: HttpStatusCode = HttpStatusCode.Unauthorized,
    ) {
        LOGGER.warnAsync(exception) { "RPC code exchange failed due to user error" }
        throw UserVisibleException(
            statusCode = statusCode,
            title = "We're sorry, but to use Unblocked, a verified email is required.",
            detail = "A verified email provides a reliable communication and verification method for your account." +
                    " Please verify your email address in GitHub and try again.",
            url = Url("https://docs.github.com/en/get-started/signing-up-for-github/verifying-your-email-address"),
        )
    }

    private suspend fun throwUnknownSlackInstallationException(
        exception: UnknownSlackInstallationException,
        statusCode: HttpStatusCode,
    ) {
        LOGGER.warnAsync(exception) { "RPC code exchange failed due to user error" }
        throw UserVisibleException(
            statusCode = statusCode,
            title = "Unknown Slack installation",
            detail = exception.slackWorkspaceName.trim().nullIfEmpty(),
            url = null,
            reasonType = UserVisibleReason.UnknownSlackInstallation,
        )
    }
}

private val ApiHttpMethod.asHttpMethod: HttpMethod
    get() = when (this) {
        ApiHttpMethod.delete -> HttpMethod.Delete
        ApiHttpMethod.get -> HttpMethod.Get
        ApiHttpMethod.head -> HttpMethod.Head
        ApiHttpMethod.options -> HttpMethod.Options
        ApiHttpMethod.patch -> HttpMethod.Patch
        ApiHttpMethod.post -> HttpMethod.Post
        ApiHttpMethod.put -> HttpMethod.Put
    }

private val Identity.ssoEntityId: String?
    get() = when (provider.isSingleSignOn) {
        true -> externalTeamId
        false -> null
    }
