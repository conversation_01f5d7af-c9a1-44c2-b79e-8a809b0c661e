package com.nextchaptersoftware.scmservice.handlers

import com.nextchaptersoftware.api.serialization.SerializationExtensions.decode
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores.enterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores.repoStore
import com.nextchaptersoftware.db.stores.Stores.scmTeamStore
import com.nextchaptersoftware.event.queue.handlers.EventHandler
import com.nextchaptersoftware.log.kotlin.debugAsync
import com.nextchaptersoftware.log.kotlin.warnAsync
import com.nextchaptersoftware.log.kotlin.withLoggingContextAsync
import com.nextchaptersoftware.maintenance.IdentityMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamLifecycleMaintenance
import com.nextchaptersoftware.maintenance.scm.ScmTeamMaintenance
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.bitbucket.models.BitbucketWebhookEvent
import com.nextchaptersoftware.scm.bitbucketdatacenter.models.BitbucketDataCenterWebhookEvent
import com.nextchaptersoftware.scm.gitlab.models.GitLabWebhookEvents
import com.nextchaptersoftware.scm.queue.payloads.BitbucketDataCenterWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.BitbucketWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.GitHubWebhookEvent
import com.nextchaptersoftware.scm.queue.payloads.GitHubWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.GitLabWebhookHeaders
import com.nextchaptersoftware.scm.queue.payloads.ScmWebhookEventPayload
import com.nextchaptersoftware.scm.services.RepoMaintenance
import com.nextchaptersoftware.scm.validators.GitHubEventValidator
import com.nextchaptersoftware.scmservice.handlers.bitbucket.BitbucketPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.bitbucketdatacenter.BitbucketDataCenterPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubAppAuthorizationHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubDiscussionCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubDiscussionHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubInstallationHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubInstallationRepositoriesHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubIssueCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubIssueHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubOrganizationHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestReviewCommentHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubPullRequestReviewHandler
import com.nextchaptersoftware.scmservice.handlers.github.GitHubRepositoryHandler
import com.nextchaptersoftware.scmservice.handlers.gitlab.GitLabPullRequestHandler
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.withTimeout
import mu.KotlinLogging

private val LOGGER = KotlinLogging.logger {}

class ScmEventWebhookHandler(
    private val itemProcessingTimeout: Duration = 30.seconds,
    private val gitHubDiscussionHandler: GitHubDiscussionHandler,
    private val gitHubDiscussionCommentHandler: GitHubDiscussionCommentHandler,
    private val gitHubIssueHandler: GitHubIssueHandler,
    private val gitHubIssueCommentHandler: GitHubIssueCommentHandler,
    private val gitHubPullRequestHandler: GitHubPullRequestHandler,
    private val gitHubPullRequestReviewHandler: GitHubPullRequestReviewHandler,
    private val gitHubEventValidator: GitHubEventValidator,
    private val bitbucketPullRequestHandler: BitbucketPullRequestHandler,
    private val bitbucketDataCenterPullRequestHandler: BitbucketDataCenterPullRequestHandler,
    private val gitLabPullRequestHandler: GitLabPullRequestHandler,
    private val gitHubPullRequestReviewCommentHandler: GitHubPullRequestReviewCommentHandler,
    private val scmAppApiFactory: ScmAppApiFactory,

    scmTeamLifecycleMaintenance: ScmTeamLifecycleMaintenance,
    repoMaintenance: RepoMaintenance,
    identityMaintenance: IdentityMaintenance,
    scmTeamMaintenance: ScmTeamMaintenance,
) : EventHandler {

    private val gitHubInstallationRepositoriesHandler = GitHubInstallationRepositoriesHandler(
        repoMaintenance = repoMaintenance,
    )
    private val gitHubOrganizationHandler = GitHubOrganizationHandler(
        scmTeamStore = scmTeamStore,
        repoStore = repoStore,
        enterpriseAppConfigStore = enterpriseAppConfigStore,
    )
    private val gitHubRepositoryHandler = GitHubRepositoryHandler(
        scmTeamStore = scmTeamStore,
        repoStore = repoStore,
        enterpriseAppConfigStore = enterpriseAppConfigStore,
    )
    private val gitHubInstallationHandler = GitHubInstallationHandler(
        scmTeamLifecycleMaintenance = scmTeamLifecycleMaintenance,
        scmTeamMaintenance = scmTeamMaintenance,
    )
    private val gitHubAppAuthorizationHandler = GitHubAppAuthorizationHandler(identityMaintenance)

    override suspend fun handle(event: String): Boolean {
        val scmWebhookEvent = event.decode<ScmWebhookEventPayload>()
        val provider = scmWebhookEvent.provider
        val headers = scmWebhookEvent.headers
        val body = scmWebhookEvent.body

        withLoggingContextAsync(headers.toMap() + ("provider" to provider.displayName)) {
            process(provider, headers, body)
        }

        return true
    }

    private suspend fun process(provider: Provider, headers: List<Pair<String, String>>, body: String) {
        withTimeout(itemProcessingTimeout) {
            when (provider) {
                Provider.GitHub,
                Provider.GitHubEnterprise,
                    -> handleGitHubWebhook(provider = provider, headers = headers, body = body)

                Provider.AzureDevOps,
                    -> handleAzureWebhook(headers = headers, body = body)

                Provider.Bitbucket,
                    -> handleBitbucketWebhook(headers = headers, body = body)

                Provider.BitbucketDataCenter,
                    -> handleBitbucketDataCenterWebhook(body = body, headers = headers)

                Provider.GitLab,
                Provider.GitLabSelfHosted,
                    -> handleGitLabWebhook(headers = headers, body = body)

                // Not applicable
                Provider.Asana,
                Provider.Aws,
                Provider.AwsIdentityCenter,
                Provider.BitbucketPipelines,
                Provider.Buildkite,
                Provider.CircleCI,
                Provider.Coda,
                Provider.Confluence,
                Provider.ConfluenceDataCenter,
                Provider.CustomIntegration,
                Provider.GenericSaml,
                Provider.GitHubActions,
                Provider.GitLabPipelines,
                Provider.GoogleDrive,
                Provider.GoogleDriveWorkspace,
                Provider.GoogleWorkspace,
                Provider.Jira,
                Provider.JiraDataCenter,
                Provider.Linear,
                Provider.MicrosoftEntra,
                Provider.Notion,
                Provider.Okta,
                Provider.PingOne,
                Provider.Slack,
                Provider.StackOverflowTeams,
                Provider.Unblocked,
                Provider.Web,
                    -> return@withTimeout
            }
        }
    }

    @Suppress("LongMethod", "SpreadOperator", "CyclomaticComplexMethod")
    private suspend fun handleGitHubWebhook(provider: Provider, headers: List<Pair<String, String>>, body: String) {
        val appId = headers.first { (key, _) -> key == GitHubWebhookHeaders.GitHubHookInstallationTargetID.name }.second
        val eventType = headers.first { (key, _) -> key == GitHubWebhookHeaders.GitHubEvent.name }.second
        val signature = headers.first { (key, _) -> key == GitHubWebhookHeaders.GitHubHubSignature256.name }.second

        val appConfig = when (provider.isEnterprise) {
            true -> enterpriseAppConfigStore.findByExternalAppId(appId)
            else -> null
        }

        if (!gitHubEventValidator.isEventValid(
                provider = provider,
                appConfig = appConfig,
                eventAppId = appId,
                eventSignature = signature,
                eventBody = body,
            )
        ) {
            LOGGER.warnAsync(
                "appId" to appId,
                "eventType" to eventType,
            ) { "Dropping invalid webhook" }
            return
        }

        val scm = Scm.fromProvider(provider, appConfig?.id)
        val appApi = scmAppApiFactory.getApi(orgId = null, scm = scm)

        when (GitHubWebhookEvent.fromString(eventType)) {
            GitHubWebhookEvent.Installation -> gitHubInstallationHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.InstallationRepositories -> gitHubInstallationRepositoriesHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.Issues -> gitHubIssueHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.Organization -> gitHubOrganizationHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.PullRequest -> gitHubPullRequestHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.PullRequestReview -> gitHubPullRequestReviewHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.PullRequestReviewComment -> gitHubPullRequestReviewCommentHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.Repository -> gitHubRepositoryHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.IssueComment -> gitHubIssueCommentHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.GithubAppAuthorization -> gitHubAppAuthorizationHandler.handle(body, scm)

            GitHubWebhookEvent.Discussion -> gitHubDiscussionHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.DiscussionComment -> gitHubDiscussionCommentHandler.handle(headers, body, appApi)

            GitHubWebhookEvent.BranchProtectionRule,
            GitHubWebhookEvent.CheckRun,
            GitHubWebhookEvent.CheckSuite,
            GitHubWebhookEvent.CodeScanningAlert,
            GitHubWebhookEvent.CommitComment,
            GitHubWebhookEvent.Create,
            GitHubWebhookEvent.Delete,
            GitHubWebhookEvent.DeployKey,
            GitHubWebhookEvent.Deployment,
            GitHubWebhookEvent.DeploymentStatus,
            GitHubWebhookEvent.Fork,
            GitHubWebhookEvent.Gollum,
            GitHubWebhookEvent.Label,
            GitHubWebhookEvent.MarketplacePurchase,
            GitHubWebhookEvent.Member,
            GitHubWebhookEvent.Membership,
            GitHubWebhookEvent.Meta,
            GitHubWebhookEvent.Milestone,
            GitHubWebhookEvent.OrgBlock,
            GitHubWebhookEvent.Package,
            GitHubWebhookEvent.PageBuild,
            GitHubWebhookEvent.Ping,
            GitHubWebhookEvent.Project,
            GitHubWebhookEvent.ProjectCard,
            GitHubWebhookEvent.ProjectColumn,
            GitHubWebhookEvent.ProjectsV2Item,
            GitHubWebhookEvent.Public,
            GitHubWebhookEvent.PullRequestReviewThread,
            GitHubWebhookEvent.Push,
            GitHubWebhookEvent.Release,
            GitHubWebhookEvent.RepositoryDispatch,
            GitHubWebhookEvent.RepositoryImport,
            GitHubWebhookEvent.RepositoryVulnerabilityAlert,
            GitHubWebhookEvent.SecurityAdvisory,
            GitHubWebhookEvent.SecurityAndAnalysis,
            GitHubWebhookEvent.Sponsorship,
            GitHubWebhookEvent.Star,
            GitHubWebhookEvent.Status,
            GitHubWebhookEvent.Team,
            GitHubWebhookEvent.TeamAdd,
            GitHubWebhookEvent.Watch,
            GitHubWebhookEvent.WorkflowDispatch,
            GitHubWebhookEvent.WorkflowJob,
            GitHubWebhookEvent.WorkflowRun,
                -> LOGGER.debugAsync { "Unhandled GitHub event type" }
        }
    }

    @Suppress("UnusedPrivateMember")
    private fun handleAzureWebhook(headers: List<Pair<String, String>>, body: String) {
        TODO() // FIXME: mrtn, azure devops
    }

    private suspend fun handleBitbucketWebhook(headers: List<Pair<String, String>>, body: String) {
        val eventType = BitbucketWebhookEvent.fromString(
            headers.first { (key, _) -> key == BitbucketWebhookHeaders.EventType.name }.second,
        )

        if (!eventType.subscribe) {
            LOGGER.debugAsync { "Receive Bitbucket event that we did not subscribe to; ignore it." }
            return
        }

        when (eventType) {
            BitbucketWebhookEvent.PullRequestApproved,
            BitbucketWebhookEvent.PullRequestChangesRequestCreated,
            BitbucketWebhookEvent.PullRequestChangesRequestRemoved,
            BitbucketWebhookEvent.PullRequestCommentCreated,
            BitbucketWebhookEvent.PullRequestCommentDeleted,
            BitbucketWebhookEvent.PullRequestCommentUpdated,
            BitbucketWebhookEvent.PullRequestCreated,
            BitbucketWebhookEvent.PullRequestFulfilled,
            BitbucketWebhookEvent.PullRequestRejected,
            BitbucketWebhookEvent.PullRequestUnapproved,
            BitbucketWebhookEvent.PullRequestUpdated,
                -> bitbucketPullRequestHandler.handle(body)

            BitbucketWebhookEvent.RepoCreated,
            BitbucketWebhookEvent.RepoDeleted,
            BitbucketWebhookEvent.RepoImported,
            BitbucketWebhookEvent.RepoTransfer,
            BitbucketWebhookEvent.RepoUpdated,
            BitbucketWebhookEvent.IssueCommentCreated,
            BitbucketWebhookEvent.IssueCreated,
            BitbucketWebhookEvent.IssueUpdated,
            BitbucketWebhookEvent.ProjectUpdated,
            BitbucketWebhookEvent.RepoCommitCommentCreated,
            BitbucketWebhookEvent.RepoCommitStatusCreated,
            BitbucketWebhookEvent.RepoCommitStatusUpdated,
            BitbucketWebhookEvent.RepoFork,
            BitbucketWebhookEvent.RepoPush,
                -> LOGGER.debugAsync { "Unhandled Bitbucket event type" }
        }
    }

    private suspend fun handleBitbucketDataCenterWebhook(headers: List<Pair<String, String>>, body: String) {
        val eventType = headers
            .first { (key, _) -> key == BitbucketDataCenterWebhookHeaders.EventType.name }
            .second
            .let(BitbucketDataCenterWebhookEvent::fromString)

        when (eventType) {
            BitbucketDataCenterWebhookEvent.PullRequestOpened,
            BitbucketDataCenterWebhookEvent.PullRequestModified,
            BitbucketDataCenterWebhookEvent.PullRequestMerged,
            BitbucketDataCenterWebhookEvent.PullRequestDeleted,
            BitbucketDataCenterWebhookEvent.PullRequestDeclined,
            BitbucketDataCenterWebhookEvent.PullRequestCommentAdded,
            BitbucketDataCenterWebhookEvent.PullRequestCommentEdited,
            BitbucketDataCenterWebhookEvent.PullRequestCommentDeleted,
                -> bitbucketDataCenterPullRequestHandler.handle(body)

            BitbucketDataCenterWebhookEvent.ProjectModified,
                -> LOGGER.debugAsync { "Unhandled Bitbucket Data Center event type" }
        }
    }

    private suspend fun handleGitLabWebhook(headers: List<Pair<String, String>>, body: String) {
        val eventType = GitLabWebhookEvents.fromHookLabel(
            headers.first { (key, _) -> key == GitLabWebhookHeaders.XGitlabEvent.name }.second,
        )
        val instance = headers.first { (key, _) -> key == GitLabWebhookHeaders.XGitlabInstance.name }.second

        val scm = when (instance) {
            "https://gitlab.com" -> Scm.GitLab

            else -> {
                LOGGER.warnAsync { "GitLab Self Hosted not supported yet; ignore it." }
                return
            }
        }

        if (!eventType.subscribe) {
            LOGGER.debugAsync { "Receive GitLab event that we did not subscribe to; ignore it." }
            return
        }

        when (eventType) {
            GitLabWebhookEvents.MergeRequest,
            GitLabWebhookEvents.Subgroup,
                -> gitLabPullRequestHandler.handle(body, scm)

            GitLabWebhookEvents.ConfidentialIssue,
            GitLabWebhookEvents.ConfidentialNote,
            GitLabWebhookEvents.Deployment,
            GitLabWebhookEvents.Issue,
            GitLabWebhookEvents.Job,
            GitLabWebhookEvents.Note,
            GitLabWebhookEvents.Pipeline,
            GitLabWebhookEvents.Push,
            GitLabWebhookEvents.Release,
            GitLabWebhookEvents.RepositoryUpdate,
            GitLabWebhookEvents.TagPush,
            GitLabWebhookEvents.WikiPage,
                -> LOGGER.debugAsync { "Unhandled GitLab event type" }
        }
    }
}
