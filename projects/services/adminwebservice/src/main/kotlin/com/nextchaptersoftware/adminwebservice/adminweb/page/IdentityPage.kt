package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.requiredId
import com.nextchaptersoftware.adminwebservice.adminweb.AdminProfile.profile
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.avatar
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.api.serialization.SerializationExtensions.encodePretty
import com.nextchaptersoftware.api.utils.CallExtensions.respondJson
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.models.ClientVersion
import com.nextchaptersoftware.db.models.Identity
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.db.stores.Stores.identityStore
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmUserApi
import com.nextchaptersoftware.scm.ScmUserApiFactory
import com.nextchaptersoftware.scm.services.ProfileService
import com.nextchaptersoftware.user.secret.UserSecretServiceResolver
import com.nextchaptersoftware.utils.KotlinUtils.doNothing
import com.nextchaptersoftware.utils.KotlinUtils.required
import com.sksamuel.hoplite.Secret
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.request.uri
import io.ktor.server.response.respondText
import io.ktor.server.routing.RoutingContext
import kotlinx.coroutines.flow.toList
import kotlinx.html.FlowContent
import kotlinx.html.TBODY
import kotlinx.html.ThScope
import kotlinx.html.a
import kotlinx.html.br
import kotlinx.html.code
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import org.jetbrains.exposed.sql.update

object IdentityPage {

    suspend fun RoutingContext.renderIdentityPage(
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()

        val identityId = call.parameters.requiredId("identityId", ::IdentityId)
        val identity = identityStore.findById(identityId = identityId).required()
        val clientVersions = Stores.clientVersionStore.find(listOf(identityId))
        val path = call.request.path()

        val actions = getActions(path = call.request.uri, adminIdentity = adminIdentity.identity, identity = identity)
        val related = listOfNotNull(
            identity.person?.let { personId ->
                MenuItem(
                    href = "$WEB_ROOT/people/$personId",
                    label = "Person",
                    description = "The related person.",
                )
            },
        )

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            relatedMenu { renderRelatedMenu(related) }
            actionMenu { renderActionMenu(actions, sort = false) }
            content {
                h1 { +page.label }
                renderIdentity(identity, clientVersions)

                if (identity.provider.isScmProvider) {
                    h3(classes = "mt-5") { +"Live APIs" }
                    renderScmApiActions(path)
                }
            }
        }
    }

    fun FlowContent.renderIdentity(identity: Identity, clientVersions: Map<IdentityId, List<ClientVersion>>) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("ID", identity.id)
                property("Avatar") { avatar(identity) }
                property("Has Unblocked Account", identity.hasAccount)
                property("Provider", identity.provider)
                property("Username", identity.username, formatAsCode = true)
                property("External ID", identity.externalId, formatAsCode = true)
                property("External Team ID", identity.externalTeamId, formatAsCode = true)
                property("Name", identity.displayName)
                property("Created", identity.createdAt)
                property("Modified", identity.modifiedAt)
                property("HTML Url", identity.htmlUrl)
                property("Primary Email", identity.primaryEmail)
                property("Emails (${identity.emails.size})", identity.emails.toString())
                property("Client Versions") {
                    clientVersions[identity.id]?.sortedBy { it.productAgent.name }?.map(::asBadge)
                }
                property("Is Bot", identity.isBot)
            }
        }
        insert(PropertyListTemplate()) {
            propertyList {
                property("Has Access Token") { asBadge(identity.hasAccessToken) }
                property("Has Refresh Token") { asBadge(identity.hasRefreshToken) }
                property("Access Token Expiry", identity.accessTokenExpiresAt)
                property("Refresh Token Expiry", identity.refreshTokenExpiresAt)
                property("Access Token Scope", identity.accessTokenScope)
                property("Prompting User to Reconnect in UI", identity.promptToReconnect)
                renderAccessToken(identity)
            }
        }
    }

    private fun TBODY.renderAccessToken(identity: Identity) {
        if (identity.hasAccessToken) {
            property("Access Token") {
                code {
                    val path = "/identities/${identity.id}"
                    attributes["hx-get"] = "$path/getAccessToken"
                    attributes["hx-trigger"] = "click"
                    attributes["hx-swap"] = "outerHTML"
                    +"*** Click to reveal ***"
                }
            }
        }
    }

    suspend fun RoutingContext.getAccessToken(
        userSecretServiceResolver: UserSecretServiceResolver,
    ) {
        val identity = call.parameters.requiredId("identityId", ::IdentityId).let {
            identityStore.get(identityId = it)
        }
        val accessToken = when (identity.provider) {
            Provider.Jira,
            Provider.Confluence,
                -> identity.rawAccessToken.required().value.let { Secret(String(it)) }

            else -> identity.rawAccessToken?.let {
                userSecretServiceResolver
                    .resolve(identity.provider)
                    .decrypt(identity.rawAccessToken.required())
            }
        }

        val html = buildString {
            append(accessToken?.value)
        }
        call.respondText(html)
    }

    private fun FlowContent.renderScmApiActions(
        path: String,
    ) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Live SCM API Requests") {
                    a(href = "$path/raw/scmAccounts") { +"ScmAccounts" }
                    br
                    a(href = "$path/raw/scmUser") { +"ScmUser" }
                }
            }
        }
    }

    private suspend fun RoutingContext.getScmUserApi(
        scmUserApiFactory: ScmUserApiFactory,
    ): ScmUserApi {
        val identity = call.parameters.requiredId("identityId", ::IdentityId).let {
            identityStore.get(identityId = it)
        }
        return scmUserApiFactory.getApiFromIdentity(
            orgId = null,
            identityId = identity.id,
            scm = Scm.fromIdentity(identity),
        )
    }

    suspend fun RoutingContext.rawScmAccounts(
        scmUserApiFactory: ScmUserApiFactory,
    ) {
        val accounts = getScmUserApi(scmUserApiFactory).use { it.accounts().toList() }
        call.respondJson {
            accounts.encodePretty()
        }
    }

    suspend fun RoutingContext.rawScmUser(
        scmUserApiFactory: ScmUserApiFactory,
    ) {
        val user = getScmUserApi(scmUserApiFactory).use { it.user() }
        call.respondJson {
            user.encodePretty()
        }
    }

    fun FlowContent.renderIdentities(identities: List<Identity>) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Profile" }
                    th(scope = ThScope.col) { +"Provider" }
                    th(scope = ThScope.col) { +"External ID / External Team ID" }
                    th(scope = ThScope.col) { +"Primary Email" }
                    th(scope = ThScope.col) { +"Added" }
                    th(scope = ThScope.col) { +"Status" }
                }
            }
            tbody(classes = "table-dark") {
                identities.sortedByDescending { it.createdAt }.forEach { identity ->
                    renderIdentityRow(identity)
                }
            }
        }
    }

    private fun TBODY.renderIdentityRow(identity: Identity) {
        tr {
            attributes["onclick"] = onClickAction("$WEB_ROOT/identities/${identity.id}")
            style = "cursor: pointer;"

            td { profile(identity) }
            td { asBadge(identity.provider) }
            td {
                code {
                    style = "overflow-wrap: anywhere"
                    +identity.externalId
                }
                div(classes = "small text-muted") {
                    +identity.externalTeamId
                }
            }
            td { +(identity.primaryEmail?.value ?: "-") }
            td { timeAgo(identity.createdAt) }
            td {
                asBadge(
                    identity.promptToReconnect,
                    trueText = "Disconnected",
                    trueStyle = BootstrapStyle.Warning,
                    falseText = "OK",
                    falseStyle = BootstrapStyle.Success,
                )
            }
        }
    }

    private fun getActions(
        path: String,
        adminIdentity: Identity,
        identity: Identity,
    ): List<MenuItem> {
        return buildList {
            when {
                adminIdentity.impersonatingIdentity == identity.id -> MenuItem(
                    href = "$WEB_ROOT/impersonate/stop",
                    label = "Stop Impersonating",
                    style = BootstrapStyle.Success,
                    description = """
                    Stop impersonating ${identity.displayName ?: identity.username}.
                    """,
                ).let(::add)

                (identity.person != null && identity.provider.isSignInCapable) -> MenuItem(
                    href = "$WEB_ROOT/impersonate/start?impersonatingIdentityId=${identity.id}",
                    label = "Impersonate",
                    style = BootstrapStyle.Success,
                    description = """
                    Impersonate ${identity.displayName ?: identity.username}'s ${identity.provider.displayName} account in all clients.
                    """,
                ).let(::add)

                else -> doNothing()
            }
            if (identity.provider.isScmProvider) {
                MenuItem(
                    href = "$path/refreshProfile",
                    label = "Refresh profile",
                    style = BootstrapStyle.Info,
                    description = """
                    Refresh the profile information for this identity.
                    """,
                ).let(::add)
            }
            if (identity.provider in listOf(Provider.GitHub, Provider.GitHubEnterprise)) {
                MenuItem(
                    href = "$path/deauthorizePerson",
                    label = "Deauthorize person",
                    style = BootstrapStyle.Danger,
                    description = """
                    Remotely deauthorize OAuth authorization grant for this identity in the SCM.
                    The user will be prompted to re-authorize their SCM account the next time they try to sign in.
                    """,
                ).let(::add)
            }
            if (identity.hasAccount) {
                MenuItem(
                    href = "$path/unlinkPersonFromIdentity",
                    label = "Disconnect identity",
                    style = BootstrapStyle.Danger,
                    description = """
                    Disconnect this identity from the person.
                    """,
                ).let(::add)
            }
            if (!identity.promptToReconnect) {
                MenuItem(
                    href = "$path/promptIdentityToReconnect",
                    label = "Prompt to reconnect",
                    style = BootstrapStyle.Danger,
                    description = """
                    Prompt the user to reconnect their account. Also logs the user out of this account.
                    """,
                ).let(::add)
            }
            if (identity.hasAccessToken || identity.hasRefreshToken) {
                MenuItem(
                    href = "$path/revokeIdentity",
                    label = "Revoke identity",
                    style = BootstrapStyle.Danger,
                    description = """
                    Revoke the identity by clearing all tokens and disconnecting from person.
                    """,
                ).let(::add)
            }
        }
    }

    suspend fun RoutingContext.promptIdentityToReconnect() {
        val identityId = call.parameters.requiredId("identityId", ::IdentityId)
        identityStore.promptToReconnect(identityId)
    }

    suspend fun RoutingContext.refreshProfileForIdentity(profileService: ProfileService) {
        val identityId = call.parameters.requiredId("identityId", ::IdentityId)
        profileService.refreshProfileForIdentityId(identityId)
    }

    suspend fun RoutingContext.unlinkPersonFromIdentity() {
        val identityId = call.parameters.requiredId("identityId", ::IdentityId)

        // Reset MemberModel.orgMember to a new org member without a person
        Stores.memberStore.getMembersByIdentity(identityId = identityId).forEach { memberInfo ->
            val newOrgMember = suspendedTransaction {
                OrgMemberDAO.new {
                    this.org = OrgDAO[memberInfo.org.id]
                    this.person = null
                }
            }

            suspendedTransaction {
                MemberModel.update({ MemberModel.id eq memberInfo.member.id }) {
                    it[orgMember] = newOrgMember.id
                }
            }
        }

        suspendedTransaction {
            IdentityModel.update({ IdentityModel.id eq identityId }) {
                it[this.person] = null
            }
        }
    }

    suspend fun RoutingContext.revokeIdentity() {
        val identityId = call.parameters.requiredId("identityId", ::IdentityId)
        identityStore.revoke(identityId)
    }
}
