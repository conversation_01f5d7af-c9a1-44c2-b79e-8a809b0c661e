package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.badge
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.github.models.AppAccess
import com.nextchaptersoftware.scm.github.models.AppEvent
import com.nextchaptersoftware.scm.github.models.AppPermission
import com.nextchaptersoftware.scm.github.models.GitHubAccountType
import com.nextchaptersoftware.scm.github.models.GitHubInstallation
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import kotlinx.coroutines.flow.toList
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.a
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.li
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr
import kotlinx.html.ul

object InstallationsPage {

    suspend fun RoutingContext.renderInstallationsPage(
        page: AdminPage,
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
    ) {
        val path = call.request.path()
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val scm = IntegrationPage.getScm(call, provider)

        val installations = scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
            it.installations().toList()
        }

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +page.label }

                installations.partition { it.account.type == GitHubAccountType.Organization }.let { (orgs, users) ->
                    if (orgs.isNotEmpty()) {
                        h3(classes = "mt-5") { +"Organization Installations" }
                        renderInstallations(orgs, path)
                    }
                    if (users.isNotEmpty()) {
                        h3(classes = "mt-5") { +"Personal Installations" }
                        renderInstallations(users, path)
                    }
                }
            }
        }
    }

    private fun FlowContent.renderInstallations(installations: List<GitHubInstallation>, path: String) {
        table(classes = "table table-hover align-middle searchable") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Account Name" }
                    th(scope = ThScope.col) { +"Permissions" }
                    th(scope = ThScope.col) { +"Events" }
                    th(scope = ThScope.col) { +"Repository Selection" }
                    th(scope = ThScope.col) { +"Supports CI" }
                    th(scope = ThScope.col) { +"Traits" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSort") { +"View" }
                }
            }
            tbody(classes = "table-dark") {
                installations.sortedByDescending { it.account.login }.sortedBy { it.targetType }.forEach { installation ->
                    tr {
                        td { code { +installation.account.login } }
                        td { renderPermissionsChecklist(installation.permissions) }
                        td { renderEventsChecklist(installation.events) }
                        td { +installation.repositorySelection.name }
                        td { asBadge(installation.supportsCI) }
                        td {
                            if (!installation.isValid) {
                                badge(bootstrapStyle = BootstrapStyle.Danger) {
                                    +"Invalid"
                                }
                            }
                            if (installation.isSuspended) {
                                badge(bootstrapStyle = BootstrapStyle.Warning) {
                                    +"Suspended"
                                }
                            }
                        }
                        td { timeAgo(installation.createdAt) }
                        td {
                            a(
                                classes = "btn btn-outline-info btn-sm mx-1",
                                href = "$path/${installation.installationId}",
                            ) { +"Installation" }
                        }
                    }
                }
            }
        }
    }

    // FIXME move
    fun FlowContent.renderPermissionsChecklist(permissions: Map<AppPermission, AppAccess>) {
        ul(classes = "ul-checkmark") {
            style = "padding-left: 0;"
            AppPermission.entries.forEach { permission ->
                val isExpected = AppPermission.REQUESTED_PERMISSIONS.contains(permission)
                val isPresent = permissions.contains(permission)
                when {
                    isExpected && isPresent -> li(classes = "checked") { +permission.name }
                    isExpected && !isPresent -> li(classes = "unchecked") { +"${permission.name} (missing)" }
                    !isExpected && isPresent -> li { +"${permission.name} (extra)" }
                }
            }
        }
    }

    // FIXME move
    fun FlowContent.renderEventsChecklist(events: List<AppEvent>) {
        ul(classes = "ul-checkmark") {
            style = "padding-left: 0;"
            (AppEvent.REQUESTED_EVENTS + events).toSet().sortedBy { it.name }.forEach { event ->
                val isExpected = AppEvent.REQUESTED_EVENTS.contains(event)
                val isPresent = events.contains(event)
                when {
                    isExpected && isPresent -> li(classes = "checked") { +event.name }
                    isExpected && !isPresent -> li(classes = "unchecked text-danger") { +"${event.name} (missing)" }
                    !isExpected && isPresent -> li { +"${event.name} (extra)" }
                }
            }
        }
    }
}
