package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.component.Click.onClickAction
import com.nextchaptersoftware.adminwebservice.adminweb.component.Time.timeAgo
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.IntegrationPage.asConfig
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.config.ScmConfig
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.ThScope
import kotlinx.html.a
import kotlinx.html.div
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.style
import kotlinx.html.table
import kotlinx.html.tbody
import kotlinx.html.td
import kotlinx.html.th
import kotlinx.html.thead
import kotlinx.html.tr

object IntegrationsPage {

    suspend fun RoutingContext.renderIntegrationsPage(
        page: AdminPage,
        enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
        scmAppApiFactory: ScmAppApiFactory,
        scmConfig: ScmConfig = ScmConfig.INSTANCE,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val path = call.request.path()
        val adminIdentity = call.getAdminIdentity()

        val ghAppConfig = scmConfig.githubCloud?.let { config ->
            scmAppApiFactory.getApi(orgId = null, scm = Scm.GitHub).v3App().use {
                it.app()
            }.asConfig(config)
        }

        val appConfigs: List<EnterpriseAppConfig> = enterpriseAppConfigStore.list()

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +page.label }

                Provider.GitHub.also {
                    h3(classes = "mt-5") { +"${it.displayName} Apps" }
                    renderApps(listOfNotNull(ghAppConfig), path, it)
                }

                Provider.GitHubEnterprise.also {
                    h3(classes = "mt-5") { +"${it.displayName} Apps" }
                    renderApps(appConfigs.filterIsInstance<EnterpriseAppConfig.GitHub>(), path, it)
                    createNewApp(it)
                }

                Provider.GitLabSelfHosted.also {
                    h3(classes = "mt-5") { +"${it.displayName} Apps" }
                    renderApps(appConfigs.filterIsInstance<EnterpriseAppConfig.GitLab>(), path, it)
                }

                Provider.BitbucketDataCenter.also {
                    h3(classes = "mt-5") { +"${it.displayName} Apps" }
                    renderApps(appConfigs.filterIsInstance<EnterpriseAppConfig.Bitbucket>(), path, it)
                }
            }
        }
    }

    private fun FlowContent.renderApps(apps: List<EnterpriseAppConfig>, path: String, provider: Provider) {
        table(classes = "table table-hover align-middle") {
            thead {
                tr {
                    th(scope = ThScope.col) { +"Host and Port" }
                    th(scope = ThScope.col) { +"App ID" }
                    th(scope = ThScope.col) { +"App Slug" }
                    th(scope = ThScope.col) { +"Owner" }
                    th(scope = ThScope.col, classes = "noSearch") { +"Created" }
                    th(scope = ThScope.col, classes = "noSort") { +"View" }
                }
            }
            tbody(classes = "table-dark") {
                apps.sortedByDescending { it.createdAt }.forEach { app ->
                    val integrationPath = when (provider) {
                        Provider.GitHub,
                            -> "$path/${provider.name}"

                        Provider.GitHubEnterprise,
                        Provider.GitLabSelfHosted,
                        Provider.BitbucketDataCenter,
                            -> "$path/${provider.name}/${app.id}"

                        else -> error("Unexpected provider: $provider")
                    }
                    tr {
                        attributes["onclick"] = onClickAction(integrationPath)
                        style = "cursor: pointer;"

                        td { +app.hostAndPort }
                        td { +app.externalAppId }
                        td { (app as? EnterpriseAppConfig.GitHub)?.let { +app.slug } ?: +"N/A" }
                        td { (app as? EnterpriseAppConfig.GitHub)?.let { +app.owner } ?: +"N/A" }
                        td { timeAgo(app.createdAt) }
                        td {
                            a(classes = "btn btn-outline-info btn-sm mx-1", href = integrationPath) {
                                +"App"
                            }
                            if (provider in listOf(Provider.GitHub, Provider.GitHubEnterprise)) {
                                a(classes = "btn btn-outline-info btn-sm mx-1", href = "$integrationPath/installations") {
                                    +"Installations"
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun FlowContent.createNewApp(provider: Provider) {
        val newAppPath = "/integrations/${provider.name}/new"
        div(classes = "row mt-4") {
            div(classes = "col-12 d-grid") {
                a(classes = "btn btn-primary", href = newAppPath) {
                    +"Create New ${provider.displayName} App"
                }
            }
        }
    }
}
