package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.SearchPage.scrapeUuids
import com.nextchaptersoftware.adminwebservice.adminweb.template.WideContentTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.common.Database.suspendedTransaction
import com.nextchaptersoftware.db.common.IdAny
import com.nextchaptersoftware.db.common.TableExtensions.getForeignKeyColumns
import com.nextchaptersoftware.db.common.column.TypedUUIDColumnType
import com.nextchaptersoftware.db.models.BuildDAO
import com.nextchaptersoftware.db.models.BuildId
import com.nextchaptersoftware.db.models.BuildJobDAO
import com.nextchaptersoftware.db.models.BuildJobId
import com.nextchaptersoftware.db.models.BuildJobModel
import com.nextchaptersoftware.db.models.BuildModel
import com.nextchaptersoftware.db.models.BuildTriageDAO
import com.nextchaptersoftware.db.models.BuildTriageId
import com.nextchaptersoftware.db.models.BuildTriageModel
import com.nextchaptersoftware.db.models.CIScmDAO
import com.nextchaptersoftware.db.models.CIScmId
import com.nextchaptersoftware.db.models.CIScmModel
import com.nextchaptersoftware.db.models.ConfluenceSiteDAO
import com.nextchaptersoftware.db.models.ConfluenceSiteId
import com.nextchaptersoftware.db.models.ConfluenceSiteModel
import com.nextchaptersoftware.db.models.ConfluenceSpaceDAO
import com.nextchaptersoftware.db.models.ConfluenceSpaceId
import com.nextchaptersoftware.db.models.ConfluenceSpaceModel
import com.nextchaptersoftware.db.models.GoogleDriveFolderDAO
import com.nextchaptersoftware.db.models.GoogleDriveFolderId
import com.nextchaptersoftware.db.models.GoogleDriveFolderModel
import com.nextchaptersoftware.db.models.IdentityDAO
import com.nextchaptersoftware.db.models.IdentityId
import com.nextchaptersoftware.db.models.IdentityModel
import com.nextchaptersoftware.db.models.IngestionDAO
import com.nextchaptersoftware.db.models.IngestionId
import com.nextchaptersoftware.db.models.IngestionModel
import com.nextchaptersoftware.db.models.InstallationDAO
import com.nextchaptersoftware.db.models.InstallationId
import com.nextchaptersoftware.db.models.InstallationModel
import com.nextchaptersoftware.db.models.JiraSiteDAO
import com.nextchaptersoftware.db.models.JiraSiteId
import com.nextchaptersoftware.db.models.JiraSiteModel
import com.nextchaptersoftware.db.models.LinearIngestionDAO
import com.nextchaptersoftware.db.models.LinearIngestionId
import com.nextchaptersoftware.db.models.LinearIngestionModel
import com.nextchaptersoftware.db.models.LinearOrganizationDAO
import com.nextchaptersoftware.db.models.LinearOrganizationId
import com.nextchaptersoftware.db.models.LinearOrganizationModel
import com.nextchaptersoftware.db.models.MemberDAO
import com.nextchaptersoftware.db.models.MemberId
import com.nextchaptersoftware.db.models.MemberModel
import com.nextchaptersoftware.db.models.OrgBillingDAO
import com.nextchaptersoftware.db.models.OrgBillingId
import com.nextchaptersoftware.db.models.OrgBillingModel
import com.nextchaptersoftware.db.models.OrgDAO
import com.nextchaptersoftware.db.models.OrgId
import com.nextchaptersoftware.db.models.OrgMemberDAO
import com.nextchaptersoftware.db.models.OrgMemberId
import com.nextchaptersoftware.db.models.OrgMemberModel
import com.nextchaptersoftware.db.models.OrgModel
import com.nextchaptersoftware.db.models.PersonDAO
import com.nextchaptersoftware.db.models.PersonId
import com.nextchaptersoftware.db.models.PersonModel
import com.nextchaptersoftware.db.models.PlanDAO
import com.nextchaptersoftware.db.models.PlanId
import com.nextchaptersoftware.db.models.PlanModel
import com.nextchaptersoftware.db.models.ProviderRole
import com.nextchaptersoftware.db.models.PullRequestIngestionDAO
import com.nextchaptersoftware.db.models.PullRequestIngestionId
import com.nextchaptersoftware.db.models.PullRequestIngestionModel
import com.nextchaptersoftware.db.models.RepoDAO
import com.nextchaptersoftware.db.models.RepoId
import com.nextchaptersoftware.db.models.RepoModel
import com.nextchaptersoftware.db.models.ScmTeamDAO
import com.nextchaptersoftware.db.models.ScmTeamId
import com.nextchaptersoftware.db.models.ScmTeamModel
import com.nextchaptersoftware.db.models.ServiceModel.Companion.allServiceModels
import com.nextchaptersoftware.db.models.SlackChannelDAO
import com.nextchaptersoftware.db.models.SlackChannelId
import com.nextchaptersoftware.db.models.SlackChannelIngestionDAO
import com.nextchaptersoftware.db.models.SlackChannelIngestionId
import com.nextchaptersoftware.db.models.SlackChannelIngestionModel
import com.nextchaptersoftware.db.models.SlackChannelModel
import com.nextchaptersoftware.db.models.SlackIngestionDAO
import com.nextchaptersoftware.db.models.SlackIngestionId
import com.nextchaptersoftware.db.models.SlackIngestionModel
import com.nextchaptersoftware.db.models.SocialNetworkDAO
import com.nextchaptersoftware.db.models.SocialNetworkId
import com.nextchaptersoftware.db.models.SocialNetworkModel
import com.nextchaptersoftware.db.models.ThreadDAO
import com.nextchaptersoftware.db.models.ThreadId
import com.nextchaptersoftware.db.models.ThreadModel
import com.nextchaptersoftware.db.models.ValueId
import com.nextchaptersoftware.db.models.ValueIdConverter
import com.nextchaptersoftware.db.models.WebIngestionSiteDAO
import com.nextchaptersoftware.db.models.WebIngestionSiteId
import com.nextchaptersoftware.db.models.WebIngestionSiteModel
import com.nextchaptersoftware.db.models.ingestionStatus
import com.nextchaptersoftware.graphs.Graph
import com.nextchaptersoftware.graphs.Node
import com.nextchaptersoftware.utils.CollectionsUtils.nullIfEmpty
import com.nextchaptersoftware.utils.nullIfBlank
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.routing.RoutingContext
import java.util.Locale
import java.util.PriorityQueue
import java.util.Queue
import java.util.UUID
import kotlin.math.min
import kotlinx.html.ButtonType
import kotlinx.html.FlowContent
import kotlinx.html.FormMethod
import kotlinx.html.button
import kotlinx.html.code
import kotlinx.html.div
import kotlinx.html.form
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.p
import kotlinx.html.script
import kotlinx.html.small
import kotlinx.html.textArea
import kotlinx.html.textInput
import kotlinx.html.unsafe
import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.dao.id.IdTable
import org.jetbrains.exposed.sql.Column
import org.jetbrains.exposed.sql.EntityIDColumnType
import org.jetbrains.exposed.sql.Op
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq
import org.jetbrains.exposed.sql.Table
import org.jetbrains.exposed.sql.UUIDColumnType
import org.jetbrains.exposed.sql.intLiteral

private const val DEF_DEPTH = 1
private const val MAX_DEPTH = 10
private const val DEF_NODES = 20
private const val MAX_NODES = 500

@Suppress("LargeClass")
object VisualizePage {
    @Suppress("LongMethod", "CyclomaticComplexMethod")
    suspend fun RoutingContext.renderVisualizePage(
        page: AdminPage,
    ) {
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val path = call.request.path()
        val query = call.request.queryParameters["q"] ?: ""
        val maxDepth = call.request.queryParameters["depth"]?.toIntOrNull()?.let { min(MAX_DEPTH, it) } ?: DEF_DEPTH
        val maxNodes = call.request.queryParameters["nodes"]?.toIntOrNull()?.let { min(MAX_NODES, it) } ?: DEF_NODES
        val rootType = call.request.queryParameters["model"]?.nullIfBlank()
        val exclude = call.request.queryParameters["exclude"].orEmpty()

        val graph = buildGraph(
            uuids = scrapeUuids(query),
            maxDepth = maxDepth,
            maxNodes = maxNodes,
            rootType = rootType,
            exclude = exclude,
        )

        call.respondHtmlTemplate(WideContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            content {
                h1 { +"Visualize" }
                renderQuery(query)
                renderForm(path, query, graph)

                h3 { +"Graph" }
                renderGraph(graph)
            }
        }
    }

    private fun FlowContent.renderQuery(query: String) {
        when {
            query.isBlank() -> {
                p(classes = "text-danger") {
                    +"No query entered."
                }
            }

            else -> {
                code {
                    +""""$query""""
                }
            }
        }
    }

    private fun FlowContent.renderForm(
        path: String,
        query: String,
        graph: RecordGraph,
    ) {
        form(
            action = path,
            method = FormMethod.get,
            classes = "list-group-item list-group-item-action flex-column d-grid gap-1",
        ) {
            div(classes = "row") {
                div(classes = "col-3 form-group") {
                    small(classes = "text-muted") { +"Query" }
                    textArea(classes = "form-control", cols = "1", rows = "10") {
                        name = "q"
                        +query
                    }
                }
                div(classes = "col-3") {
                    div(classes = "form-group") {
                        small(classes = "text-muted") { +"Max Depth" }
                        textInput(classes = "form-control") {
                            name = "depth"
                            value = graph.maxDepth.toString()
                        }
                    }
                    div(classes = "form-group") {
                        small(classes = "text-muted") { +"Max Nodes" }
                        textInput(classes = "form-control") {
                            name = "nodes"
                            value = graph.maxNodes.toString()
                        }
                    }
                    div(classes = "form-group") {
                        small(classes = "text-muted") { +"Model" }
                        textInput(classes = "form-control") {
                            name = "model"
                            value = graph.rootType ?: ""
                        }
                    }
                    div(classes = "form-group") {
                        small(classes = "text-muted") { +"Exclude Models (comma-separated regular expressions)" }
                        textInput(classes = "form-control") {
                            name = "exclude"
                            value = graph.exclude
                        }
                    }
                }
            }
            div(classes = "row col-6 form-group") {
                button(classes = "btn btn-primary mb-1", type = ButtonType.submit) {
                    +"Submit"
                }
            }
        }
    }

    private fun FlowContent.renderGraph(graph: RecordGraph) {
        when {
            graph.isEmpty() -> {
                p(classes = "text-danger") {
                    +"No match found."
                }
            }

            else -> {
                div(classes = "col-12") {
                    attributes["id"] = "graph"
                    attributes["style"] = "text-align: center;"
                }
                script(src = "https://d3js.org/d3.v7.min.js") {}
                script(src = "https://unpkg.com/@hpcc-js/wasm@2.20.0/dist/graphviz.umd.js") {}
                script(src = "https://unpkg.com/d3-graphviz@5.6.0/build/d3-graphviz.js") {}
                script {
                    unsafe {
                        raw(
                            """
                                |// query count: ${graph.queryCount}
                                |let graph = `
                                |   digraph Visualize {
                                |       rankdir = LR
                                |       node [
                                |           colorscheme = $SCHEME_NAME
                                |           style = filled
                                |       ]
                                |       // nodes: ${graph.size}
                                |       ${graph.renderNodes()}
                                |       // edges: ${graph.edges.size}
                                |       ${graph.renderEdges()}
                                |       // labels: ${graph.labels.size}
                                |       ${graph.renderLabels()}
                                |   }`
                                |
                                |let graphviz = d3.select("#graph").graphviz()
                                |
                                |graphviz.renderDot(graph)
                            """.trimMargin(),
                        )
                    }
                }
            }
        }
    }

    private fun RecordGraph.renderNodes(): String = buildString {
        val paramDepth = when {
            maxDepth == DEF_DEPTH -> ""
            else -> "&depth=$maxDepth"
        }
        val paramNodes = when {
            maxNodes == DEF_NODES -> ""
            else -> "&nodes=$maxNodes"
        }
        val paramExclude = when {
            exclude.isBlank() -> ""
            else -> "&exclude=$exclude"
        }
        nodes.forEach { node ->
            val paramModel = "&model=${node.model::class.simpleName}"
            """
                |  "${node.key}" [
                |    shape="box"
                |    label="${node.label.replace("\"", "\\\\\"")}"
                |    color="${node.color}"
                |    depth="${node.depth}"
                |    href="/visualize?q=${node.id}$paramModel$paramDepth$paramNodes$paramExclude"
                |  ]
            """.trimMargin().also(::append)
            append("\n")
        }
    }

    private fun RecordGraph.renderEdges(): String = buildString {
        edges.forEach { edge ->
            val attrs = edge.label as? RecordEdgeLabel
            val style = listOfNotNull(
                    attrs?.label?.let { "label=\"$it\"" },
                    attrs?.style?.let { "style=\"$it\"" },
                )
                .nullIfEmpty()
                ?.joinToString(separator = ", ", prefix = "[", postfix = "]")
                ?: ""
            """
                | "${edge.source.key}" -> "${edge.target.key}" $style
            """.trimMargin().also(::append)
            append("\n")
        }
    }

    private fun RecordGraph.renderLabels(): String = buildString {
        labels.forEachIndexed { k, node ->
            val i = k + 1
            """
                | "label-$i" [ label="[$i]", tooltip="match number #$i" ]
                | { rank=same; "label-$i" -> "${node.key}" }
            """.trimMargin().also(::append)
            append("\n")
        }
    }

    private val IdTable<*>.modelName: String get() = this::class.simpleName ?: "(none)"

    private suspend fun buildGraph(
        uuids: Set<UUID>,
        maxDepth: Int,
        maxNodes: Int,
        rootType: String?,
        exclude: String,
    ): RecordGraph {
        val graph = RecordGraph(
            maxDepth = maxDepth,
            maxNodes = maxNodes,
            rootType = rootType,
            exclude = exclude,
        )

        val type = rootType?.let {
            allServiceModels.find { it.modelName == rootType }
        }

        uuids.forEach { uuid ->
            graph.pending.add(
                GraphSearch(
                    uuid = uuid,
                    type = type,
                ) {
                    graph.labels.add(it)
                },
            )
        }

        while (graph.pending.isNotEmpty()) {
            val input = graph.pending.remove()
            graph.search(input).onEach { node ->
                input.onResult(node)
            }
        }

        return graph
    }

    private val searchModelMap = mapOf<IdTable<*>, suspend (UUID, Int) -> Record?>(
        BuildModel to ::searchBuild,
        BuildJobModel to ::searchBuildJob,
        BuildTriageModel to ::searchBuildTriage,
        CIScmModel to ::searchCIScm,
        ConfluenceSiteModel to ::searchConfluenceSite,
        ConfluenceSpaceModel to ::searchConfluenceSpace,
        GoogleDriveFolderModel to ::searchGoogleDriveFolder,
        IdentityModel to ::searchIdentity,
        IngestionModel to ::searchIngestion,
        InstallationModel to ::searchInstallation,
        JiraSiteModel to ::searchJiraSite,
        LinearIngestionModel to ::searchLinearIngestion,
        LinearOrganizationModel to ::searchLinearOrganization,
        MemberModel to ::searchMember,
        OrgBillingModel to ::searchOrgBilling,
        OrgMemberModel to ::searchOrgMember,
        OrgModel to ::searchOrg,
        PersonModel to ::searchPerson,
        PlanModel to ::searchPlan,
        PullRequestIngestionModel to ::searchPullRequestIngestionModel,
        RepoModel to ::searchRepo,
        ScmTeamModel to ::searchScmTeam,
        SlackChannelIngestionModel to ::searchSlackChannelIngestion,
        SlackChannelModel to ::searchSlackChannel,
        SlackIngestionModel to ::searchSlackIngestion,
        SocialNetworkModel to ::searchSocialNetwork,
        ThreadModel to ::searchThread,
        WebIngestionSiteModel to ::searchWebIngestionSite,
    )

    private val searchModels = searchModelMap + (allServiceModels - searchModelMap.keys).map { model ->
        model to searcherGeneric(model)
    }

    private suspend fun RecordGraph.search(
        input: GraphSearch,
    ): List<Record> = search(
        id = input.uuid,
        type = input.type,
        depth = input.depth,
    )

    private suspend fun RecordGraph.search(
        id: UUID,
        depth: Int,
        type: Table? = null,
    ): List<Record> {
        if (visited.contains(id) || depth > maxDepth || size > maxNodes) {
            return emptyList()
        }

        visited.add(id)

        val models = searchModels.filter { searching.contains(it.key) }

        val records = buildList {
            when (type) {
                null -> models.values.forEach { searchFn ->
                    queryCount += 1
                    searchFn(id, depth)?.also(::add)
                }

                else -> models[type]
                    .also { queryCount += 1 }
                    ?.invoke(id, depth)?.also(::add)
            }
        }

        return records.onEach {
            addNode(it)
            discoverForeignKeyEdgesFrom(it, depth)
            discoverForeignKeyEdgesTo(it, depth)
        }
    }

    private fun createRecord(
        id: UUID,
        depth: Int,
        model: IdTable<*>,
        attrs: List<Any?>,
    ): Record {
        val props = attrs
            .filterNotNull()
            .ifEmpty { listOf(id) }
            .joinToString(", ")
        val type = model::class.java.simpleName.removeSuffix("Model")
        return Record(
            id = extractUUID(id),
            depth = depth,
            model = model,
            label = "$type($props)",
        )
    }

    private fun searcherGeneric(model: IdTable<*>): suspend (UUID, Int) -> Record? = { id, depth ->
        val exists = suspendedTransaction {
            model.select(intLiteral(1)).where { model.id.wrappedEquals(id) }.empty().not()
        }
        exists.takeIf { it }?.let {
            createRecord(
                id = id,
                depth = depth,
                model = model,
                attrs = emptyList(),
            )
        }
    }

    private suspend fun searchBuild(id: UUID, depth: Int): Record? = suspendedTransaction {
        val vid = id.let(::BuildId)
        BuildDAO.findById(vid)?.asDataModel()?.let { build ->
            createRecord(
                id = id,
                depth = depth,
                model = BuildModel,
                attrs = listOf(
                    build.externalId,
                    build.headSha,
                    build.status,
                    build.result,
                ),
            )
        }
    }

    private suspend fun searchBuildJob(id: UUID, depth: Int): Record? = suspendedTransaction {
        val vid = id.let(::BuildJobId)
        BuildJobDAO.findById(vid)?.asDataModel()?.let { job ->
            createRecord(
                id = id,
                depth = depth,
                model = BuildJobModel,
                attrs = listOf(
                    job.externalId,
                    job.displayName,
                    job.status,
                    job.result,
                ),
            )
        }
    }

    private suspend fun searchBuildTriage(id: UUID, depth: Int): Record? = suspendedTransaction {
        val vid = id.let(::BuildTriageId)
        BuildTriageDAO.findById(vid)?.asDataModel()?.let { triage ->
            createRecord(
                id = id,
                depth = depth,
                model = BuildTriageModel,
                attrs = listOf(
                    triage.state,
                ),
            )
        }
    }

    private suspend fun searchCIScm(id: UUID, depth: Int): Record? {
        val vid = id.let(::CIScmId)
        return suspendedTransaction { CIScmDAO.findById(vid)?.asDataModel() }?.let { ciScm ->
            createRecord(
                id = id,
                depth = depth,
                model = CIScmModel,
                attrs = listOf(
                    "mode=${ciScm.mode.name}",
                ),
            )
        }
    }

    private suspend fun searchConfluenceSite(id: UUID, depth: Int): Record? {
        return suspendedTransaction { ConfluenceSiteDAO.findById(id.let(::ConfluenceSiteId))?.asDataModel() }?.let { confluenceSite ->
            createRecord(
                id = id,
                depth = depth,
                model = ConfluenceSiteModel,
                attrs = listOf(
                    confluenceSite.name,
                    confluenceSite.siteId,
                    confluenceSite.confluenceSpaceIngestionType,
                ),
            )
        }
    }

    private suspend fun searchConfluenceSpace(id: UUID, depth: Int): Record? {
        return suspendedTransaction { ConfluenceSpaceDAO.findById(id.let(::ConfluenceSpaceId))?.asDataModel() }?.let { confluenceSpace ->
            createRecord(
                id = id,
                depth = depth,
                model = ConfluenceSpaceModel,
                attrs = listOf(
                    confluenceSpace.spaceName,
                ),
            )
        }
    }

    private suspend fun searchGoogleDriveFolder(id: UUID, depth: Int): Record? {
        val googleDriveFolderId = id.let(::GoogleDriveFolderId)
        return suspendedTransaction { GoogleDriveFolderDAO.findById(googleDriveFolderId)?.asDataModel() }?.let { googleDriveFolder ->
            createRecord(
                id = id,
                depth = depth,
                model = GoogleDriveFolderModel,
                attrs = listOf(
                    googleDriveFolder.name,
                ),
            )
        }
    }

    private suspend fun searchIdentity(id: UUID, depth: Int): Record? {
        val identityId = id.let(::IdentityId)
        return suspendedTransaction { IdentityDAO.findById(identityId)?.asDataModel() }?.let { identity ->
            createRecord(
                id = id,
                depth = depth,
                model = IdentityModel,
                attrs = listOf(
                    identity.displayName ?: identity.username,
                    identity.provider,
                ),
            )
        }
    }

    private suspend fun searchOrg(id: UUID, depth: Int): Record? {
        val orgId = id.let(::OrgId)
        return suspendedTransaction { OrgDAO.findById(orgId)?.asDataModel() }?.let { org ->
            createRecord(
                id = id,
                depth = depth,
                model = OrgModel,
                attrs = listOf(
                    org.displayName,
                ),
            )
        }
    }

    private suspend fun searchOrgBilling(id: UUID, depth: Int): Record? {
        val orgBillingId = id.let(::OrgBillingId)
        return suspendedTransaction { OrgBillingDAO.findById(orgBillingId)?.asDataModel() }?.let { orgBilling ->
            createRecord(
                id = id,
                depth = depth,
                model = OrgBillingModel,
                attrs = listOf(
                    orgBilling.rate ?: orgBilling.nextPlanRate,
                ),
            )
        }
    }

    private suspend fun searchOrgMember(id: UUID, depth: Int): Record? {
        val orgMemberId = id.let(::OrgMemberId)
        return suspendedTransaction { OrgMemberDAO.findById(orgMemberId)?.asDataModel() }?.let { orgMember ->
            createRecord(
                id = id,
                depth = depth,
                model = OrgMemberModel,
                attrs = listOf(
                    orgMember.selectedRole,
                    orgMember.id,
                ),
            )
        }
    }

    private suspend fun searchIngestion(id: UUID, depth: Int): Record? {
        val ingestionId = id.let(::IngestionId)
        return suspendedTransaction { IngestionDAO.findById(ingestionId)?.asDataModel() }?.let { ingestion ->
            createRecord(
                id = id,
                depth = depth,
                model = IngestionModel,
                attrs = listOf(
                    ingestion.provider,
                    ingestion.ingestionStatus,
                ),
            )
        }
    }

    private suspend fun searchInstallation(id: UUID, depth: Int): Record? {
        val installationId = id.let(::InstallationId)
        return suspendedTransaction { InstallationDAO.findById(installationId)?.asDataModel() }?.let { installation ->
            createRecord(
                id = id,
                depth = depth,
                model = InstallationModel,
                attrs = listOf(
                    installation.provider,
                    installation.displayName,
                ),
            )
        }
    }

    private suspend fun searchJiraSite(id: UUID, depth: Int): Record? {
        val jiraSiteId = id.let(::JiraSiteId)
        return suspendedTransaction { JiraSiteDAO.findById(jiraSiteId)?.asDataModel() }?.let { jiraSite ->
            createRecord(
                id = id,
                depth = depth,
                model = JiraSiteModel,
                attrs = listOf(
                    jiraSite.name,
                    jiraSite.siteId,
                    jiraSite.jiraProjectIngestionType,
                ),
            )
        }
    }

    private suspend fun searchLinearIngestion(id: UUID, depth: Int): Record? {
        val vid = id.let(::LinearIngestionId)
        return suspendedTransaction { LinearIngestionDAO.findById(vid)?.asDataModel() }?.let { linearIngestion ->
            createRecord(
                id = id,
                depth = depth,
                model = LinearIngestionModel,
                attrs = listOf(
                    linearIngestion.status,
                ),
            )
        }
    }

    private suspend fun searchLinearOrganization(id: UUID, depth: Int): Record? {
        val vid = id.let(::LinearOrganizationId)
        return suspendedTransaction { LinearOrganizationDAO.findById(vid)?.asDataModel() }?.let { linearOrganization ->
            createRecord(
                id = id,
                depth = depth,
                model = LinearOrganizationModel,
                attrs = listOf(
                    linearOrganization.name,
                    linearOrganization.linearTeamIngestionType,
                ),
            )
        }
    }

    private suspend fun searchMember(id: UUID, depth: Int): Record? {
        val memberId = id.let(::MemberId)
        return suspendedTransaction { MemberDAO.findById(memberId)?.asDataModel() }?.let { member ->
            val role = when (member.providerRole) {
                ProviderRole.None -> null
                else -> member.providerRole
            }
            createRecord(
                id = id,
                depth = depth,
                model = MemberModel,
                attrs = listOf(
                    role,
                    member.isPrimaryMember.takeIf { it }?.let { "Primary" }
                        ?: member.isCurrentMember.takeIf { it }?.let { "Current" },
                    member.id,
                ),
            )
        }
    }

    private suspend fun searchPerson(id: UUID, depth: Int): Record? {
        val personId = id.let(::PersonId)
        return suspendedTransaction { PersonDAO.findById(personId)?.asDataModel() }?.let { person ->
            createRecord(
                id = id,
                depth = depth,
                model = PersonModel,
                attrs = listOf(
                    person.customDisplayName,
                ),
            )
        }
    }

    private suspend fun searchPlan(id: UUID, depth: Int): Record? {
        val vid = id.let(::PlanId)
        return suspendedTransaction { PlanDAO.findById(vid)?.asDataModel() }?.let { plan ->
            createRecord(
                id = id,
                depth = depth,
                model = PlanModel,
                attrs = listOf(
                    plan.name,
                    plan.tier?.takeIf { it.name != plan.name },
                ),
            )
        }
    }

    private suspend fun searchPullRequestIngestionModel(id: UUID, depth: Int): Record? {
        val vid = id.let(::PullRequestIngestionId)
        return suspendedTransaction { PullRequestIngestionDAO.findById(vid)?.asDataModel() }?.let { prIngestion ->
            createRecord(
                id = id,
                depth = depth,
                model = PullRequestIngestionModel,
                attrs = listOf(
                    "Light=${prIngestion.lightweightIngestionStatus}",
                    "Bulk=${prIngestion.bulkIngestionStatus}",
                ),
            )
        }
    }

    private suspend fun searchRepo(id: UUID, depth: Int): Record? {
        val vid = id.let(::RepoId)
        return suspendedTransaction { RepoDAO.findById(vid)?.asDataModel() }?.let { repo ->
            createRecord(
                id = id,
                depth = depth,
                model = RepoModel,
                attrs = listOf(
                    repo.fullName,
                    repo.isUserSelected.takeIf { it }?.let { "Selected" },
                ),
            )
        }
    }

    private suspend fun searchScmTeam(id: UUID, depth: Int): Record? {
        val vid = id.let(::ScmTeamId)
        return suspendedTransaction { ScmTeamDAO.findById(vid)?.asDataModel() }?.let { scmTeam ->
            createRecord(
                id = id,
                depth = depth,
                model = ScmTeamModel,
                attrs = listOf(
                    scmTeam.displayName,
                    scmTeam.provider,
                    scmTeam.isDeleted.takeIf { it }?.let { "Deleted" },
                ),
            )
        }
    }

    private suspend fun searchSlackChannel(id: UUID, depth: Int): Record? {
        val vid = id.let(::SlackChannelId)
        return suspendedTransaction { SlackChannelDAO.findById(vid)?.asDataModel() }?.let { slackChannel ->
            createRecord(
                id = id,
                depth = depth,
                model = SlackChannelModel,
                attrs = listOf(
                    slackChannel.name,
                    slackChannel.isChannel.takeIf { it }?.let { "Channel" } ?: "DM",
                    slackChannel.isPrivate.takeIf { it }?.let { "Private" },
                    slackChannel.isArchived.takeIf { it }?.let { "Archived" },
                    slackChannel.isShared.takeIf { it }?.let { "Shared" },
                ),
            )
        }
    }

    private suspend fun searchSlackChannelIngestion(id: UUID, depth: Int): Record? {
        val vid = id.let(::SlackChannelIngestionId)
        return suspendedTransaction { SlackChannelIngestionDAO.findById(vid)?.asDataModel() }?.let { slackChannelIngestion ->
            createRecord(
                id = id,
                depth = depth,
                model = SlackChannelIngestionModel,
                attrs = listOf(
                    slackChannelIngestion.status,
                ),
            )
        }
    }

    private suspend fun searchSlackIngestion(id: UUID, depth: Int): Record? {
        val vid = id.let(::SlackIngestionId)
        return suspendedTransaction { SlackIngestionDAO.findById(vid)?.asDataModel() }?.let { slackIngestion ->
            createRecord(
                id = id,
                depth = depth,
                model = SlackIngestionModel,
                attrs = listOf(
                    slackIngestion.status,
                ),
            )
        }
    }

    private suspend fun searchSocialNetwork(id: UUID, depth: Int): Record? {
        val vid = id.let(::SocialNetworkId)
        return suspendedTransaction { SocialNetworkDAO.findById(vid)?.asDataModel() }?.let { socialNetwork ->
            createRecord(
                id = id,
                depth = depth,
                model = SocialNetworkModel,
                attrs = listOf(
                    socialNetwork.id,
                    socialNetwork.weight.let { String.format(Locale.US, "weight=%.2f", it) },
                ),
            )
        }
    }

    private suspend fun searchThread(id: UUID, depth: Int): Record? {
        val vid = id.let(::ThreadId)
        return suspendedTransaction { ThreadDAO.findById(vid)?.asDataModel() }?.let { thread ->
            createRecord(
                id = id,
                depth = depth,
                model = ThreadModel,
                attrs = listOf(
                    thread.title,
                ),
            )
        }
    }

    private suspend fun searchWebIngestionSite(id: UUID, depth: Int): Record? {
        val vid = id.let(::WebIngestionSiteId)
        return suspendedTransaction { WebIngestionSiteDAO.findById(vid)?.asDataModel() }?.let { webIngestionSite ->
            createRecord(
                id = id,
                depth = depth,
                model = WebIngestionSiteModel,
                attrs = listOf(
                    webIngestionSite.url,
                    webIngestionSite.disabled.takeIf { it }?.let { "Disabled" },
                ),
            )
        }
    }

    private suspend fun RecordGraph.discoverForeignKeyEdgesFrom(
        node: Record,
        depth: Int,
    ) {
        searching.forEach { otherModel ->
            node.model.getForeignKeyColumns<IdAny>(otherModel)
                .nullIfEmpty()
                ?.let { modelColumns ->
                    buildMap {
                        suspendedTransaction {
                            queryCount += 1
                            node.model
                                .select(modelColumns)
                                .withDistinct(true)
                                .where { node.model.id.castedEquals(node.id) }
                                .forEach { row ->
                                    modelColumns.forEach { modelColumn ->
                                        @Suppress("UNCHECKED_CAST")
                                        val rowValue = row[modelColumn] as EntityID<UUID>?
                                        if (rowValue != null) {
                                            put(
                                                key = modelColumn,
                                                value = rowValue.value.let(::extractUUID),
                                            )
                                        }
                                    }
                                }
                        }
                    }
                }
                ?.forEach { (modelColumn, uuid) ->
                    val next = GraphSearch(
                        uuid = uuid,
                        depth = depth + 1,
                        type = otherModel,
                    ) { other ->
                        val label = RecordEdgeLabel(
                            label = modelColumn.edgeLabelTo(otherModel),
                        )
                        addEdge(node, other, label)
                    }
                    pending.add(next)
                }
        }
    }

    private fun Column<out EntityID<*>>.castedEquals(value: UUID): Op<Boolean> {
        val entityColumnType = columnType as EntityIDColumnType<*>
        if (this == entityColumnType.idColumn) {
            // column is primary key
            entityColumnType.idColumn.wrappedEquals(value)
        }
        // column is foreign key
        return when (entityColumnType.idColumn.columnType) {
            is UUIDColumnType -> {
                @Suppress("UNCHECKED_CAST")
                val column = this as? Column<EntityID<UUID>> ?: error("wrong")
                column eq value
            }

            is TypedUUIDColumnType<*> -> {
                @Suppress("UNCHECKED_CAST")
                val column = this as? Column<EntityID<ValueId>> ?: error("wrong")

                @Suppress("UNCHECKED_CAST")
                val entityIDColumnType = entityColumnType.idColumn.columnType as TypedUUIDColumnType<ValueId>
                column eq entityIDColumnType.wrapValueId(value)
            }

            else -> error("not supported: ${value::class.java.name}")
        }
    }

    private fun Column<*>.wrappedEquals(value: UUID): Op<Boolean> {
        return when (columnType) {
            is EntityIDColumnType<*> -> {
                (columnType as EntityIDColumnType<*>).idColumn.wrappedEquals(value)
            }

            is UUIDColumnType -> {
                @Suppress("UNCHECKED_CAST")
                val column = this as? Column<UUID> ?: error("wrong")
                column eq value
            }

            is TypedUUIDColumnType<*> -> {
                @Suppress("UNCHECKED_CAST")
                val column = this as? Column<ValueId> ?: error("wrong")
                column eq (columnType as TypedUUIDColumnType<ValueId>).wrapValueId(value)
            }

            else -> error("not supported: ${columnType::class.java.name}")
        }
    }

    private suspend fun RecordGraph.discoverForeignKeyEdgesTo(node: Record, depth: Int) {
        searching.forEach { otherModel ->
            otherModel.getForeignKeyColumns<IdAny>(node.model).forEach { otherColumn ->
                val uuids = suspendedTransaction {
                    queryCount += 1
                    otherModel
                        .select(otherModel.id)
                        .where { otherColumn.castedEquals(node.id) }
                        .map { it[otherModel.id].value }
                        .map(::extractUUID)
                }
                uuids.forEach { uuid ->
                    val next = GraphSearch(
                        uuid = uuid,
                        depth = depth + 1,
                        type = otherModel,
                    ) { other ->
                        val label = RecordEdgeLabel(
                            label = otherColumn.edgeLabelTo(node.model),
                        )
                        addEdge(other, node, label)
                    }
                    pending.add(next)
                }
            }
        }
    }

    private fun Column<out EntityID<*>>.edgeLabelTo(otherModel: Table): String? {
        return otherModel::class.java.simpleName
            .startsWith(name.lowercase(), ignoreCase = true)
            .takeIf { !it }
            ?.let { name }
    }

    private fun extractUUID(id: Any): UUID {
        return when (id) {
            is UUID -> id
            is ValueId -> ValueIdConverter.extract(id)
            else -> error("missing extractor for ${id::class.simpleName}")
        }
    }

    private fun TypedUUIDColumnType<ValueId>.wrapValueId(uuid: UUID): ValueId {
        return ValueIdConverter.of(type).factory(uuid)
    }
}

private const val SCHEME_NAME = "set312"
private const val SCHEME_COLORS = 12

private class RecordGraph(
    val maxNodes: Int,
    val maxDepth: Int,
    val rootType: String?,
    val exclude: String,
) : Graph<Record>() {
    val pending: Queue<GraphSearch> = PriorityQueue(compareBy { it.depth })
    val visited: MutableSet<UUID> = mutableSetOf()
    val labels = mutableListOf<Record>()
    var queryCount = 0

    val excluding: Set<IdTable<*>> = exclude
        .split(",")
        .filterNot { it.isBlank() }
        .map { it.trim().toRegex() }
        .flatMap { regex ->
            allServiceModels.filter { serviceModel ->
                regex.matchesAt(serviceModel.tableName, 0)
            }
        }
        .toSet()

    val searching: Set<IdTable<*>> = (allServiceModels - excluding)
        .toSet()
}

private class GraphSearch(
    val uuid: UUID,
    val type: IdTable<*>?,
    val depth: Int = 0,
    val onResult: (Record) -> Unit,
)

private val nodeColors = mutableMapOf<IdTable<*>, Int>()

private data class Record(
    val id: UUID,
    val depth: Int,
    val label: String,
    val model: IdTable<*>,
) : Node {

    val key get() = "${model.tableName}/$id"

    val color: Int
        get() = nodeColors.computeIfAbsent(model) {
            1 + allServiceModels.indexOf(model) % SCHEME_COLORS
        }

    override fun toString() = "Record(${model::class.simpleName}, $id)"
}

private data class RecordEdgeLabel(
    val label: String? = null,
    val style: String? = null,
)
