package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPathUtils.optionalId
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationsPage.renderEventsChecklist
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationsPage.renderPermissionsChecklist
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.EnterpriseAppConfig
import com.nextchaptersoftware.db.models.EnterpriseAppConfigId
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.stores.EnterpriseAppConfigStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.scm.Scm
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.config.GitHubCloudConfig
import com.nextchaptersoftware.scm.github.models.GitHubApp
import com.nextchaptersoftware.utils.Base64.base64Encode
import io.ktor.http.authority
import io.ktor.server.application.ApplicationCall
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlin.time.Duration.Companion.seconds
import kotlinx.coroutines.withTimeout
import kotlinx.html.FlowContent
import kotlinx.html.code
import kotlinx.html.h1
import kotlinx.html.h3
import kotlinx.html.p

object IntegrationPage {

    suspend fun RoutingContext.renderIntegrationPage(
        page: AdminPage,
        enterpriseAppConfigStore: EnterpriseAppConfigStore = Stores.enterpriseAppConfigStore,
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
    ) {
        val path = call.request.path()
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val scm = getScm(call, provider)

        val appConfig = when (scm) {
            is Scm.OnPremise -> enterpriseAppConfigStore.getById(scm.enterpriseId, scm.provider)
            Scm.GitHub -> null
            else -> error("${provider.displayName} is not supported")
        }

        @Suppress("ktlint:nextchaptersoftware:no-run-catching-expression-rule")
        val gitHubApp = runCatching {
            withTimeout(3.seconds) {
                scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
                    it.app()
                }
            }
        }.getOrNull()

        val actions = buildList {
            add(
                MenuItem(
                    href = "$path/deleteIntegration",
                    style = BootstrapStyle.Danger,
                    label = "Delete App",
                    description = """
                        Delete this Enterprise App Config from the DB.
                        This does not delete the app from the SCM provider side, so you should do that also if you want to cleanup fully.
                    """.trimIndent(),
                ),
            )
        }

        val related = buildList {
            if (provider in listOf(Provider.GitHub, Provider.GitHubEnterprise)) {
                add(
                    MenuItem(
                        href = "$path/installations",
                        label = "Installations",
                        description = "List all installations for this app.",
                    ),
                )
            }
        }

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity) }
            relatedMenu { renderRelatedMenu(related) }
            actionMenu { renderActionMenu(actions) }
            content {
                h1 { +page.label }

                gitHubApp?.also {
                    h3(classes = "mt-5") { +"${scm.displayName} App" }
                    p { +"This is the Application from the perspective of GitHub." }
                    renderApp(gitHubApp)
                }

                appConfig?.also {
                    h3(classes = "mt-5") { +"${scm.displayName} App Config" }
                    p { +"This is the Application from our database perspective." }
                    renderAppConfig(appConfig)
                }
            }
        }
    }

    private fun FlowContent.renderAppConfig(appConfig: EnterpriseAppConfig) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("ID", appConfig.id)
                property("Created", appConfig.createdAt)
                property("Modified", appConfig.modifiedAt)
                property("Provider Type", appConfig.provider.displayName)
                property("Host and Port", appConfig.hostAndPort)
                property("Authority", appConfig.authority)
                property("External App ID", appConfig.externalAppId, formatAsCode = true)
                property("OAuth Client ID", appConfig.oauthClientId, formatAsCode = true)
                property(
                    label = "OAuth Client Secret Encrypted",
                    nullableText = appConfig.oauthClientSecretEncrypted.base64Encode(),
                    description = "Raw ciphertext.",
                    formatAsCode = true,
                    hideDetails = true,
                )

                if (appConfig is EnterpriseAppConfig.GitHub) {
                    property("Slug") { code { +appConfig.slug } }
                    property("Owner", appConfig.owner)
                    property("Public URL", appConfig.appHtmlUrl)
                    property(
                        label = "Webhook Secret Encrypted",
                        nullableText = appConfig.webhookSecretEncrypted.base64Encode(),
                        description = "Raw ciphertext.",
                        formatAsCode = true,
                        hideDetails = true,
                    )
                    property(
                        label = "Private Key PEM Encrypted",
                        nullableText = appConfig.privateKeyPemEncrypted.base64Encode(),
                        description = "Raw ciphertext.",
                        formatAsCode = true,
                        hideDetails = true,
                    )
                }
            }
        }
    }

    private fun FlowContent.renderApp(scmApp: GitHubApp) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Created", scmApp.createdAt)
                property("Modified", scmApp.updatedAt)
                property("App External ID", scmApp.appId)
                property("App Name") { +scmApp.name }
                property("App Slug") { code { +scmApp.slug } }
                property("App Owner", scmApp.account.login)
                property("App Public URL", scmApp.appUrl)
                property("App Integration URL", scmApp.integrationUrl)
                property("App Events") {
                    renderEventsChecklist(scmApp.events)
                }
                property("App Permissions") {
                    renderPermissionsChecklist(scmApp.permissions)
                }
                property("App Is Valid", scmApp.isValid)
                property("App Description") { +scmApp.description }
            }
        }
    }

    suspend fun RoutingContext.deleteIntegration(enterpriseAppConfigStore: EnterpriseAppConfigStore) {
        call.parameters.optionalId("enterpriseId", ::EnterpriseAppConfigId)?.also { enterpriseId ->
            enterpriseAppConfigStore.deleteById(enterpriseId)
        }
        call.respondRedirect("$WEB_ROOT/integrations")
    }

    internal fun getScm(call: ApplicationCall, provider: Provider): Scm {
        val enterpriseId = call.parameters.optionalId("enterpriseId", ::EnterpriseAppConfigId)
        return Scm.fromProvider(provider, enterpriseId)
    }

    fun GitHubApp.asConfig(config: GitHubCloudConfig): EnterpriseAppConfig {
        return EnterpriseAppConfig.GitHub(
            id = EnterpriseAppConfigId.random(),
            createdAt = createdAt,
            modifiedAt = updatedAt,
            hostAndPort = appUrl.authority,
            externalAppId = appId.toString(),
            slug = slug,
            owner = account.login,
            appHtmlUrl = appUrl,
            oauthClientId = config.oauth.clientId,
            oauthClientSecretEncrypted = ByteArray(0),
            webhookSecretEncrypted = ByteArray(0),
            privateKeyPemEncrypted = ByteArray(0),
        )
    }
}
