package com.nextchaptersoftware.adminwebservice.adminweb.page

import com.nextchaptersoftware.adminwebservice.adminweb.AdminAlerts.renderAlerts
import com.nextchaptersoftware.adminwebservice.adminweb.AdminPage
import com.nextchaptersoftware.adminwebservice.adminweb.WEB_ROOT
import com.nextchaptersoftware.adminwebservice.adminweb.component.BootstrapStyle
import com.nextchaptersoftware.adminwebservice.adminweb.component.MenuItem
import com.nextchaptersoftware.adminwebservice.adminweb.component.asBadge
import com.nextchaptersoftware.adminwebservice.adminweb.component.property
import com.nextchaptersoftware.adminwebservice.adminweb.makeBreadcrumb
import com.nextchaptersoftware.adminwebservice.adminweb.models.AlertItem
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationsPage.renderEventsChecklist
import com.nextchaptersoftware.adminwebservice.adminweb.page.InstallationsPage.renderPermissionsChecklist
import com.nextchaptersoftware.adminwebservice.adminweb.renderActionMenu
import com.nextchaptersoftware.adminwebservice.adminweb.renderRelatedMenu
import com.nextchaptersoftware.adminwebservice.adminweb.template.ContentTemplate
import com.nextchaptersoftware.adminwebservice.adminweb.template.PropertyListTemplate
import com.nextchaptersoftware.adminwebservice.auth.AdminIdentity.getAdminIdentity
import com.nextchaptersoftware.db.models.Provider
import com.nextchaptersoftware.db.models.ScmTeam
import com.nextchaptersoftware.db.stores.ScmTeamStore
import com.nextchaptersoftware.db.stores.Stores
import com.nextchaptersoftware.scm.ScmAppApiFactory
import com.nextchaptersoftware.scm.github.models.GitHubInstallation
import io.ktor.server.html.insert
import io.ktor.server.html.respondHtmlTemplate
import io.ktor.server.request.path
import io.ktor.server.response.respondRedirect
import io.ktor.server.routing.RoutingContext
import kotlinx.html.FlowContent
import kotlinx.html.code
import kotlinx.html.h1

object InstallationPage {

    suspend fun RoutingContext.renderInstallationPage(
        page: AdminPage,
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
        scmTeamStore: ScmTeamStore = Stores.scmTeamStore,
    ) {
        val path = call.request.path()
        val breadcrumb = call.makeBreadcrumb()
        val adminIdentity = call.getAdminIdentity()
        val scm = IntegrationPage.getScm(call, provider)
        val installationId = requireNotNull(call.parameters["installationId"])

        val installation = scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
            it.installation(installationId)
        }

        val scmTeam = scmTeamStore.findByProviderExternalInstallationId(
            provider = scm.provider,
            providerEnterpriseId = scm.providerEnterpriseId,
            providerExternalInstallationId = installationId,
        )

        val actions = getActions(installation, path)
        val related = getRelated(scmTeam)
        val alerts = getAlerts(installation)

        call.respondHtmlTemplate(ContentTemplate(page, breadcrumb, adminIdentity)) {
            alerts { renderAlerts(adminIdentity, additionalAlerts = alerts) }
            relatedMenu { renderRelatedMenu(related) }
            actionMenu { renderActionMenu(actions, sort = false) }
            content {
                h1 { +page.label }
                renderInstallation(installation)
            }
        }
    }

    private fun getRelated(scmTeam: ScmTeam?): List<MenuItem> {
        return buildList {
            scmTeam?.also {
                add(
                    MenuItem(
                        href = "$WEB_ROOT/orgs/${scmTeam.orgId}",
                        label = "Org",
                        description = "Org for this installation.",
                    ),
                )
                add(
                    MenuItem(
                        href = "$WEB_ROOT/orgs/${scmTeam.orgId}/teams/${scmTeam.id}",
                        label = "SCM Team",
                        description = "SCM Team for this installation.",
                    ),
                )
            }
        }
    }

    private fun getAlerts(installation: GitHubInstallation): List<AlertItem> {
        return buildList {
            if (installation.isSuspended) {
                add(
                    AlertItem(
                        bootstrapStyle = BootstrapStyle.Warning,
                        title = "Suspended",
                        description = """
                        The GitHub Unblocked App is suspended for this account.
                        Access to the GitHub API or webhook events is blocked for this account.
                        """.trimIndent(),
                    ),
                )
            }
        }
    }

    private fun getActions(installation: GitHubInstallation, path: String): List<MenuItem> {
        return buildList {
            add(
                when (installation.isSuspended) {
                    true -> MenuItem(
                        href = "$path/installationUnsuspend",
                        label = "Unsuspend",
                        description = """
                        Removes a GitHub App installation suspension for this account.
                        """.trimIndent(),
                    )

                    false -> MenuItem(
                        href = "$path/installationSuspend",
                        label = "Suspend",
                        description = """
                        Suspends the GitHub Unblocked App for this account, which blocks the app from accessing the account's resources.
                        When a GitHub App is suspended, the app's access to the GitHub API or webhook events is blocked for that account.
                        """.trimIndent(),
                    )
                },
            )
            add(
                MenuItem(
                    href = "$path/installationUninstall",
                    label = "Uninstall",
                    style = BootstrapStyle.Danger,
                    description = """
                    Remotely uninstalls the GitHub Unblocked App for this account.
                    """.trimIndent(),
                ),
            )
        }
    }

    private fun FlowContent.renderInstallation(installation: GitHubInstallation) {
        insert(PropertyListTemplate()) {
            propertyList {
                property("Installation ID") { code { +installation.installationId.toString() } }
                property("App ID") { code { +installation.appId.toString() } }
                property("Account ID") { code { +installation.account.id.toString() } }
                property("Account Name") { code { +installation.account.login } }
                property("Account Type", installation.account.type.name)
                property("Created At", installation.createdAt)
                property("Updated At", installation.updatedAt)

                property("Permissions") { renderPermissionsChecklist(installation.permissions) }
                property("Events") { renderEventsChecklist(installation.events) }
                property("Repository Selection") { +installation.repositorySelection.name }
                property("Is Valid", installation.isValid)
                property("Supports CI", installation.supportsCI)
                property("Is Suspended") {
                    asBadge(bool = installation.isSuspended, trueStyle = BootstrapStyle.Warning, falseStyle = BootstrapStyle.Success)
                }
                installation.suspendedAt?.also { property("Suspended At", it) }
                installation.suspendedBy?.also { property("Suspended By", it.login) }
            }
        }
    }

    suspend fun RoutingContext.installationSuspend(
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
    ) {
        val scm = IntegrationPage.getScm(call, provider)
        val installationId = requireNotNull(call.parameters["installationId"])
        scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
            it.suspendInstallation(installationId)
        }
    }

    suspend fun RoutingContext.installationUnsuspend(
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
    ) {
        val scm = IntegrationPage.getScm(call, provider)
        val installationId = requireNotNull(call.parameters["installationId"])
        scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
            it.unsuspendInstallation(installationId)
        }
    }

    suspend fun RoutingContext.installationUninstall(
        scmAppApiFactory: ScmAppApiFactory,
        provider: Provider,
    ) {
        val scm = IntegrationPage.getScm(call, provider)
        val installationId = requireNotNull(call.parameters["installationId"])
        scmAppApiFactory.getApi(orgId = null, scm = scm).v3App().use {
            it.deleteInstallation(installationId)
        }

        call.respondRedirect(
            call.request.path()
                .replaceAfterLast("/", "").trimEnd('/')
                .replaceAfterLast("/", "").trimEnd('/'),
        )
    }
}
