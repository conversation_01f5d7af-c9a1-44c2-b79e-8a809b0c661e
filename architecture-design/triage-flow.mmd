flowchart TB

    Bitbucket[Bitbu<PERSON> Webhook]
    Bitbucket --> HooksApi

    Buildkite[Buildkite Webhook]
    Buildkite --> HooksApi

    CircleCi[CircleCi Webhook]
    CircleCi --> HooksApi

    GitHubWebhooks[GitHub Webhook]
    GitHubWebhooks --> HooksApi

    GitLabWebhooks[GitLab Webhook]
    GitLabWebhooks --> HooksApi

    subgraph queue: hooks_ci
        HooksApi --> CiWebhookHandler{{CiWebhookHandler}}

        CiWebhookHandler --> BitbucketWebhookHandler
        CiWebhookHandler --> BuildkiteWebhookHandler
        CiWebhookHandler --> CircleCiWebhookHandler
        CiWebhookHandler --> GitHubWebhookHandler
        CiWebhookHandler --> GitLabWebhookHandler
    end

    subgraph queue: ci_events
    %% builds + jobs
        BitbucketWebhookHandler --> BitbucketBuildEventHandler --> BuildIngestionService_onBuild
        GitLabWebhookHandler --> GitLabPipelineEventHandler --> BuildIngestionService_onBuild

    %% builds, jobs pairs:
        BuildkiteWebhookHandler --> BuildkiteBuildEventHandler --> BuildIngestionService_onBuild
        BuildkiteWebhookHandler --> BuildkiteJobEventHandler --> BuildIngestionService_onJob

        CircleCiWebhookHandler --> CircleCiJobEventHandler --> BuildIngestionService_onJob
        CircleCiWebhookHandler --> CircleCiWorkflowEventHandler --> BuildIngestionService_onBuild

        GitHubWebhookHandler --> GitHubCheckRunEventHandler --> BuildIngestionService_onJob
        GitHubWebhookHandler --> GitHubCheckSuiteEventHandler --> BuildIngestionService_onBuild

        subgraph  BuildIngestionService
            BuildIngestionService_onBuild[[ingestBuild]]
            BuildIngestionService_onJob[[ingestJob]]

            BuildIngestionService_onBuild --->|if Complete| BuildEventHandler
            BuildIngestionService_onJob --->|if Complete| BuildJobEventHandler
        end
    end

    subgraph queue: ci_triages
        TriageRequest[[TriageRequest]]
        TriagePublish[[TriagePublish]]
        TriageBilling[[TriageBilling]]

        TriageRequest --> BuildTriageService -->|if Visible| TriagePublish

        BuildEventHandler -->|if Success| TriagePublish
        BuildEventHandler -->|if Failure| TriageRequest

        BuildJobEventHandler -->|if Success| TriagePublish

        TriagePublish -->|on publish| TriageBilling
    end

    TriagePublish ---->|post comment| SCM
