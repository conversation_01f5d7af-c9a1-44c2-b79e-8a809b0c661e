package com.nextchaptersoftware.ktlint.rules

import com.pinterest.ktlint.test.KtLintAssertThat.Companion.assertThatRule
import com.pinterest.ktlint.test.LintViolation
import org.junit.jupiter.api.Test

class NoDirectHttpClientConstructorRuleTest {

    @Test
    fun `disallow direct HttpClient construction`() {
        assertThatRule { NoDirectHttpClientConstructorRule() }(
            """
                import io.ktor.client.HttpClient
                import io.ktor.client.engine.okhttp.OkHttp

                fun test(engine: OkHttp) {
                    val client = HttpClient(engine) {
                    }
                }
            """.trimIndent(),
        )
            .asFileWithPath("src/main/kotlin/com/example/client-scm/Test1.kt")
            .hasLintViolationsWithoutAutoCorrect(
                LintViolation(
                    line = 5,
                    col = 18,
                    detail = "Direct construction of HttpClient is forbidden. Use orgHttpClient(orgId, …) instead.",
                ),
            )
    }

    @Test
    fun `allow usage of orgHttpClient`() {
        assertThatRule { NoDirectHttpClientConstructorRule() }(
            """
                import com.nextchaptersoftware.ktor.client.org.orgHttpClient
                import com.nextchaptersoftware.db.models.OrgId
                import io.ktor.client.engine.okhttp.OkHttp

                fun test(orgId: OrgId, engine: OkHttp) {
                    val client = orgHttpClient(orgId, engine) {
                    }
                }
            """.trimIndent(),
        )
            .asFileWithPath("src/main/kotlin/com/example/client-scm/Test2.kt")
            .hasNoLintViolations()
    }
}
