package com.nextchaptersoftware.ktlint.rules

import com.nextchaptersoftware.ktlint.utils.PackageValidator
import org.jetbrains.kotlin.KtNodeTypes
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtReferenceExpression

class NoRunCatchingExpressionRule : CustomRule("no-run-catching-expression-rule") {

    private val packageValidator = PackageValidator(exclusionPatterns = listOf(Regex("^com.nextchaptersoftware.kotlinx")))

    @Suppress("NestedBlockDepth")
    override fun beforeVisitChildNodes(
        node: ASTNode,
        emit: Emitter,
    ) {
        if (!packageValidator.validate(node)) {
            return
        }

        if (node.elementType == KtNodeTypes.REFERENCE_EXPRESSION) {
            val referenceExpression = node.psi as? KtReferenceExpression
            referenceExpression?.let {
                val text = referenceExpression.text
                if (text == "runCatching") {
                    val errorMessage =
                        "Usage of runCatching() is forbidden because it suppresses CancellationException in coroutines. Use runSuspendCatching() instead."
                    emit(node.startOffset, errorMessage, false)
                }
            }
        }
    }
}
