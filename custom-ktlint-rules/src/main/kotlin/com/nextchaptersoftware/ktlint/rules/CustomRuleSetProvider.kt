package com.nextchaptersoftware.ktlint.rules

import com.pinterest.ktlint.cli.ruleset.core.api.RuleSetProviderV3
import com.pinterest.ktlint.rule.engine.core.api.RuleProvider
import com.pinterest.ktlint.rule.engine.core.api.RuleSetId

class CustomRuleSetProvider : RuleSetProviderV3(
    id = RuleSetId(ID),
) {
    override fun getRuleProviders(): Set<RuleProvider> {
        return setOf(
            RuleProvider { NoAssertionsAssertCallRule() },
            RuleProvider { NoCapitalizedIdRule() },
            RuleProvider { NoDaoAsArgumentRule() },
            RuleProvider { NoDaoOrModelInFlywayMigrationRule() },
            RuleProvider { NoDirectBouncyCastleProviderRule() },
            RuleProvider { NoDirectHttpClientConstructorRule() },
            RuleProvider { NoDoubleBangOperatorRule() },
            RuleProvider { NoExplicitHttpClientEngineRule() },
            RuleProvider { NoExposedInnerJoinRule() },
            RuleProvider { NoExpressionInLogStatementRule() },
            RuleProvider { NoNanoDateTimeRule() },
            RuleProvider { NoRunBlockingInTestRule() },
            RuleProvider { NoRunCatchingExpressionRule() },
            RuleProvider { NoSingleOrNullExpressionRule() },
            RuleProvider { NoStandardJsonSerializationOperatorsRule() },
            RuleProvider { NoStandardKotlinLoggingRule() },
            RuleProvider { NoSuspendedFlowRule() },
            RuleProvider { NoTestDelayExpressionRule() },
            RuleProvider { ProhibitAdminWebServiceMigrationRule() },
            RuleProvider { RestrictApiModelFunctionsRule() },
            RuleProvider { RestrictDefaultKtorExceptionsImportRule() },
            RuleProvider { ValidateTestPackageRule() },
        )
    }

    companion object {
        const val ID = "nextchaptersoftware"
    }
}
