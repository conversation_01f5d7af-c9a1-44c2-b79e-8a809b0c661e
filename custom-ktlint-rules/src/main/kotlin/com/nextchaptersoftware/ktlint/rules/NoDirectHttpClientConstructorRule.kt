package com.nextchaptersoftware.ktlint.rules

import com.nextchaptersoftware.ktlint.utils.FileValidator
import com.nextchaptersoftware.ktlint.utils.PackageValidator
import org.jetbrains.kotlin.KtNodeTypes
import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtCallExpression
import org.jetbrains.kotlin.psi.KtNameReferenceExpression

/**
 * Prohibits direct construction of [io.ktor.client.HttpClient].
 * Callers must use `orgHttpClient(orgId, …)` so that the mandatory
 * "X-Org-Id" header and guard-rails are always applied.
 */
class NoDirectHttpClientConstructorRule :
    CustomRule("no-direct-httpclient-constructor-rule") {

    private val fileValidator = FileValidator(
        inclusionPatterns = listOf(
            Regex("client-scm"),
        ),
        exclusionPatterns = listOf(
            Regex("src/test"),
        ),
    )

    // Skip the module that actually defines orgHttpClient to avoid self-violations.
    private val packageValidator = PackageValidator(
        exclusionPatterns = listOf(Regex("^com\\.nextchaptersoftware\\.ktor\\.client\\.org")),
    )

    @Suppress("ReturnCount")
    override fun beforeVisitChildNodes(
        node: ASTNode,
        emit: Emitter,
    ) {
        if (!packageValidator.validate(node)) return
        if (!fileValidator.validate(node)) return

        if (node.elementType == KtNodeTypes.CALL_EXPRESSION) {
            val callExpression = node.psi as? KtCallExpression ?: return
            val callee = callExpression.calleeExpression as? KtNameReferenceExpression ?: return

            if (callee.getReferencedName() == "HttpClient") {
                val error =
                    "Direct construction of HttpClient is forbidden. " +
                            "Use orgHttpClient(orgId, …) instead."
                emit(node.startOffset, error, false)
            }
        }
    }
}
