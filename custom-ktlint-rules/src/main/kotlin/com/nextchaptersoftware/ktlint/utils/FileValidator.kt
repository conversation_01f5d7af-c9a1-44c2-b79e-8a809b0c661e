package com.nextchaptersoftware.ktlint.utils

import org.jetbrains.kotlin.com.intellij.lang.ASTNode
import org.jetbrains.kotlin.psi.KtFile

class FileValidator(
    private val inclusionPatterns: List<Regex>? = null,
    private val exclusionPatterns: List<Regex>? = null,
) {
    private var resolved: Boolean? = null

    fun validate(node: ASTNode): Boolean {
        val filePath = (node.psi as? KtFile)?.virtualFilePath
        return if (filePath != null && filePath != "/File.kt") {
            validate(filePath)
        } else {
            resolved ?: true
        }
    }

    fun validate(filePath: String): Boolean {
        resolved?.let {
            return@validate it
        }

        return (
            inclusionPatterns?.any {
                it.containsMatchIn(filePath)
            } ?: true &&
            exclusionPatterns?.any {
                it.containsMatchIn(filePath)
            }?.not() ?: true
        )
            .also { resolved = it }
    }
}
