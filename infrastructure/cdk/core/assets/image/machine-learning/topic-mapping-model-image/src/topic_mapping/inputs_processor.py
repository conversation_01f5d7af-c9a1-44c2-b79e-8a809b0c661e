import logging
from typing import List

from text_utils.text_sanitizer import TextSanitizer

from topic_mapping.topic_mapping_constants import MIN_INPUT_LENGTH, MAX_INPUT_LENGTH, MAX_SUMMARY_INPUT_LENGTH


class InputsProcessor:
    __min_input_length: int
    __max_input_length: int
    __max_summary_input_length: int

    def __init__(
        self,
        min_input_length: int = MIN_INPUT_LENGTH,
        max_input_length: int = MAX_INPUT_LENGTH,
        max_summary_input_length: int = MAX_SUMMARY_INPUT_LENGTH,
    ):
        self.__min_input_length = min_input_length
        self.__max_input_length = max_input_length
        self.__max_summary_input_length = max_summary_input_length

    def process_inputs(self, inputs: List[str]) -> List[str]:
        documents = []

        for i in inputs:
            if len(i.strip()) > self.__min_input_length:
                sanitized_document = TextSanitizer.remove_usernames(
                    TextSanitizer.remove_url(TextSanitizer.unmark(TextSanitizer.sanitize(i)))
                )
                if len(sanitized_document) < self.__max_input_length:
                    documents.append(sanitized_document)
                else:
                    documents.append(sanitized_document[: self.__max_input_length])

        return documents
