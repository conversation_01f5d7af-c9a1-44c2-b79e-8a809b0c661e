import * as s3 from 'aws-cdk-lib/aws-s3';
import { S3BucketCorsRule } from '../s3/config';

export class S3Helpers {
    /**
     * Generates an S3 CorsRule based on the provided S3BucketCorsRule configuration.
     *
     * @param corsRule - The CORS rule configuration of type S3BucketCorsRule.
     * @returns A valid S3 CorsRule object, excluding undefined or missing values.
     */
    public static generateCorsRule(corsRule: S3BucketCorsRule): s3.CorsRule {
        const corsConfig: s3.CorsRule = {
            allowedOrigins: corsRule.corsAllowedOrigins || [],
            allowedMethods: (corsRule.corsAllowedMethods || []).map(
                (method) => s3.HttpMethods[method as keyof typeof s3.HttpMethods]
            ),
            allowedHeaders: corsRule.corsAllowedHeaders,
            exposedHeaders: corsRule.corsExposeHeaders,
            maxAge: corsRule.corsMaxAge,
        };

        return corsConfig;
    }
}
