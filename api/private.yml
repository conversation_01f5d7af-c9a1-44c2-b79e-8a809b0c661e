# prettier-ignore
openapi: 3.0.3
info:
  title: Unblocked API Reference
  description: |
    The Unblocked API is a private API that is not intended for public use or third-party developers.
    It is intended for use by Unblocked internal tools and services.
  contact:
    name: Unblocked
    url: https://getunblocked.com
    email: <EMAIL>
  version: v1
  x-audience: company-internal
  x-api-id: d0184f38-b98d-11e7-9c56-68f728c1ba70
  x-logo:
    url: https://avatars.githubusercontent.com/u/91906527?s=100
    altText: Unblocked Logo
servers:
  - url: https://getunblocked.com/api
    description: Production stack
  - url: https://dev.getunblocked.com/api
    description: Development stack
security:
  - bearerAuth: []
tags:
  - name: Assets
    description: |
      Assets such as videos, images, and binaries.
  - name: Auth
    description: |
      Signing up, signing in, and signing out.
  - name: AuthInstall
    description: |
      Installing a provider.
  - name: AtlassianForgeHooks
    description: |
      Inbound events from Atlassian Forge apps.
  - name: Asana
    description: |
      Asana integrations.
  - name: Bots
    description: |
      Management of bots.

      These APIs are intended initially for use in CI systems for managing external SCM bots for SCM systems that do not provide a
      bot native to our integration with the SCM.

      However, the API is designed so that it can be used for selecting bots from any provider (eg: Linear, GitLab, Confluence) for
      use in any future product feature (eg: Issue Automation, Code Review, Documentation generation).
  - name: CiHooks
    description: |
      Inbound events from CI hooks.
  - name: CI
    description: |
      CI integration configuration.
  - name: Coda
    description: |
      Coda integrations.
  - name: CodaHooks
    description: |
      Inbound events from Coda hooks.
  - name: Config
    description: |
      Client config.
  - name: Confluence
    description: |
      Confluence integrations.
  - name: Confluence Data Center
    description: |
      Confluence Data Center integrations.
  - name: Data Source Presets
    description: |
      Managing data source presets.
  - name: Demo
    description: |
      Demo related activities.
  - name: Email
    description: |
      Sending emails for various notification events.
  - name: Google Drive
    description: |
      Google Drive integrations.
  - name: Google Drive Workspace
    description: |
      Google Drive Workspace integrations.
  - name: Groups
    description: |
      User groups are collections of org members.

      Today groups are managed using a standardized SCIM protocol (System for Cross-domain Identity Management),
      from external identity providers like Okta or Entra ID.

      Each group can contain zero or more members.

      A member can be be part of zero or more groups.
  - name: Health
    description: |
      Validating the health of the service.
  - name: Insights
    description: |
      An `Insight` object represents every content type surfaced in Unblocked.
  - name: Invite
    description: |
      Invite events.
  - name: Invitees
    description: |
      Invitees are team members that are candidates for being invited to use Unblocked.
  - name: Jira
    description: |
      Jira integrations.
  - name: Jira Data Center
    description: |
      Jira Data Center integrations.
  - name: Keys
    description: |
      Manage public API keys.
  - name: Linear
    description: |
      Linear integrations.
  - name: LinearHooks
    description: |
      Inbound events from Linear hooks.
  - name: Logs
    description: |
      Outputting diagnostic logs.
  - name: Messages
    description: |
      A thread message object.
  - name: Metrics
    description: |
      Capturing user and product metrics.
  - name: Notion
    description: |
      Notion integrations.
  - name: NotionHooks
    description: |
      Inbound events from Notion hooks.
  - name: Persons
    description: |
      A `Person` object can have one or many identities.
      This is because a person can have a GitHub, Bitbucket, and GitLab account.
      A person can be a member of many teams.
  - name: PaymentInvites
    description: |
      Manage payment invites.
  - name: Plans
    description: |
      A `Plan` object models the capabilities and billing information.
  - name: Product Feedback
    description: |
      Feedback on the Unblocked Product
  - name: PullRequests
    description: |
      A `PullRequest` object models a pull request in an SCM repository.
  - name: Push
    description: |
      Push channel subscription and modified since queries.
  - name: Archived References
    description: |
      A `ArchivedReference` object is an obsoleted reference to an Unblocked question.
      Can be a thread, pull request, or an external integration document.
  - name: Repos
    description: |
      A `Repo` object models an SCM repository.
      Repos are a container for all thread and message content.

      Repos are identified where possible using the repo _root commit_.
      The root commit hash in a repo is identical for all forks,
      and it is invariant across repo renames and moves.
      Clients that have Git access can find the root commit SHA using `git rev-list --max-parents=0 HEAD`.
  - name: SCM Configurations
    description: |
      SCM configuration settings.
  - name: SCM Enterprise Integrations
    description: |
      Enterprise integrations model the entities used for logging into on-premise installations.
  - name: SCM Installations
    description: |
      SCM app installation.
  - name: ScmHooks
    description: |
      Inbound events from scm hooks.
  - name: Search
    description: |
      Search.
  - name: SlackHooks
    description: |
      Inbound events from slack hooks.
  - name: ML Routers
    description: |
      Inbound events that will be transformed and routed to a destination based on configured ML templates.
  - name: Slack
    description: |
      Slack integrations.
  - name: Stack Overflow for Teams
    description: |
      Stack Overflow for Teams integrations.
  - name: StripeHooks
    description: |
      Inbound events from Stripe hooks.
  - name: SourceMarks
    description: |
      SourceMarks.
  - name: Teams
    description: |
      A team object. Teams have many team members.
  - name: TeamMembers
    description: |
      This object represents a team member. Only team members can access (read and write to) team resources.

      A team member can become de-activated if they (i) leave, or (ii) are removed, or (iii) are suspended from a team.
      A team member is not active until they have an Unblocked account.
      In those cases it is important to note that team member objects are never deleted;
      instead they are marked as not a current member !(`isCurrentMember`).
      This allows the system to reliably render content generated by ex-team members.
  - name: Threads
    description: |
      A thread object is an abstraction that represents discussions and notes.
  - name: TranscriptionHooks
    description: |
      Inbound events from transcription hooks.
  - name: Unreads
    description: |
      A thread unread object.
      Use this to determine the threads that have been fully or partially unread for the currently authorized user.
      This object takes into account messages that have been added to or deleted from a thread,
      and messages that have been read or unread by the user.
  - name: Versions
    description: |
      Management of client upgrades and obsolescence.
  - name: Web Ingestion
    description: |
      Website ingestion integrations.
  - name: Single Sign On
    description: |
      The Single Sign-On APIs allow a team to configure and manage SAML-based identity providers.
      Teams can also choose to enforce SSO via the `enforceSignIn` field.
  - name: SAML Auth
    description: |
      APIs related to SAML authentication flow.
      These APIs are intentionally and necessarily unauthenticated.
  - name: Registered Domains
    description: |
      Use these APIs to register and validate domains.
  - name: Segments
    description: |
      Segmentation APIs allow you to query for analytics data segmented by various dimensions.
  - name: Single Stats
    description: |
      Single Stat APIs allow you to query for a single metric value.
  - name: Time Series
    description: |
      Time Series APIs allow you to query for time series data.
  - name: Diagram
    description: |
      Diagram API allows you to render diagrams.
  - name: MCP
    description: |
      Model Context Protocol operations and configurations.
paths:
  /diagrams/{encodedDiagram}:
    get:
      tags:
        - Diagram
      description: |
        Converts a markdown diagram into its visual representation as an image.
      operationId: renderDiagram
      parameters:
        - name: encodedDiagram
          description: Encoded diagram string.
          in: path
          required: true
          schema:
            type: string
        - name: sig
          description: SHA1 HMAC (Hash-based Message Authentication Code) signature of the encoded diagram, `encodedDiagram`.
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            image/png:
              schema:
                type: string
                format: byte
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /preauth:
    get:
      tags:
        - Auth
      description: native client preauth exchange
      operationId: preAuth
      responses:
        "200":
          description: Pre-auth token and secret
          content:
            application/json:
              schema:
                $ref: ./components/PreAuthToken.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /preauth/exchange:
    get:
      tags:
        - Auth
      description: token exchange endpoint for valid authentication result
      operationId: preAuthTokenExchange
      responses:
        "200":
          description: Out-of-band token exchange for valid authentication result
          content:
            application/json:
              schema:
                $ref: ./components/AuthToken.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - exchangeAuth: []
  /auth/{provider}/exchange:
    get:
      tags:
        - AuthInstall
      description: Never called by the client - browser will be pushed to this endpoint
      operationId: authExchange
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            $ref: ./components/Provider.yml
        - name: state
          in: query
          description: state created by client when authorizing
          required: true
          schema:
            type: string
        - name: code
          in: query
          description: OAuth token exchange code
          required: true
          schema:
            type: string
      responses:
        "302":
          description: Redirect browser to auth provider
        default:
          $ref: "#/components/responses/Default"
      security: []
  /auth/teams/{teamId}/exchange:
    get:
      tags:
        - Auth
      description: |
        Exchange OAuth code for a connecting identity ID.
      operationId: authConnectionExchange
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: state
          in: query
          description: state created by client when authorizing
          required: true
          schema:
            type: string
        - name: code
          in: query
          description: OAuth token exchange code
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/AuthConnection.yml"
        default:
          $ref: "#/components/responses/Default"
  /auth/saml/{teamId}/metadata:
    get:
      tags:
        - SAML Auth
      description: |
        Get the SAML metadata configured for the team.
        Responds with 404 if the team does not have SAML configured.
        This endpoint is necessarily unauthorized for the SAML flow.
      operationId: getSamlMetadata
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/xml:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /auth/saml/{teamId}/acs:
    post:
      tags:
        - SAML Auth
      description: |
        Consume the SAML assertion from the IdP.
        Always redirects to the web dashboard exchange URL.
        This endpoint is necessarily unauthorized for the SAML flow.
        The body of the request is a form post with the following parameters:
         - `RelayState`
         - `SAMLResponse`

        #### RelayState
        The `RelayState` parameter is an opaque value that the SP uses to maintain state between the request and the response.
        Typically, the SP will use the `RelayState` parameter to store the URL to which the user should be redirected
        after the SAML assertion is processed. However, we use it to store the `clientState` parameter.

        #### SAMLResponse
        The `SAMLResponse` parameter is the SAML assertion that the IdP sends to the SP.
        It is the base64 encoded SAML assertion.
      operationId: consumeSamlAssertion
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "302":
          description: Found
          headers:
            Location:
              description: Web dashboard exchange URL.
              required: true
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /login:
    delete:
      tags:
        - Auth
      description: Invalidate this refresh token chain (logout)
      operationId: logout
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security:
        - refreshAuth: []
  /login/{provider}:
    get:
      tags:
        - Auth
      description: Never called by the client - browser will be pushed to this endpoint
      operationId: login
      parameters:
        - name: provider
          in: path
          required: true
          schema:
            $ref: ./components/Provider.yml
        - name: clientSecret
          in: query
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: enterpriseProviderId
          in: query
          description:
            Required parameter if `provider` is an enterprise provider type.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: teamId
          in: query
          description:
            Optional. Server will use the team ID to scope the login to that team.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: ssoProviderId
          in: query
          description:
            Required parameter if `provider` is a single sign on provider type.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: ssoEmail
          in: query
          description: |
            Optional email address used as a hint to SSO providers.
            Only relevant for SSO providers.
          required: false
          schema:
            type: string
        - name: agentType
          in: query
          required: false
          schema:
            $ref: ./components/AgentType.yml
        - name: redirectUrl
          deprecated: true
          in: query
          description: |
            Deprecated. Use `overrideRedirectUrl` instead.
          required: false
          schema:
            type: string
        - $ref: "#/components/parameters/overrideRedirectUrl"
        - $ref: "#/components/parameters/clientState"
        - name: targetIdentityId
          in: query
          description: |
            When specified, the OAuth flow will succeed if and only if the user connects the target identity.
            Otherwise, if not specified, the user can authenticate with any identity.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: skipAccountLinking
          in: query
          description: |
            When specified, the OAuth flow will skip the account connection step.
            This means that a person will not be created, and the identity will not be linked to any person.
            This is useful when the OAuth flow is used for authentication only.
          required: false
          schema:
            type: boolean
      responses:
        "302":
          description: Redirect browser to auth provider
        default:
          $ref: "#/components/responses/Default"
      security: []
  /login/optionsV3:
    get:
      tags:
        - Auth
      description: Login Options
      operationId: loginOptionsV3
      parameters:
        - name: clientSecret
          in: query
          description: Client secret used to associate preauth token to nonce
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: agentType
          in: query
          description: Client hint to know what to render at the end of the auth flow
          required: false
          schema:
            $ref: ./components/AgentType.yml
        - name: redirectUrl
          in: query
          description: Override the OAuth URL. Useful for dev purposes
          required: false
          schema:
            type: string
        - name: manifestRedirectUrl
          in: query
          description: Override the Manifest URL. Useful for dev purposes
          required: false
          schema:
            type: string
        - $ref: "#/components/parameters/clientState"
        - name: enterpriseProviderIds
          in: query
          description: |
            Comma separated list of EnterpriseProvider IDs.
            When specified, the response will also include enterprise login providers.
          required: false
          schema:
            $ref: ./components/ApiResourceListIds.yml
        - name: ssoProviderIds
          in: query
          description: |
            Comma separated list of SSO Provider IDs.
            When specified, the response will also include SSO Providers.
          required: false
          schema:
            $ref: ./components/ApiResourceListIds.yml
        - name: teamId
          in: query
          description: |
            Optional team ID.
            When specified, the response will include only team-specific login providers.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: inviteId
          in: query
          description: |
            Optional invite ID.
            The invite can be a Slack invite, or a team invite generated from web or email notifications.
            When specified, the response will include only login providers relevant for the invite.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/LoginOptionsResponseV2.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /login/exchangeV2:
    get:
      tags:
        - Auth
      description: Exchange auth code
      operationId: exchangeAuthCodeV2
      parameters:
        - name: state
          in: query
          description: nonce created by client at /login step
          required: true
          schema:
            type: string
        - name: code
          in: query
          description: OAuth token exchange code
          required: true
          schema:
            type: string
        - $ref: "#/components/parameters/sessionIdentifier"
      responses:
        "200":
          description: Access token response
          content:
            application/json:
              schema:
                $ref: ./components/AuthToken.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /login/refresh:
    get:
      tags:
        - Auth
      description: Refresh access token
      operationId: refreshToken
      parameters:
        - $ref: "#/components/parameters/sessionIdentifier"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: Access token response
          content:
            application/json:
              schema:
                $ref: ./components/AuthToken.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - refreshAuth: []
  /auth/scopedAccess:
    post:
      tags:
        - Auth
      description: |
        Provides long-lived access to a scoped resource.
        Scoped token only allow access to the exact resource defined in scoped resource.

        The server has an allow-list of permitted scoped resources.
        Attempts to get a scoped token for any other resource will fail.
      operationId: getScopedAccess
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ScopedResource.yml
        required: true
      responses:
        "200":
          description: Scoped access token response.
          content:
            application/json:
              schema:
                $ref: ./components/ScopedToken.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - refreshAuth: []
  /enterpriseProviders:
    post:
      tags:
        - SCM Enterprise Integrations
      description: |
        Look up enterprise providers.
        It liberally accepts any SCM input URL.
        It looks up registered enterprise providers.
        Otherwise, it tests endpoints to determine if they exist, are reachable, and if they match known provider types.

        ### Flow
        The Enterprise Provider flow can be invoked from the sign-in page in each of the clients.
        - VSCode — pass Git repo URLs derived from the IDE workspace.
        - Web — pass a user specified hostname and port.
        - Chrome/Safari web extensions — pass the current web page URL.

        ### Results
        - If `registeredProvider` exists in the response,
          then pass the EnterpriseProvider ID to `loginOptionsV2`.
        - Otherwise, if `unregisteredProvider` exists in the response,
          then use its manifest parameters to invoke the manifest flow.
        - Otherwise, if `status` is not `ok` render those errors.
        - Otherwise, no enterprise provider could be found for the input url.
      operationId: findEnterpriseProviders
      parameters:
        - name: clientSecret
          in: query
          description: Client secret used to associate preauth token to nonce
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: agentType
          in: query
          description: Client hint to know what to render at the end of the auth flow
          required: false
          schema:
            $ref: ./components/AgentType.yml
        - name: redirectUrl
          in: query
          description: Override the OAuth URL. Useful for DEV environment purposes.
          required: false
          schema:
            type: string
        - name: manifestRedirectUrl
          in: query
          description: Override the Manifest URL. Useful for DEV environment purposes.
          required: false
          schema:
            type: string
        - $ref: "#/components/parameters/clientState"
        - name: provider
          in: query
          description: |
            Optional provider filter. Only return enterprise providers matching this provider type.
          required: false
          schema:
            $ref: ./components/Provider.yml
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/EnterpriseProvidersUrls.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/EnterpriseProviderResult.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/{enterpriseProviderId}:
    get:
      tags:
        - SCM Enterprise Integrations
      description: |
        Get an Enterprise Integration
      operationId: getEnterpriseProvider
      parameters:
        - $ref: "#/components/parameters/enterpriseProviderId"
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/overrideRedirectUrl"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProvider.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/github:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges GitHub `code` and `state` for a new GitHub Enterprise provider.
      operationId: createGitHubEnterpriseProvider
      parameters:
        - name: code
          in: query
          description: |
            A temporary code used to retrieve the enterprise provider app configuration.
          required: true
          schema:
            type: string
        - name: state
          in: query
          description: |
            An unguessable random string. It is used to protect against cross-site request forgery attacks.
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProvider.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/gitlab:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges GitLab application configuration for a new GitLab Self-Managed provider.
      operationId: createGitLabEnterpriseProvider
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/GitLabSelfHostedApplication.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProviderOutcome.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/bitbucket:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges Bitbucket application configuration for a new Bitbucket Data Center provider.
      operationId: createBitbucketDataCenterEnterpriseProvider
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/BitbucketDataCenterApplication.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProviderOutcome.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/teams/{teamId}:
    get:
      tags:
        - SCM Enterprise Integrations
      description: |
        List enterprise providers for an org.
        If `provider` is specified, then only return enterprise providers matching this provider type.
      operationId: listOrgEnterpriseProviders
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/overrideRedirectUrl"
        - name: provider
          in: query
          description: |
            Only return enterprise providers matching this provider type.
          required: true
          schema:
            $ref: ./components/Provider.yml
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/EnterpriseProvider.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /enterpriseProviders/teams/{teamId}/github:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges GitHub `code` and `state` for a new GitHub Enterprise provider.
      operationId: createOrgGitHubEnterpriseProvider
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: code
          in: query
          description: |
            A temporary code used to retrieve the enterprise provider app configuration.
          required: true
          schema:
            type: string
        - name: state
          in: query
          description: |
            An unguessable random string. It is used to protect against cross-site request forgery attacks.
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProvider.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/teams/{teamId}/gitlab:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges GitLab application configuration for a new GitLab Self-Managed provider.
      operationId: createOrgGitLabEnterpriseProvider
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/GitLabSelfHostedApplication.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProviderOutcome.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /enterpriseProviders/teams/{teamId}/bitbucket:
    put:
      tags:
        - SCM Enterprise Integrations
      description: |
        Exchanges Bitbucket application configuration for a new Bitbucket Data Center provider.
      operationId: createOrgBitbucketDataCenterEnterpriseProvider
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/BitbucketDataCenterApplication.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EnterpriseProviderOutcome.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /installationsV2:
    post:
      tags:
        - SCM Installations
      description: Get SCM Installations
      operationId: findInstallationsAndRepos
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/QueryInstallationRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/InstallationsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /installationsV3:
    get:
      tags:
        - SCM Installations
      description: |
        List SCM installations available to the currently authorized user.

        Returns installations matching the specified optional `provider` query parameter.
        If the `provider` argument is not specified, then it infers the provider from the currently logged-in identity.
      operationId: listInstallations
      parameters:
        - name: provider
          in: query
          required: false
          schema:
            $ref: ./components/Provider.yml
        - name: enterpriseProviderId
          in: query
          description:
            Required parameter if `provider` is an enterprise provider
            type.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ListInstallationsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /scmAccounts:
    get:
      tags:
        - SCM Installations
      description: |
        List SCM accounts available for installation.

        When a `connectingIdentity` is specified then it is used to list SCM accounts;
        otherwise the the currently authorized SCM login identity is used to list SCM accounts.
      operationId: listScmAccounts
      parameters:
        - $ref: "#/components/parameters/connectingIdentity"
        - name: provider
          in: query
          required: true
          schema:
            $ref: ./components/Provider.yml
        - name: enterpriseProviderId
          in: query
          description: Required parameter if `provider` is an enterprise provider type.
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: externalInstallationId
          in: query
          description: |
            When the API is given this external installation ID, it will ensure that the SCM account associated with it is returned.
            This will cause the API to block until the SCM account is available, which will introduce noticeable latency.
          required: false
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "./components/ScmAccount.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /scmInstallations:
    put:
      tags:
        - SCM Installations
      description: |
        Creates a new SCM installation if necessary from an external SCM installation ID.

        If the installation already exists, then it will be returned.

        This API is only relevant for GitHub App installations.
      operationId: createScmInstallation
      parameters:
        - name: providerExternalInstallationId
          in: query
          required: true
          schema:
            type: string
            description: |
              _External_ SCM installation ID.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/InstallationV2.yml
        default:
          $ref: "#/components/responses/Default"
  /scmInstallations/{fullyQualifiedScmInstallationKey}/connect:
    post:
      tags:
        - SCM Installations
      description: |
        Connect to an installation if not already connected.

        If `installRepositoriesIds` is non-null, then only the specified repos will be installed,
        and existing repos that are not specified will be uninstalled.
        This behavior is different from the **patchInstallation** operation.

        If `installRepositoriesIds` is null , then repositories will not be modified.
      operationId: connectScmInstallation
      parameters:
        - $ref: "#/components/parameters/fullyQualifiedScmInstallationKey"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/InstallationConnectRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/InstallationV2.yml
        default:
          $ref: "#/components/responses/Default"
  /installationsV3/{fullyQualifiedScmInstallationKey}/update:
    patch:
      tags:
        - SCM Installations
      description: |
        Connect to an installation if not already connected.

        Ensure that the specified repos are installed.
        Existing repos that are not specified will remain installed.
        This behavior is different from the **connectInstallationV3** operation.
      operationId: patchInstallation
      parameters:
        - $ref: "#/components/parameters/fullyQualifiedScmInstallationKey"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/InstallationConnectRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/InstallationV2.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/scms:
    put:
      tags:
        - SCM Installations
      description: |
        Idempotently creates a new SCM Instance if necessary from SCM Account information.
      operationId: createOrgScm
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/connectingIdentity"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/ScmAccountRequest.yml"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/ScmInstance.yml"
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/integrationsV2:
    get:
      tags:
        - Integrations
      description: List all available integrations for this team.
      operationId: listIntegrationsV2
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataIntegrationRedirectUrl"
        - $ref: "#/components/parameters/overrideRedirectUrl"
        - $ref: "#/components/parameters/clientState"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Integration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []

  /teams/{teamId}/installations/{installationId}:
    delete:
      tags:
        - Installations
      description: Remove an installation.
      operationId: uninstallIntegration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/installations/{installationId}/bots:
    get:
      tags:
        - Bots
      description: |
        Find installation bot candidates by smart partial name match.

        When match is undefined, null, empty, or blank, then returns bot candidates alphabetically.

        Default limit is 5.
      operationId: findBots
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/limit"
        - name: match
          in: query
          description: |
            Smart partial name match. Each token is partially matched case insensitively against the bot username and display name.
          required: false
          schema:
            type: string
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/overrideRedirectUrl"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "./components/Bot.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []

  /teams/{teamId}/installations/{installationId}/bot:
    get:
      tags:
        - Bots
      description: |
        Get the currently selected bot for this installation.

        Returns 404 if a bot has not been selected yet for this installation.
      operationId: getBot
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/overrideRedirectUrl"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/Bot.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Bots
      description: |
        Select a bot to be used when interacting publicly with this installation.
      operationId: selectBot
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/BotSelectionRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Bots
      description: |
        Deselect a bot.

        Unblocked will no longer be able interact publicly with this installation.
      operationId: deselectBot
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/installations/{installationId}/bot/{botIdentityId}/oauthUrl:
    get:
      tags:
        - Bots
      description: |
        Get the OAuth URL for the bot to connect to the target identity.
      operationId: getBotOauthUrl
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/botIdentityId"
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/overrideRedirectUrl"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/BotOAuthUrlResponse.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []

  /teams/{teamId}/installationsV3:
    get:
      deprecated: true
      tags:
        - Installations
      description: |
        **Deprecated** Use `listIntegrationInstallationsV4` instead.
      operationId: listIntegrationInstallationsV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: provider
          in: query
          description:
            Optional provider filter. Only return installations matching this provider type.
          required: false
          schema:
            $ref: ./components/Provider.yml
        - $ref: "#/components/parameters/overrideRedirectUrl"
        - $ref: "#/components/parameters/dataIntegrationRedirectUrl"
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/IfModifiedSince"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/IntegrationInstallationV3.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installationsV3/linkedAccounts:
    put:
      tags:
        - Installations
      description: |
        User has completed linking accounts.
      operationId: completedLinkedAccounts
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installationsV4:
    get:
      tags:
        - Installations
      description: |
        List the installations for this team.
      operationId: listIntegrationInstallationsV4
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: provider
          in: query
          description:
            Optional provider filter. Only return installations matching this provider type.
          required: false
          schema:
            $ref: ./components/Provider.yml
        - $ref: "#/components/parameters/overrideRedirectUrl"
        - $ref: "#/components/parameters/dataIntegrationRedirectUrl"
        - $ref: "#/components/parameters/clientState"
        - $ref: "#/components/parameters/IfModifiedSince"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: ./components/IntegrationInstallationV4.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installationsV2/{installationId}/dismiss:
    put:
      tags:
        - Installations
      description: Dismisses the connection prompt for this integration
      operationId: dismissIntegrationConnection
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
  /teams/{teamId}/sampleQuestions:
    get:
      tags:
        - Search
      description: |
        Get sample questions to demo semantic search
      operationId: getSampleQuestions
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SampleQuestionsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/sampleQuestion/{sampleQuestionId}:
    get:
      tags:
        - Search
      description: Answer a sample question
      operationId: getSampleQuestionAnswer
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: sampleQuestionId
          in: path
          description: Single use exchange code for answering a sample question
          required: true
          schema:
            type: string
            format: uuid
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SampleQuestionAnswerResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/insights/search:
    post:
      tags:
        - Insights
      description: |
        Search insights.

        This operation supports pagination using `limit` and `cursor` parameters.
      operationId: searchInsights
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchInsightsRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SearchInsightsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/search/semanticSearchFile:
    post:
      tags:
        - Search
      description: |
        Semantically search for references for a file.

        This operation supports pagination using `limit` parameters.
      operationId: semanticSearchFile
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SemanticSearchFileRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SemanticSearchFileResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/archivedReferences:
    get:
      tags:
        - Archived References
      description: List archived references for a team.
      operationId: listArchivedReferences
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ArchivedReference.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Archived References
      description: Archive an existing reference for a team.
      operationId: archiveReference
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ArchiveReferenceBody.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/archivedReferences/{archivedReferenceId}:
    delete:
      tags:
        - Archived References
      description: Restore an archived reference for a team.
      operationId: restoreReference
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/archivedReferenceId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/insights/countsV2:
    post:
      tags:
        - Insights
      description: |
        Get counts for every insight type by provider for the given filter queries.
      operationId: getInsightCountsV2
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/InsightCountsRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ProviderInsightCount.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threads/mine:
    get:
      tags:
        - Threads
      description: |
        Get threads, but only where the current user is a thread participant.
        Threads are ordered by unread first, then the time of the most recent message in each thread.
        Archived and deleted threads are not returned.

        This operation supports pagination using `limit` and `cursor` parameters.
        You cannot use pagination parameters with `X-Unblocked-If-Modified-Since` header.
      operationId: getThreadsForMe
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoIds"
        - $ref: "#/components/parameters/IfModifiedSince"
        - $ref: "#/components/parameters/provider"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threads:
    post:
      tags:
        - Threads
      description: |
        Fetch a collection of threads by thread ID.
        Consider using this operation for bulk lookups instead of repeatedly calling `getThread`.
      operationId: fetchThreads
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ./components/ApiResourceId.yml
        required: true
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threads/archived:
    get:
      tags:
        - Threads
      description: |
        Get archived threads in the team.
        Threads are ordered by most recently archived.

        This operation supports pagination using `limit` and `cursor` parameters.
      operationId: getThreadsArchived
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoIds"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threads/search:
    get:
      tags:
        - Threads
      description: Search threads
      operationId: searchThreads
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoIds"
        - $ref: "#/components/parameters/productAgent"
        - name: q
          in: query
          required: true
          schema:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threadsV3/{threadId}:
    post:
      tags:
        - Threads
      description: Create thread
      operationId: createThreadV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateThreadRequestV3.yml
        required: true
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: ./components/Thread.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}:
    get:
      tags:
        - Threads
      description: Get thread by id
      operationId: getThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
        - $ref: "#/components/parameters/IfModifiedSince"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Threads
      description: Update thread
      operationId: updateThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateThreadRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Thread.yml
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Threads
      description: Delete thread
      operationId: deleteThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/questions:
    get:
      tags:
        - Threads
      description: |
        Get questions asked by people in the team.

        Supports cursor-based keyset pagination to fetch previous and next pages using the `before` and `after` parameters.
      operationId: getQuestions
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
        - $ref: "#/components/parameters/teamMemberIds"
        - $ref: "#/components/parameters/feedbackTypeFilter"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ThreadInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/feedbackCounts:
    get:
      tags:
        - Threads
      description: |
        Get response feedback counts.
      operationId: getFeedbackCounts
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/teamMemberIds"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/FeedbackTypeFilterCount.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/threads/{threadId}/archive:
    put:
      tags:
        - Threads
      description: Archive thread
      operationId: archiveThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Thread.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}/restore:
    put:
      tags:
        - Threads
      description: Restore thread
      operationId: restoreThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Thread.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}/viewed:
    put:
      tags:
        - Metrics
      description: Capture thread view event
      operationId: viewThread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}/private:
    put:
      tags:
        - Threads
      description: Update privacy state of thread
      operationId: updateThreadPrivacy
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateThreadPrivacyRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/unreads:
    get:
      tags:
        - Unreads
      description: |
        Get the unread status for all threads.
      operationId: getUnreads
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/IfModifiedSince"
        - $ref: "#/components/parameters/repoIds"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Unread.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    delete:
      tags:
        - Unreads
      description: |
        Mark all threads as read for the calling team member
      operationId: clearUnreads
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}/unread:
    put:
      tags:
        - Unreads
      description: |
        Update the unread status of this thread.
        Use to set the thread as having been read up to a specified message.
        Also use to set the entire thread as unread.
      operationId: updateThreadUnread
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateThreadUnreadRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/messagesV3/{messageId}:
    put:
      tags:
        - Messages
      description: Update message
      operationId: updateMessageV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/messageId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateMessageRequestV3.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Message.yml
        default:
          $ref: "#/components/responses/Default"
    post:
      tags:
        - Messages
      description: Create message
      operationId: createMessageV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/messageId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateMessageRequestV3.yml
        required: true
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: ./components/Message.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/messages/{messageId}:
    delete:
      tags:
        - Messages
      description: Delete message
      operationId: deleteMessage
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/messageId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/messages/{messageId}/feedback:
    patch:
      tags:
        - Messages
      description: Update Message Feedback
      operationId: updateMessageFeedback
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/messageId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateMessageFeedbackRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /personV2:
    get:
      tags:
        - Persons
      description: Get currently authenticated person
      operationId: getPersonV2
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Person.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Persons
      description: |
        Update the person's profile information.
      operationId: patchPerson
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PersonUpdate.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Person.yml
        default:
          $ref: "#/components/responses/Default"
  /person/onboardingStatusV2:
    get:
      tags:
        - Persons
      description: Get onboarding status of currently authenticated person
      operationId: getOnboardingStatus
      deprecated: true
      parameters:
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/OnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /person/onboardingStatus:
    patch:
      tags:
        - Persons
      description: Update Person onboarding status
      operationId: updateOnboardingStatus
      parameters:
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/OnboardingStatusUpdate.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/OnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
  /person/onboardingStatusV3:
    get:
      tags:
        - Persons
      description: Get onboarding status of currently authenticated person
      operationId: getPersonOnboardingStatus
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PersonOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Persons
      description: Update Person onboarding status
      operationId: updatePersonOnboardingStatus
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PersonOnboardingStatusUpdate.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PersonOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/memberOnboardingStatus:
    get:
      tags:
        - TeamMembers
      description: Get member onboarding status for the currently authenticated user
      operationId: getMemberOnboardingStatus
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/MemberOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - TeamMembers
      description: Update onboarding status for the currently authenticated user
      operationId: updateMemberOnboardingStatus
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/MemberOnboardingStatusUpdate.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/MemberOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/teamOnboardingStatus:
    get:
      tags:
        - Teams
      description: Get team onboarding status
      operationId: getTeamOnboardingStatus
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/TeamOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Teams
      description: Update team status
      operationId: updateTeamOnboardingStatus
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/TeamOnboardingStatusUpdate.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/TeamOnboardingStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /person/emailPreferences:
    get:
      tags:
        - Persons
      description: Get email preferences for person
      operationId: getPersonEmailPreferences
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EmailPreferences.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Persons
      description: Update email preferences for person
      operationId: updatePersonEmailPreferences
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/EmailPreferences.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/EmailPreferences.yml
        default:
          $ref: "#/components/responses/Default"
  /person/preferences:
    get:
      tags:
        - Persons
      description: Get preferences for the currently authenticated person
      operationId: getPersonPreferences
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PersonPreferences.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Persons
      description: Update preferences for the currently authenticated person
      operationId: updatePersonPreferences
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PersonPreferences.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /person/emails:
    get:
      tags:
        - Persons
      description: |
        Get all emails for the currently authenticated person.
        The emails are derived from all the accounts the person has connected to Unblocked.
        This endpoint is only available to the currently authenticated person.
      operationId: getPersonEmails
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/Emails.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams:
    get:
      tags:
        - Teams
      description: Get all teams
      operationId: getTeams
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Team.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}:
    patch:
      tags:
        - Teams
      description: Configure the properties for a team.
      operationId: patchTeam
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchTeamRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Teams
      description: |
        Deletes a team, including all of its content.
        If this is a GitHub backed team, then the GitHub App installation will be remotely uninstalled.
      operationId: deleteTeam
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/status:
    get:
      tags:
        - Teams
      description: |
        Get pending status for a team.

        Teams can either be pending or not (see `Team.isPending`).
        This endpoint returns the subtype of the pending status.
      operationId: getTeamStatus
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/TeamStatus.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/teamStats:
    get:
      tags:
        - Teams
      description: Get a team's statistics
      operationId: getTeamStats
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/TeamStats.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/settings:
    get:
      tags:
        - Config
      description: Get the feature settings for a team.
      operationId: getFeatureSettings
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/FeatureSettings.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Config
      description: Configure the feature settings for a team.
      operationId: patchFeatureSettings
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/FeatureSettings.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/dsac:
    get:
      tags:
        - Config
      description: Get the DSAC settings for a team.
      operationId: getDsacSettings
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/DsacSettings.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Config
      description: Configure the DSAC settings for a team.
      operationId: updateDsacSettings
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/DsacSettings.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/dsac/{installationId}:
    get:
      tags:
        - Config
      description: Get the DSAC setting for an installation.
      operationId: getDsacInstallationSettings
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: boolean
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Config
      description: Configure the DSAC setting for an installation.
      operationId: updateDsacInstallationSetting
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              type: boolean
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/publicKey:
    get:
      tags:
        - Config
      description: |
        Get the team-specific asymmetric public key used for client encryption.
        Use this approach to securely send sensitive secrets from the client to the service.
      operationId: getPublicKey
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []

  /teams/{teamId}/groups:
    get:
      tags:
        - Groups
      operationId: listGroups
      description: |
        List all groups.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Group.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/groups/{groupId}:
    get:
      tags:
        - Groups
      operationId: getGroup
      description: |
        Get a group.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/groupId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Group.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Groups
      operationId: patchGroup
      description: |
        Modify specified properties on a group. Only specified properties are updated.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/groupId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/GroupMutableProperties.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/member:
    patch:
      tags:
        - TeamMembers
      description: |
        **Deprecated:** Use `updateMemberOnboardingStatus` instead.
      operationId: updateTeamMemberInfo
      deprecated: true
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/TeamMemberInfoUpdateRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/memberPreferences:
    get:
      tags:
        - TeamMembers
      description: Update team member info
      operationId: getMemberPreferences
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/MemberPreferencesResponse.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - ciBearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - TeamMembers
      description: Update team member info
      operationId: patchMemberPreferences
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/MemberPreferencesRequest.yml"
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - ciBearerAuth: []
  /teams/{teamId}/members/bot:
    get:
      tags:
        - TeamMembers
      description: |
        Get the bot member ID.
      operationId: getBotMember
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ApiResourceId.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/membersV4:
    get:
      tags:
        - TeamMembers
      description:
        Get non-bot team members that are both current and primary. Returns
        a list of team member IDs.
      operationId: listTeamMembers
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - name: search
          in: query
          description: |
            Search term to filter members. Returns all members if not provided.
          required: false
          schema:
            type: string
        - name: sortOrder
          in: query
          description: |
            Sort order. Defaults to lastActiveAt if not provided.
          required: false
          schema:
            $ref: ./components/TeamMemberSortOrder.yml
        - name: sortDirection
          in: query
          description: |
            Sort order direction. Defaults to descending if not provided.
          required: false
          schema:
            $ref: ./components/SortDirection.yml
        - name: includeBot
          in: query
          description: |
            When true, the response may include bots.
            When false, the response never includes bots.
          required: false
          schema:
            type: boolean
        - name: role
          deprecated: true
          in: query
          description: |
            Deprecated, use roles instead.
            Filter by role.
            When not specified, team members for all roles are included.
          required: false
          schema:
            $ref: ./components/TeamMemberRole.yml
        - name: roles
          in: query
          description: |
            Filter by roles.
            When not specified, team members for all roles are included.
          required: false
          schema:
            type: array
            items:
              $ref: ./components/TeamMemberRole.yml
        - name: groupIds
          in: query
          description: |
            Filter by a comma separated list of Group IDs.
            When this parameter is not specified or empty, team members for all groups are included.
          required: false
          schema:
            $ref: ./components/ApiResourceListIds.yml
        - name: hasAccount
          in: query
          description: |
            When true, response only includes users with Unblocked accounts.
            When false, response only includes users without Unblocked accounts.
            If not specified, response returns a non-discriminate list of team members.
          required: false
          schema:
            type: boolean
        - name: accountType
          in: query
          description: |
            Filter by sign-in provider.
            When not specified, team members for all providers are included.
          required: false
          schema:
            type: array
            items:
              $ref: ./components/Provider.yml
        - name: hasLicense
          in: query
          description: |
            When true, response only includes users with an Unblocked seat.
            When false, response only includes users without an Unblocked seat.
            If not specified, response returns a non-discriminate list of team members.
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ApiResourceId.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/members:
    post:
      tags:
        - TeamMembers
      description: |
        Fetch team members by IDs.

        **primaryMemberId** —
        If `isPrimary` is true, then the `primaryMemberId` field is `null`.
        If `isPrimary` is false, the `primaryMemberId` field may be populated with the `id` of the primary member.

        **inheritedGroup** —
        This response field represents the group from which the TeamMember inherits their role.
        If the TeamMember belongs to multiple groups, this field represents the group with the highest role.
        If roles are equal, it defaults to the group with the lowest alphabetical name.
        This field is null if the TeamMember is not part of any groups,
        or none of the groups the TeamMember belongs to have an assigned role.

        **role** —
        This is the effective role of the TeamMember, incorporating the role of the inherited group.
        Use this in most cases to determine the role of the TeamMember.

        **selectedRole** —
        The role that was explicitly assigned to the TeamMember, if any.
        It is null if the TeamMember's role is inherited from a group.
        Only use this field if you need to know the role that was explicitly assigned to the TeamMember.
      operationId: fetchTeamMembers
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/FetchTeamMembersRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/TeamMember.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/members/{teamMemberId}:
    patch:
      tags:
        - TeamMembers
      description: |
        Update properties of a team member object.
      operationId: updateTeamMember
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/teamMemberId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/TeamMemberMutableFields.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /channels/teams/{teamId}/modifiedSince:
    post:
      tags:
        - Push
      description: Get modifiedSince status for set of query paths
      operationId: getChannelsModifiedSince
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ChannelsModifiedSinceRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ChannelsModifiedSinceResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /__shallowcheck:
    get:
      tags:
        - Health
      description: |
        Shallow check.

        Tests if the service has completed startup,
        and is ready to receive traffic.
      operationId: getShallowCheck
      responses:
        "200":
          description: OK
        default:
          $ref: "#/components/responses/Default"
      security: []
    x-style-validator-ignored: true
  /__deepcheck:
    get:
      tags:
        - Health
      description: |
        Deep check.

        Tests if the service has completed startup,
        tests if it's dependencies (eg: DBs) are available,
        and is ready to receive traffic.
      operationId: getDeepCheck
      responses:
        "200":
          description: OK
        default:
          $ref: "#/components/responses/Default"
      security: []
    x-style-validator-ignored: true
  /logs:
    put:
      tags:
        - Logs
      description: Log output
      operationId: log
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/LogContext.yml
        required: true
      responses:
        "200":
          description: OK
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/repos:
    get:
      tags:
        - Repos
      description: |
        Get all repos.
      operationId: getRepos
      deprecated: true
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/IfModifiedSince"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Repo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/repos/{repoId}/sourcemarks:
    get:
      tags:
        - SourceMarks
      description: |
        Get all source marks for the specified repos.
        This endpoint is paginated. The items in the collection are in ordered by modified timestamp;
        so use the `X-Unblocked-Last-Modified` response header as the cursor for subsequent pages.
        This ordering is symmetric with the pusher ordering.
      operationId: getRepoSourceMarks
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoId"
        - $ref: "#/components/parameters/IfModifiedSince"
        - $ref: "#/components/parameters/limit"
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Mark.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/repos/{repoId}/sourcemarksByFile:
    post:
      tags:
        - SourceMarks
      description: |
        Find all SourceMarks that were at some point in time related to the files specified in `filePaths`.
        Specifically, returns sourcemarks where the original sourcepoint or subsequent sourcepoints match the specified `filePaths`.

        Note that the sourcemark returned may not exist in the file at the local revision;
        the local sourcemark engine is responsible for figuring out if the sourcemark exists in the file at the local revision.
      operationId: findRepoSourceMarksByFile
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoId"
        - $ref: "#/components/parameters/IfModifiedSince"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/FindSourceMarksByFileRequest.yml
        required: true
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Mark.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/repos/{repoId}/sourcemarksById:
    post:
      tags:
        - SourceMarks
      description: |
        Find all SourceMarks by IDs.
      operationId: findRepoSourceMarksById
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoId"
        - $ref: "#/components/parameters/IfModifiedSince"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/FindSourceMarksByIdRequest.yml
        required: true
      responses:
        "200":
          description: OK
          headers:
            X-Unblocked-Last-Modified:
              description:
                Pass to push APIs as `X-Unblocked-If-Modified-Since` header
                parameter.
              required: true
              schema:
                type: string
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/Mark.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /repo:
    post:
      tags:
        - Repos
      description: |
        Finds a `Repo` matching the `rootCommitSha` or `repoUrl` in that order.
        Searches over all orgs the caller is a member of.
      operationId: discoverRepo
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/DiscoverRepoRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/OrgRepo.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/pullRequests:
    post:
      tags:
        - PullRequests
      description: |
        Fetch a collection of pull requests by pull requests DB ID.
        Consider using this operation for bulk lookups instead of repeatedly calling `getPullRequest`.
      operationId: fetchPullRequests
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ./components/ApiResourceId.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/PullRequest.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/pullRequestsForCommits:
    post:
      tags:
        - PullRequests
      description: |
        List all closed or merged pull requests in the team for the provided commits in repo.
      operationId: getPullRequestsForCommits
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PullRequestsForCommitsRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/PullRequest.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/pullRequests/{pullRequestId}/restore:
    put:
      tags:
        - PullRequests
      description: Restore pull request
      operationId: restorePullRequest
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/pullRequestId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PullRequest.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/pullRequests/{pullRequestId}/info:
    get:
      tags:
        - PullRequests
      description: |
        Get a pull request info.
      operationId: getPullRequestInfo
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/pullRequestId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PullRequestInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/pullRequests/{pullRequestId}/blocks/{pullRequestBlockId}:
    delete:
      tags:
        - PullRequests
      description: Delete a top level comment on a pull request.
      operationId: deletePullRequestBlock
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/pullRequestId"
        - $ref: "#/components/parameters/pullRequestBlockId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/pullRequests/{pullRequestId}/viewed:
    put:
      tags:
        - Metrics
      description: Capture pull request view event
      operationId: viewPullRequest
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/pullRequestId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/viewContent:
    put:
      tags:
        - Metrics
      description: |
        Capture content view event.
        Dashboard clients should call this periodically when the IDE is foregrounded and any content list view is visible.
      operationId: viewContent
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/viewTeamManagement:
    put:
      tags:
        - Metrics
      description: |
        Capture team management view event.
      operationId: viewTeamManagement
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/viewIdeSidebar:
    put:
      tags:
        - Metrics
      description: |
        Capture IDE sidebar list view event.
        IDE clients should call this periodically when the IDE is foregrounded and the Sidebar list view is visible.
      operationId: viewIdeSidebar
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/openIdeSidebar:
    put:
      tags:
        - Metrics
      description: |
        Developer opened the IDE sidebar panel.
      operationId: openIdeSidebar
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/closeIdeSidebar:
    put:
      tags:
        - Metrics
      description: |
        Developer closed the IDE sidebar panel.
      operationId: closeIdeSidebar
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/viewIdeInsights:
    put:
      tags:
        - Metrics
      description: |
        Capture IDE insights panel view event.
        IDE clients should call this periodically when the IDE is foregrounded and the Insights Panel view is visible.
      operationId: viewIdeInsights
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/openIdeInsights:
    put:
      tags:
        - Metrics
      description: |
        Developer opened the IDE insights panel.
      operationId: openIdeInsights
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/closeIdeInsights:
    put:
      tags:
        - Metrics
      description: |
        Developer closed the IDE insights panel.
      operationId: closeIdeInsights
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/visitedProcessingComplete:
    put:
      tags:
        - Metrics
      description: |
        Developer accessed Unblocked through processing complete.
      operationId: visitedProcessingComplete
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/openAnswerPreferences:
    put:
      tags:
        - Metrics
      description: |
        Developer opened the answer preferences tooltip.
      operationId: openAnswerPreferences
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/ide/{ideActivityEvent}:
    put:
      tags:
        - Metrics
      description: |
        Developer triggered an IDE Activity metric.
      operationId: triggerIdeActivityEvent
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/ideActivityEvent"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/repos/{repoId}/sourcepoints:
    put:
      tags:
        - SourceMarks
      description: |
        Bulk upload new sourcepoints or filepoints for existing sourcemarks.
        Exactly one of the `sourcePoints` or `filePoints` arrays is required.
      operationId: putRepoSourcePoints
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoId"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ./components/NewSourcePoints.yml
        required: true
      responses:
        "202":
          description: Accepted
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/invites:
    post:
      tags:
        - Email
      description:
        Send Unblocked invitation emails tailored for a team to members
        who have not joined Unblocked
      operationId: sendTeamEmailInvites
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SendEmailInvitesRequest.yml
        required: true
      responses:
        "202":
          description: Accepted
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/threads/{threadId}/invites:
    post:
      tags:
        - Email
      description:
        Send Unblocked invitation emails tailored for a thread to members
        who have not joined Unblocked
      operationId: sendThreadEmailInvites
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/threadId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SendEmailInvitesRequest.yml
        required: true
      responses:
        "202":
          description: Accepted
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/invites/{inviteId}:
    get:
      deprecated: true
      tags:
        - Invite
      description: |
        **Deprecated:** Use `getTeamInviteV2` instead.
      operationId: getTeamInvite
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/inviteId"
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                $ref: ./components/GetTeamInviteResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/invitesV2/{inviteId}:
    get:
      tags:
        - Invite
      description: Get team information associated with a specific invite identifier
      operationId: getTeamInviteV2
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/inviteId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetTeamInviteResponseV2.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/invitees:
    put:
      tags:
        - Invitees
      description: |
        Bulk send Unblocked invitation emails tailored for a team to the invitees, team members who have not joined Unblocked.
        This request tracks the invitee so that the current user will never be prompted to invite these members again.
      operationId: emailInvitees
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: ./components/EmailInvite.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/inviteesV2:
    get:
      tags:
        - Invitees
      description: |
        Get a list of candidate team members to invite to Unblocked.
        The results are sensitive to the authenticated user making the request.

        - The list of invitees will only include members who do not have an Unblocked account.
        - The list of invitees will not include members who have already been invited to the team by this user.
        - The list of invitees will not include members who have already been dismissed by this user.
        - No invitees will be returned unless the current user is actively engaged with the product.

        The list can be optionally parameterized (using the `sociallyConnected` option) to only return users
        who are strongly socially connected to the current user. A prepopulated email may also be included.

        The invitees are sorted in descending order by the strength of the social connection between the current user and the invitee.
      operationId: getInviteesV2
      parameters:
        - $ref: "#/components/parameters/teamId"
        - name: sociallyConnected
          in: query
          description: |
            When true, only returns a list of team members to invite if the current user is strongly socially connected to the invitee.
            When false, the default, returns a list of all team members to invite.
          required: false
          schema:
            type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/EmailInvitee.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/invitees/{teamMemberId}:
    delete:
      tags:
        - Invitees
      description: |
        Dismisses this invitee.
        This request tracks the dismissal so that the current user will never be prompted to invite this member again.
      operationId: dismissInvitee
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/teamMemberId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /assets/teams/{teamId}/{assetId}/multipart:
    post:
      tags:
        - Assets
      description: |
        Initiate a multipart upload request for this asset.
        Returns an uploadId and an array of pre-signed urls for the parts
      operationId: createMultipartUpload
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateMultipartUploadRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CreateMultipartUploadResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /assets/teams/{teamId}/{assetId}/multipart/{uploadId}:
    post:
      tags:
        - Assets
      description: |
        Clients must call this to finalize a multi-part upload
      operationId: completeMultipartUpload
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
        - $ref: "#/components/parameters/uploadId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CompleteMultipartUploadRequest.yml
        required: true
      responses:
        "200":
          description: OK
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Assets
      description: |
        Abort upload. Clients should call this when they hit a non-recoverable failure
      operationId: abortMultipartUpload
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
        - $ref: "#/components/parameters/uploadId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /assets/teams/{teamId}/scm:
    post:
      tags:
        - Assets
      description: |
        Authorizes GitHub asset URLs, by responding with a pre-signed URL for reading the asset.
        If the asset is not found or cannot be authorized, then no authorized asset is returned.
      operationId: authorizeScmAssets
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ScmAssetRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ScmAssetResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /assets/teams/{teamId}/repos/{repoId}/scm:
    post:
      tags:
        - Assets
      description: |
        Authorizes GitHub asset URLs appearing in repo context (`repoId`), by responding with a pre-signed URL for reading the asset.
        If the asset is not found or cannot be authorized, then no authorized asset is returned.
      operationId: authorizeScmRepoAssets
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/repoId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ScmAssetRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ScmAssetResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /assets/teams/{teamId}/{assetId}:
    get:
      tags:
        - Assets
      description: Get asset metadata and a pre-signed url for download
      operationId: getAsset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
        - $ref: "#/components/parameters/amznCloudFrontId"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetAssetResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Assets
      description: Create asset and provides a pre-signed url for upload
      operationId: createAsset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateAssetRequest.yml
        required: true
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: ./components/CreateAssetResponse.yml
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Assets
      description: Delete Asset
      operationId: deleteAsset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/assetId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/slack:
    get:
      tags:
        - Slack
      description: |
        Get Slack teams for this team
      operationId: getSlackTeams
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/SlackTeam.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/slack/{slackId}/state:
    get:
      tags:
        - Slack
      description: |
        Get Slack State
      operationId: getSlackState
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SlackState.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/slack/{slackId}/configurationV3:
    get:
      tags:
        - Slack
      description: |
        Get Slack Configuration
      operationId: getSlackConfigurationV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SlackConfigurationV3.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Slack
      description: |
        Post Slack Configuration
      operationId: postSlackConfigurationV3
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SlackConfigurationV3.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/slack/{slackId}/configurationV4:
    get:
      tags:
        - Slack
      description: |
        Get Slack Configuration
      operationId: getSlackConfigurationV4
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SlackConfigurationV4.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Slack
      description: |
        Post Slack Configuration
      operationId: postSlackConfigurationV4
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SlackConfigurationV4.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/slack/{slackId}/channels/recommended:
    get:
      tags:
        - Slack
      description: |
        Get Recommended Slack Channels
      operationId: getRecommendedSlackChannels
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetRecommendedSlackChannelsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/slack/{slackId}/channels/search:
    post:
      tags:
        - Slack
      description: |
        Search Slack Channels
      operationId: searchSlackChannels
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/slackId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
        - $ref: "#/components/parameters/productAgent"
        - name: sortOrder
          in: query
          description: |
            Sort order. Defaults to memberCount if not provided.
          required: false
          schema:
            $ref: ./components/SlackChannelSortOrder.yml
        - name: sortDirection
          in: query
          description: |
            Sort order direction. Defaults to descascending if not provided.
          required: false
          schema:
            $ref: ./components/SortDirection.yml
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchSlackChannelsRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SearchSlackChannelsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/slack/sendReinstallAppRequest:
    post:
      tags:
        - Slack
      description: |
        Send Slack Reinstall notification to admins
      operationId: sendReinstallAppRequest
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SlackReinstallAppRequestBody.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/availableJiraSites:
    get:
      tags:
        - Jira
      description: |
        List all Jira sites that the current user has access to.  The returned sites may already
        be connected to Unblocked, or may not.  The current user must have a linked Jira
        account.
      operationId: getAvailableJiraSites
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/AvailableJiraSite.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/availableJiraSites/connect:
    post:
      tags:
        - Jira
      description: |
        Connect a Jira site if not already connected.
      operationId: connectJiraSite
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/JiraSiteConnectRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/JiraSite.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/jira:
    get:
      tags:
        - Jira
      description: |
        Get connected Jira sites for this team
      operationId: getJiraSites
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/JiraSite.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/jira/{jiraId}/configuration:
    get:
      tags:
        - Jira
      description: |
        Get Jira Configuration
      operationId: getJiraConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/jiraId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/JiraConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Jira
      description: |
        Post Jira Configuration
      operationId: postJiraConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/jiraId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/JiraConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/jira/{jiraId}/projects:
    get:
      tags:
        - Jira
      description: |
        Get Jira Projects
      operationId: getJiraProjects
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/jiraId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetJiraProjectsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/jiraDataCenter/{jiraDataCenterId}:
    patch:
      tags:
        - Jira Data Center
      description: |
        Patch Jira Data Center installation
      operationId: patchJiraDataCenter
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/jiraDataCenterId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchJiraDataCenterRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/JiraDataCenter.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/confluence:
    get:
      tags:
        - Confluence
      description: |
        Get Confluence sites for this team
      operationId: getConfluenceSites
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ConfluenceSite.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/confluence/{confluenceId}/configuration:
    get:
      tags:
        - Confluence
      description: |
        Get Confluence Configuration
      operationId: getConfluenceConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/confluenceId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ConfluenceConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Confluence
      description: |
        Post Confluence Configuration
      operationId: postConfluenceConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/confluenceId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ConfluenceConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/confluence/{confluenceId}/spaces:
    get:
      tags:
        - Confluence
      description: |
        Get Confluence Spaces
      operationId: getConfluenceSpaces
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/confluenceId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetConfluenceSpacesResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/confluenceDataCenter/{confluenceDataCenterId}:
    patch:
      tags:
        - Confluence Data Center
      description: |
        Patch Confluence Data Center installation
      operationId: patchConfluenceDataCenter
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/confluenceDataCenterId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchConfluenceDataCenterRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ConfluenceDataCenter.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/coda/{codaId}:
    patch:
      tags:
        - Coda
      description: |
        Patch Coda installation
      operationId: patchCoda
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/codaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchCodaRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/coda/{codaId}/configuration:
    get:
      tags:
        - Coda
      description: |
        Get Coda Configuration
      operationId: getCodaConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/codaId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CodaConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    patch:
      tags:
        - Coda
      description: |
        Patch Coda Configuration
      operationId: patchCodaConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/codaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CodaConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/coda/{codaId}/configuration/search:
    post:
      tags:
        - Coda
      description: |
        Search Coda resources.
      operationId: searchCodaResources
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/codaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchCodaResourcesRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SearchCodaResourcesResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/coda/{codaId}/configuration/recommended:
    get:
      tags:
        - Coda
      description: |
        Get Recommended Coda Resources
      operationId: getRecommendedCodaResources
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/codaId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetRecommendedCodaResourcesResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/asana/workspaces:
    get:
      tags:
        - Asana
      description: |
        Get Asana workspaces. Only returns connectable workspaces (i.e. do not map to existing installations)
      operationId: getAsanaWorkspaces
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/AsanaWorkspace.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/asana/{asanaId}:
    patch:
      tags:
        - Asana
      description: |
        Patch Asana installation
      operationId: patchAsana
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/asanaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchAsanaRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/asana/{asanaId}/configuration:
    get:
      tags:
        - Asana
      description: |
        Get Asana Configuration
      operationId: getAsanaConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/asanaId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/AsanaConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    patch:
      tags:
        - Asana
      description: |
        Patch Asana Configuration
      operationId: patchAsanaConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/asanaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchAsanaConfigurationRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/asana/{asanaId}/projects:
    get:
      tags:
        - Asana
      description: |
        Get Asana projects.
      operationId: getAsanaProjects
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/asanaId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/AsanaProjectsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/asana/{asanaId}/projects/search:
    post:
      tags:
        - Asana
      description: |
        Search Asana projects.
      operationId: searchAsanaProjects
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/asanaId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchAsanaProjectsRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/AsanaProjectsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/stackOverflowTeams:
    get:
      tags:
        - Stack Overflow for Teams
      description: |
        Get Stack Overflow for Teams installations for this team
      operationId: getStackOverflowTeams
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/StackOverflowTeam.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/stackOverflowTeams/{stackOverflowTeamId}:
    patch:
      tags:
        - Stack Overflow for Teams
      description: |
        Patch Stack Overflow for Teams installation
      operationId: patchStackOverflowTeam
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/stackOverflowTeamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchStackOverflowTeamRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/StackOverflowTeam.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/webIngestions:
    get:
      tags:
        - Web Ingestion
      description: |
        Get Web Ingestions for this team
      operationId: getWebIngestions
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/WebIngestion.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/webIngestions/{webIngestionId}/configuration:
    get:
      tags:
        - Web Ingestion
      description: |
        Get Web Ingestion Configuration
      operationId: getWebIngestionConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/webIngestionId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/WebIngestionConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Web Ingestion
      description: |
        Patch Web Ingestion Configuration
      operationId: patchWebIngestionConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/webIngestionId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdateWebIngestionConfigurationRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/webIngestion/validateUrl:
    post:
      tags:
        - Web Ingestion
      description: |
        Validate a URL for Web Ingestion
      operationId: postWebIngestionValidateUrl
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/WebIngestionValidationRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/WebIngestionValidationResponse.yml
  /teams/{teamId}/linear:
    get:
      tags:
        - Linear
      description: |
        Get Linear organizations for this team
      operationId: getLinearOrganizations
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/LinearOrganization.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/linear/{linearId}/configuration:
    get:
      tags:
        - Linear
      description: |
        Get Linear Configuration
      operationId: getLinearConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/linearId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/LinearConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Linear
      description: |
        Post Linear Configuration
      operationId: postLinearConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/linearId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/LinearConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/linear/{linearId}/teams:
    get:
      tags:
        - Linear
      description: |
        Get Linear Teams
      operationId: getLinearTeams
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/linearId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetLinearTeamsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/google/{googleDriveId}/connectedTeamMembers:
    get:
      tags:
        - Google Drive
      description: |
        Get team members that are connected to Google
      operationId: getGoogleDriveConnectedTeamMembers
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/TeamMember.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/google/{googleDriveId}/configuration:
    get:
      tags:
        - Google Drive
      description: |
        Get Google Drive Configuration
      operationId: getGoogleDriveConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GoogleDriveConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Google Drive
      description: |
        Post Google Drive Configuration
      operationId: postGoogleDriveConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/GoogleDriveConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/google/{googleDriveId}/configuration/search:
    post:
      tags:
        - Google Drive
      description: |
        Search Google Drive files.
      operationId: searchGoogleDriveFiles
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchGoogleDriveFilesRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SearchGoogleDriveFilesResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/google/{googleDriveId}/googleWorkspaceDrive:
    patch:
      deprecated: true
      tags:
        - Google Drive
      description: |
        Patch Google Workspace Drive.
      operationId: patchGoogleWorkspaceDrive
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchGoogleDriveWorkspace.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GoogleDriveWorkspace.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/googleDriveWorkspace/{googleDriveId}/configuration:
    get:
      tags:
        - Google Drive Workspace
      description: |
        Get Google Drive Configuration
      operationId: getGoogleDriveWorkspaceConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GoogleDriveWorkspaceConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    post:
      tags:
        - Google Drive Workspace
      description: |
        Post Google Drive Workspace Configuration
      operationId: postGoogleDriveWorkspaceConfiguration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/GoogleDriveWorkspaceConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/googleDriveWorkspace/{googleDriveId}/configuration/search:
    post:
      tags:
        - Google Drive Workspace
      description: |
        Search Google Drive Workspace files.
      operationId: searchGoogleDriveWorkspaceFiles
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SearchGoogleDriveFilesRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/SearchGoogleDriveFilesResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/googleDriveWorkspace/{googleDriveId}:
    patch:
      tags:
        - Google Drive Workspace
      description: |
        Patch Google Drive Workspace.
      operationId: patchGoogleDriveWorkspace
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/googleDriveId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchGoogleDriveWorkspace.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GoogleDriveWorkspace.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/customIntegration:
    get:
      tags:
        - Custom Integration
      description: |
        Get Custom Integration
      operationId: getCustomIntegration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CustomIntegration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - Custom Integration
      description: |
        Patch Custom Integration
      operationId: patchCustomIntegration
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/PatchCustomIntegrationRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/scm:
    get:
      tags:
        - SCM Installations
      description: |
        Get an SCM instance.
      operationId: getOrgInstallationScm
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/ScmInstance.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installations/{installationId}/scmRepos:
    get:
      tags:
        - SCM Installations
      description: |
        List SCM repositories for the specified installation.
      operationId: getOrgInstallationScmRepos
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/RepoSelection.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - SCM Installations
      description: |
        Add the specified repos to the set of repos that are ingested for this installation.
        This API is additive only, meaning that existing repos are not removed.
      operationId: patchOrgInstallationScmRepos
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/ScmRepoChanges.yml"
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/scmConfig:
    get:
      tags:
        - SCM Configurations
      description: |
        Get SCM configuration for this SCM instance.
      operationId: getOrgInstallationScmConfig
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ScmConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - SCM Configurations
      description: |
        Update SCM configuration for this SCM instance.
        Use this to edit one or more of the configuration fields.
      operationId: patchOrgInstallationScmConfig
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/ScmConfiguration.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/ciInstallations:
    put:
      tags:
        - CI
      description: >
        Idempotently creates and returns a CI installation with the specified provider type.
        If the CI installation already exists, then the existing installation is returned.

        If a token is specified, then this is added to the newly created CI installation.
      operationId: createCIInstallation
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CIInstallationRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/IntegrationInstallationV3.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/ciTokens:
    get:
      tags:
        - CI
      description: >
        Get CI tokens for a CI installation.
      operationId: getCITokens
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/CIToken.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - CI
      description: >
        Add a CI token for a CI installation.
        The token is encrypted client-side.
      operationId: addCIToken
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CITokenRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CIToken.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/ciTokens/{tokenId}:
    delete:
      tags:
        - CI
      description: |
        Delete a CI token from a CI installation.
        Deleting a token may affect CI Projects.
      operationId: deleteCIToken
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/tokenId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/ciProjects:
    get:
      tags:
        - CI
      description: |
        Get projects available for CI configuration.
      operationId: getCIProjects
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/CIProject.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    patch:
      tags:
        - CI
      description: |
        Update projects available for CI configuration.
      operationId: updateCIProjects
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CIProjectsUpdateRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
    post:
      tags:
        - CI
      description: >
        Find a project available for CI configuration using a slug.
        This does not create any project data on the service.
      operationId: findCIProject
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CIProjectSlugRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CIProject.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/installations/{installationId}/ciScm:
    get:
      tags:
        - CI
      description: |
        Get SCM Instances available for CI configuration.
      operationId: getCIScmInstances
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/CIScmInstance.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installations/{installationId}/ciReports:
    get:
      tags:
        - CI
      description: |
        List recent CI reports for a CI installation.
      operationId: listCIReports
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/limit"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/CIReport.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installations/{installationId}/ciWebhook:
    get:
      tags:
        - CI
      description: |
        Get webhook information for a CI installation.
      operationId: getCIWebhookInfo
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CIWebhookInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/installations/{installationId}/ciScm/{scmInstallationId}/ciRepos:
    get:
      tags:
        - CI
      description: |
        Get CI repository configuration for a CI installation and SCM instance.
      operationId: getCIRepos
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/scmInstallationId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CIRepoConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - CI
      operationId: putCIRepos
      description: |
        Updates CI repository configuration for a CI installation and SCM instance.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/installationId"
        - $ref: "#/components/parameters/scmInstallationId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CIRepoConfigurationRequest.yml
        required: true
      responses:
        "201":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CIRepoConfiguration.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/notion/{notionId}/connectedTeamMembers:
    get:
      tags:
        - Notion
      description: |
        Get team members that are connected to Notion
      operationId: getNotionConnectedTeamMembers
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/notionId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/TeamMember.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/currentPlan:
    get:
      tags:
        - Plans
      operationId: getPlan
      description: |
        The current active plan for the given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Plan.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    put:
      tags:
        - Plans
      operationId: updatePlan
      description: |
        Updates the current plan for the given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdatePlanRequest.yml
        required: true
      responses:
        "201":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Plan.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/currentPlan/extendTrial:
    post:
      tags:
        - Plans
      operationId: extendTrial
      description: |
        Extends the trial period of the current plan for the given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "201":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/Plan.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/currentPlan/requestTrialExtension:
    post:
      tags:
        - Plans
      operationId: requestTrialExtension
      description: |
        Sends a trial extension request for the current plan for the given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/RequestPlanTrialExtensionRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/stripeClientSecretForCardSetup:
    get:
      tags:
        - Plans
      operationId: getStripeClientSecretForCardSetup
      description: |
        Gets a Stripe client secret for card setup for a customer.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/StripeClientSecretForCardSetup.yml"
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/currentPlan/billing:
    get:
      tags:
        - Plans
      operationId: getPlanBilling
      description: |
        Billing details for the current plan of a given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PlanBillingInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/planTemplates:
    get:
      tags:
        - Plans
      operationId: getPlanTemplates
      description: |
        Get display plans templates for a given team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PlanTemplatesResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/invoices:
    get:
      tags:
        - Plans
      operationId: getInvoices
      description: |
        Returns a list of historical invoices for the given team.

        This operation supports pagination using `limit` and `cursor` parameters.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/limit"
        - $ref: "#/components/parameters/before"
        - $ref: "#/components/parameters/after"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/BillingInvoice.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /teams/{teamId}/paymentInvites/{paymentInviteId}:
    post:
      tags:
        - PaymentInvites
      description: |
        Create a payment invite for a given plan template.
      operationId: createPaymentInvite
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/paymentInviteId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreatePaymentInviteRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CreatePaymentInviteResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/paymentInvites/{paymentInviteId}/invite:
    put:
      tags:
        - PaymentInvites
      operationId: sendPaymentInvite
      description: |
        Send payment invite email.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/paymentInviteId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SendPaymentInviteRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /paymentInvites/{paymentInviteId}:
    get:
      tags:
        - PaymentInvites
      operationId: getPaymentInviteMetadata
      description: |
        Returns all relevant information to render an unauthed payment page for a given payment invite id.
      parameters:
        - $ref: "#/components/parameters/paymentInviteId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/PaymentInviteMetadataResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
    put:
      tags:
        - PaymentInvites
      operationId: updatePlanForInvite
      description: |
        Unauthed endpoint that updates the plan for the given payment invite.
      parameters:
        - $ref: "#/components/parameters/paymentInviteId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/UpdatePlanRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/registeredDomains:
    post:
      tags:
        - Registered Domains
      operationId: registerDomain
      description: |
        Register a new domain. This will create a new `RegisteredDomain` this is initially unverified.
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              properties:
                domainName:
                  description: Domain to register.
                  type: string
              required:
                - domainName
        required: true
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: ./components/RegisteredDomain.yml
        default:
          $ref: "#/components/responses/Default"
    get:
      tags:
        - Registered Domains
      operationId: listRegisteredDomains
      description: |
        List all registered domains for a team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/RegisteredDomain.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/registeredDomains/{registeredDomainId}:
    get:
      tags:
        - Registered Domains
      operationId: getRegisteredDomain
      description: |
        Get a registered domain.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/registeredDomainId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/RegisteredDomain.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    put:
      tags:
        - Registered Domains
      operationId: verifyDomain
      description: |
        Attempt to verify the specified domain.
        The verification process will be initiated and the result (`isVerified`) will be returned in the response.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/registeredDomainId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/RegisteredDomain.yml
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Registered Domains
      operationId: deleteDomain
      description: |
        Delete a registered domain.
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/registeredDomainId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /auth/ssoProviders:
    post:
      tags:
        - SAML Auth
      description: |
        Retrieves the SSO provider for the specified email address.
        The service searches for a registered SSO provider matching the domain part of the email.
        If a matching provider is found, it is returned in the response.
        If no provider is found, a 404 is returned.
      operationId: findSsoProvider
      parameters:
        - name: clientSecret
          in: query
          description: Client secret used to associate preauth token to nonce
          required: false
          schema:
            $ref: ./components/ApiResourceId.yml
        - name: agentType
          in: query
          description: Client hint to know what to render at the end of the auth flow
          required: false
          schema:
            $ref: ./components/AgentType.yml
        - name: overrideRedirectUrl
          in: query
          description: Override the redirect URL. Useful for DEV environment purposes.
          required: false
          schema:
            type: string
        - $ref: "#/components/parameters/clientState"
      requestBody:
        content:
          application/json:
            schema:
              properties:
                email:
                  description: Email address to search for a matching SSO provider.
                  type: string
                  format: email
              required:
                - email
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/SSOProvider.yml"
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /teams/{teamId}/ssoProviders:
    get:
      tags:
        - Single Sign On
      operationId: listSsoProviders
      description: |
        List the available SSO providers for a team.
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: "./components/Provider.yml"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/saml:
    get:
      tags:
        - Single Sign On
      operationId: getSaml
      description: |
        Returns the SAML Settings for a team
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/SamlSettingsResponse.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    delete:
      tags:
        - Single Sign On
      description: Delete SAML
      operationId: deleteSaml
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/samlV2:
    patch:
      tags:
        - Single Sign On
      description: Configure the SAML settings for a team
      operationId: updateSamlV2
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/UpdateSamlSettingsRequest.yml"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/SamlSettingsResponse.yml"
        default:
          $ref: "#/components/responses/Default"
  /hooks/buildkite:
    post:
      tags:
        - CI Hooks
      description: |
        Webhook events from Buildkite.

        See more information:
        https://buildkite.com/docs/apis/webhooks
      operationId: buildKiteEvent
      parameters:
        - name: X-Buildkite-Event
          in: header
          description: |
            Type of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: build.scheduled
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /hooks/circleci:
    post:
      tags:
        - CI Hooks
      description: |
        Webhook events from CircleCI.

        See more information:
        https://circleci.com/docs/api/v2/#webhooks
      operationId: circleciEvent
      parameters:
        - name: Circleci-Event-Type
          in: header
          description: |
            Type of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: job-completed
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /hooks/coda:
    get:
      tags:
        - CodaHooks
      description: |
        Webhook handshake events from Coda. Events are sent to this endpoint when a webhook is created.
      operationId: codaWebhookHandshake
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
    post:
      tags:
        - CodaHooks
      description: |
        Event from Coda hooks.
      operationId: codaEvent
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /hooks/forge:
    post:
      tags:
        - AtlassianForgeHooks
      description: |
        Event from Forge apps
      operationId: forgeEvent
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/forge/token:
    post:
      tags:
        - AtlassianForgeHooks
      description: |
        System auth token event from Forge apps
      operationId: forgeTokenEvent
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/linear:
    post:
      tags:
        - LinearHooks
      description: |
        Event from Linear hooks.
      operationId: linearEvent
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/notion:
    post:
      tags:
        - NotionHooks
      description: |
        Event from Notion hooks.
      operationId: notionEvent
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /hooks/transcription:
    post:
      tags:
        - TranscriptionHooks
      description: |
        Event from Transcription hooks.
      operationId: transcriptionEvent
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/slack:
    post:
      tags:
        - SlackHooks
      description: |
        Event from Slack.

        Need to handle webhook verification as well.
        https://api.slack.com/events/url_verification
      operationId: slackEvent
      parameters:
        - name: X-Slack-Request-Timestamp
          in: header
          description: |
            This header is sent to identify when the webhook was sent and is used during slack signature validation.
          required: true
          schema:
            type: string
        - name: X-Slack-Signature
          in: header
          description: |
            This signature is generated using the client signing secret of the Slack app, the body and the timestamp.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/stripe:
    post:
      tags:
        - StripeHooks
      description: |
        Event from Stripe.

        Need to handle webhook verification as well.
        https://docs.stripe.com/webhooks/signature
      operationId: stripeEvent
      parameters:
        - name: Stripe-Signature
          in: header
          description: |
            This signature is generated using the webhook signing secret found under https://dashboard.stripe.com/workbench/webhooks
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: [ ]
  /hooks/bitbucket:
    post:
      tags:
        - ScmHooks
      description: |
        Event from Bitbucket Cloud.

        See delivery headers:
        https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#delivery-headers
      operationId: bitbucketEvent
      parameters:
        - name: X-Event-Key
          in: header
          description: |
            Type of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: pullrequest:comment_created
        - name: X-Hook-Uuid
          in: header
          description: |
            The UUID of the installed webhook.
          required: true
          schema:
            type: string
          example: f618ec66-5287-4b61-9989-58b41dcfe596
        - name: X-Request-Uuid
          in: header
          description: |
            A GUID to identify the delivery.
          required: true
          schema:
            type: string
          example: 1deb893d-1364-401b-bc1e-3bc4d73aa9c6
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/bitbucketDataCenter:
    post:
      tags:
        - ScmHooks
      description: |
        Event from Bitbucket Data Center.

        See: https://confluence.atlassian.com/bitbucketserver/manage-webhooks-938025878.html
      operationId: bitbucketDataCenterEvent
      parameters:
        - name: X-Event-Key
          in: header
          description: |
            Type of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: project:modified
        - name: X-Request-Id
          in: header
          description: |
            A GUID to identify the delivery.
          required: true
          schema:
            type: string
          example: da9f89de-6505-471a-8538-9a81dfa4146d
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/github:
    post:
      tags:
        - ScmHooks
      description: |
        Event from GitHub.

        See delivery headers:
        https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#delivery-headers
      operationId: githubEvent
      parameters:
        - name: X-GitHub-Delivery
          in: header
          description: |
            A GUID to identify the delivery.
          required: true
          schema:
            type: string
          example: 302cb350-95f7-11ed-9068-163c238c3ef7
        - name: X-GitHub-Event
          in: header
          description: |
            Name of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: pull_request
        - name: X-GitHub-Hook-ID
          in: header
          description: |
            External ID of the hook configuration resource in GitHub.
            Always the same ID for a given GitHub App.
            https://docs.github.com/en/rest/apps/webhooks
          required: true
          schema:
            type: string
          example: 339231948
        - name: X-GitHub-Hook-Installation-Target-ID
          in: header
          description: |
            GitHub App ID.
          required: true
          schema:
            type: string
          example: 166219
        - name: X-GitHub-Hook-Installation-Target-Type
          in: header
          description: |
            Always `integration` for GitHub Apps.
          required: true
          schema:
            type: string
          example: integration
        - name: X-Hub-Signature-256
          in: header
          description: |
            This header is sent if the webhook is configured with a secret.
            This is the HMAC hex digest of the request body,
            and is generated using the SHA-256 hash function and the secret as the HMAC key.
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/githubEnterprise:
    post:
      tags:
        - ScmHooks
      description: |
        Event from GitHub Enterprise.

        See delivery headers:
        https://docs.github.com/en/developers/webhooks-and-events/webhooks/webhook-events-and-payloads#delivery-headers
      operationId: githubEnterpriseEvent
      parameters:
        - name: X-GitHub-Delivery
          in: header
          description: |
            A GUID to identify the delivery.
          required: true
          schema:
            type: string
          example: 302cb350-95f7-11ed-9068-163c238c3ef7
        - name: X-GitHub-Event
          in: header
          description: |
            Name of the event that triggered the delivery.
          required: true
          schema:
            type: string
          example: pull_request
        - name: X-GitHub-Hook-ID
          in: header
          description: |
            External ID of the hook configuration resource in GitHub.
            Always the same ID for a given GitHub App.
            https://docs.github.com/en/rest/apps/webhooks
          required: true
          schema:
            type: string
          example: 339231948
        - name: X-GitHub-Hook-Installation-Target-ID
          in: header
          description: |
            GitHub App ID.
          required: true
          schema:
            type: string
          example: 166219
        - name: X-GitHub-Hook-Installation-Target-Type
          in: header
          description: |
            Always `integration` for GitHub Apps.
          required: true
          schema:
            type: string
          example: integration
        - name: X-GitHub-Enterprise-Host
          in: header
          description: |
            The GitHub Enterprise server hostname.
            Only specified when the origin of the request is a GitHub Enterprise server.
          required: true
          schema:
            type: string
          example: ghe.secops.getunblocked.com
        - name: X-GitHub-Enterprise-Version
          in: header
          description: |
            The GitHub Enterprise server version.
            Only specified when the origin of the request is a GitHub Enterprise server.
          required: true
          schema:
            type: string
          example: 3.7.2
        - name: X-Hub-Signature-256
          in: header
          description: |
            This header is sent if the webhook is configured with a secret.
            This is the HMAC hex digest of the request body,
            and is generated using the SHA-256 hash function and the secret as the HMAC key.
          required: false
          schema:
            type: string
        - name: X-Hub-Signature
          in: header
          description: |
            This header is sent if the webhook is configured with a secret.
            This is the HMAC hex digest of the request body,
            and is generated using the SHA-1 hash function and the secret as the HMAC key.
          required: false
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /hooks/gitlab:
    post:
      tags:
        - ScmHooks
      description: |
        Event from GitLab Cloud.

        See delivery headers:
        https://docs.gitlab.com/ee/user/project/integrations/webhooks.html#delivery-headers
      operationId: gitlabEvent
      parameters:
        - name: X-Gitlab-Instance
          in: header
          description: |
            Hostname of the GitLab instance that sent the webhook.
          required: true
          schema:
            type: string
          example: https://gitlab.com
        - name: X-Gitlab-Webhook-UUID
          in: header
          description: |
            Unique ID per webhook.
          required: true
          schema:
            type: string
          example: 02affd2d-2cba-4033-917d-ec22d5dc4b38
        - name: X-Gitlab-Event
          in: header
          description: |
            Name of the webhook type. Corresponds to event types but in the format "<EVENT> Hook".
            See https://docs.gitlab.com/ee/user/project/integrations/webhook_events.html
          required: true
          schema:
            type: string
          example: Push Hook
        - name: X-Gitlab-Event-UUID
          in: header
          description: |
            Unique ID per webhook that is not recursive.
            A hook is recursive if triggered by an earlier webhook that hit the GitLab instance.
            Recursive webhooks have the same value for this header.
          required: true
          schema:
            type: string
          example: 13792a34-cac6-4fda-95a8-c58e00a3954e
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "202":
          description: Accepted
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /mlRouters/{mlRouterId}/events:
    post:
      tags:
        - ML Routers
      description: |
        Receives events for a given route
      operationId: routerEvent
      parameters:
        - $ref: "#/components/parameters/mlRouterId"
      requestBody:
        content:
          application/json:
            schema:
              type: string
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /configs:
    get:
      tags:
        - Config
      description: |
        Get remote controlled client config for the currently authorized user.
        This API operation takes no explicit parameters.
        The remote config service implicitly uses the authorized
        _person_, _identity_, and _team(s)_ to control behaviour.

        This API is user-centric, not team-centric in order to provide a consistent experience to the authorized user.
        The consequence for a user who is a current member of multiple teams is that property conflicts can occur
        for team properties. The remote config service resolves these conflicts deterministically.
      operationId: getGlobalConfig
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ClientConfig.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /meta:
    get:
      tags:
        - Config
      description: |
        Metadata config.

        Vends service IP addresses to allow list in third-party corporate firewalls.
        All outbound service calls originate from these IP addresses.
      operationId: getMetaConfig
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/MetaConfig.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /demo/signup:
    post:
      tags:
        - Demo
      description: Signup for a demo.
      operationId: postDemoSignup
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/DemoSignupRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security: []
  /download/latest/hub:
    get:
      tags:
        - Versions
      description: Redirect to the latest hub client download url
      operationId: getLatestHub
      responses:
        "302":
          description: Found
          headers:
            Location:
              description: The location of the latest hub client download url
              required: true
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /download/latest/desktop:
    get:
      tags:
        - Versions
      description: Redirect to the latest desktop client download url
      operationId: getLatestDesktop
      parameters:
        - $ref: "#/components/parameters/clientPlatform"
        - $ref: "#/components/parameters/clientArchitecture"
      responses:
        "302":
          description: Found
          headers:
            Location:
              description: The location of the latest desktop client download url
              required: true
              schema:
                type: string
        default:
          $ref: "#/components/responses/Default"
      security: []
  /versionInfo:
    get:
      tags:
        - Versions
      description: Get all non-obsolete client version info.
      operationId: getVersionInfo
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/VersionInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /versionInfo/latest:
    get:
      tags:
        - Versions
      description: Get latest client version info
      operationId: getLatestVersionInfo
      parameters:
        - $ref: "#/components/parameters/productSha"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetVersionInfoResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
  /versionInfo/public:
    get:
      tags:
        - Versions
      description: Get latest public client version info
      operationId: getLatestPublicVersionInfo
      parameters:
        - $ref: "#/components/parameters/productSha"
        - $ref: "#/components/parameters/productAgent"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetVersionInfoResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/keys:
    get:
      tags:
        - Keys
      description: List existing API keys
      operationId: getApiKeys
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ApiKeyMetadata.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Keys
      description: Create an API key used to access Unblocked's public APIs
      operationId: createApiKey
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateApiKeyRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CreateApiKeyResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/keys/{apiKeyId}:
    delete:
      tags:
        - Keys
      description: Delete API key
      operationId: deleteApiKey
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/apiKeyId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/scimKeys:
    get:
      tags:
        - Keys
      description: List all SCIM keys.
      operationId: getScimKeys
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/ApiKeyMetadata.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: []
        - readOnlyBearerAuth: []
    post:
      tags:
        - Keys
      description: Create a SCIM key used for User and Group provisioning.
      operationId: createScimKey
      parameters:
        - $ref: "#/components/parameters/teamId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateApiKeyRequest.yml
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/CreateApiKeyResponse.yml
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/scimKeys/{scimKeyId}:
    delete:
      tags:
        - Keys
      description: Delete a SCIM key.
      operationId: deleteScimKey
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/scimKeyId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"

  /teams/{teamId}/analytics/stats/timeSaved:
    get:
      tags:
        - Single Stats
      description: |
        Single stat value representing an estimate of the time saved by answers.
      operationId: statsTimeSaved
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: number
                format: float
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/timeSeries/answers:
    get:
      tags:
        - Time Series
      description: |
        Multiple time-series counts of all answers in the organization.
        Series are cut by feedback; when multiple feedback is recorded for an answer, the most recent feedback is chosen.
        Series labels are of type `FeedbackType`.
        Includes all answers from all sources, including auto-answers.
      operationId: timeSeriesAnswers
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/dataResolution"
        - $ref: "#/components/parameters/timeZone"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/DataTimeSeries.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/timeSeries/ciReports:
    get:
      tags:
        - Time Series
      description: |
        Multiple time-series counts of all PRs with CI reports in the organization.
        Series are cut by feedback.
        Series labels are of type `FeedbackType`.
      operationId: timeSeriesCIReports
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/dataResolution"
        - $ref: "#/components/parameters/timeZone"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/DataTimeSeries.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/timeSeries/members:
    get:
      tags:
        - Time Series
      description: |
        Single time-series counts of lifetime members of the organization.
        Series labels are of type `DataMemberLabels`.
        Includes all people who have created Unblocked account from all applicable identity providers.
      operationId: timeSeriesMembers
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/dataResolution"
        - $ref: "#/components/parameters/timeZone"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/DataTimeSeries.yml"
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/segments/answersByMember:
    get:
      tags:
        - Segments
      description: |
        Answers segmented by member.
        Includes all answers from all sources, including auto-answers.
        Limit defaults to 5 items.
      operationId: segmentAnswersByMember
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/limit"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/DataSegmentByMember.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/segments/answersByChannel:
    get:
      tags:
        - Segments
      description: |
        Answers segmented by channel.
        Includes all answers from all sources, including auto-answers.
        Limit defaults to 5 items.
      operationId: segmentAnswersByChannel
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/limit"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/DataSegmentByChannel.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/segments/ciReportsByRepo:
    get:
      tags:
        - Segments
      description: |
        CI reports segmented by repo.
        Limit defaults to 5 items.
      operationId: segmentCIReportsByRepo
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
        - $ref: "#/components/parameters/limit"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/DataSegmentByRepo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]

  /teams/{teamId}/analytics/segments/answersByClient:
    get:
      tags:
        - Segments
      description: |
        Answers segmented by client (`ProductAgent`).
        Includes all answers from all sources, including auto-answers.
      operationId: segmentAnswersByClient
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataDuration"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/DataSegmentByClient.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/dataSourcePresets:
    get:
      tags:
        - Data Source Presets
      description: |
        Get existing data source presets
      operationId: getDataSourcePresets
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/DataSourcePresetSummary.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/dataSourcePresets/{dataSourcePresetId}:
    get:
      tags:
        - Data Source Presets
      description: |
        Get existing data source preset by ID
      operationId: getDataSourcePreset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataSourcePresetId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/DataSourcePreset.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    put:
      tags:
        - Data Source Presets
      description: |
        Creates or updates a data source preset
      operationId: updateDataSourcePreset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataSourcePresetId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/UpdateDataSourcePresetRequest.yml"
        required: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: "./components/DataSourcePreset.yml"
        default:
          $ref: "#/components/responses/Default"
    delete:
      tags:
        - Data Source Presets
      description: Remove a data source preset.
      operationId: removeDataSourcePreset
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/dataSourcePresetId"
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /teams/{teamId}/dataSourcePresets/installations:
    get:
      tags:
        - Data Source Presets
      description: |
        Get the available installations for data source presets.
      operationId: getAvailableDataSourcePresetInstallations
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: ./components/AvailableDataSourcePresetInstallation.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/dataSourcePresets/avatars:
    get:
      tags:
        - Data Source Presets
      description: |
        Get the available avatars for data source presets.
      operationId: getAvailableDataSourcePresetAvatars
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetDataSourcePresetAvatarsResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /session/action:
    put:
      tags:
        - Metrics
      description: |
        Capture unauthenticated user action
      operationId: triggerSessionAction
      parameters:
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SessionActionRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security: []
  /session/referral:
    put:
      tags:
        - Metrics
      description: |
        Capture UTM Campaign information for a session
      operationId: triggerSessionReferral
      parameters:
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/SessionReferralRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
      security: []
  /teams/{teamId}/productFeedback:
    get:
      tags:
        - Product Feedback
      description: |
        Get the product feedback configuration
      operationId: getProductFeedbackConfig
      parameters:
        - $ref: "#/components/parameters/teamId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/ProductFeedbackConfig.yml
        default:
          $ref: "#/components/responses/Default"
    post:
      tags:
        - Product Feedback
      description: |
        Provide a response to a product feedback question
      operationId: postProductFeedback
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/productAgent"
      requestBody:
        content:
          application/json:
            schema:
              $ref: "./components/ProductFeedbackResponse.yml"
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
  /mcp/tools:
    get:
      tags:
        - MCP
      description: |
        Get available MCP tools. Optionally specify the client making the request using the mcpClient query parameter.
      operationId: getMcpTools
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/McpToolInfo.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
  /teams/{teamId}/mcp/query/{mcpQueryId}:
    get:
      tags:
        - MCP
      description: |
        Get an MCP query.
      operationId: getMcpQuery
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/mcpQueryId"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: ./components/GetMcpQueryResponse.yml
        default:
          $ref: "#/components/responses/Default"
      security:
        - bearerAuth: [ ]
        - readOnlyBearerAuth: [ ]
    put:
      tags:
        - MCP
      description: |
        Create an MCP query.
      operationId: createMcpQuery
      parameters:
        - $ref: "#/components/parameters/teamId"
        - $ref: "#/components/parameters/mcpQueryId"
      requestBody:
        content:
          application/json:
            schema:
              $ref: ./components/CreateMcpQueryRequest.yml
        required: true
      responses:
        "204":
          description: No Content
        default:
          $ref: "#/components/responses/Default"
components:
  schemas:
    OAuthState:
      $ref: ./components/OAuthState.yml
  responses:
    Default:
      description: Default error response.
      content:
        application/problem+json:
          schema:
            $ref: ./components/ApiError.yml
  parameters:
    mlRouterId:
      name: mlRouterId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    teamId:
      name: teamId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    dataDuration:
      name: dataDuration
      in: query
      required: true
      schema:
        $ref: ./components/DataDuration.yml
    dataResolution:
      name: dataResolution
      in: query
      required: true
      schema:
        $ref: ./components/DataResolution.yml
    dataMemberLabels:
      name: dataMemberLabels
      in: query
      required: false
      schema:
        $ref: "./components/DataMemberLabels.yml"
    timeZone:
      name: timeZone
      description: |
        IANA time zone name.
        See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/DateTimeFormat/resolvedOptions
      example: America/Los_Angeles
      in: query
      required: true
      schema:
        type: string
    groupId:
      name: groupId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    registeredDomainId:
      name: registeredDomainId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    teamMemberId:
      name: teamMemberId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    messageId:
      name: messageId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    reactionId:
      name: reactionId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    threadId:
      name: threadId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    pullRequestId:
      name: pullRequestId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    pullRequestBlockId:
      name: pullRequestBlockId
      in: path
      description: Pull request block resource ID.
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    archivedReferenceId:
      name: archivedReferenceId
      in: path
      description: Archived reference resource ID.
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    inviteId:
      name: inviteId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    fullyQualifiedScmInstallationKey:
      name: fullyQualifiedScmInstallationKey
      in: path
      required: true
      schema:
        type: string
        description: |
          Stable opaque ID encapsulating a globally unique installation.
          This is not a UUID.
    installationId:
      name: installationId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    botIdentityId:
      name: botIdentityId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    repoId:
      name: repoId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    repoIds:
      name: repoIds
      in: query
      description: |
        Comma separated list of Repo IDs.
        When null implies all repos.
        When empty implies no repos.
      required: false
      schema:
        $ref: ./components/ApiResourceListIds.yml
    teamMemberIds:
      name: teamMemberIds
      in: query
      description: |
        Comma separated list of Team Member IDs.
        Null or empty implies all team members.
      required: false
      schema:
        $ref: ./components/ApiResourceListIds.yml
    slackId:
      name: slackId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    jiraId:
      name: jiraId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    jiraDataCenterId:
      name: jiraDataCenterId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    confluenceId:
      name: confluenceId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    confluenceDataCenterId:
      name: confluenceDataCenterId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    linearId:
      name: linearId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    notionId:
      name: notionId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    googleDriveId:
      name: googleDriveId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    stackOverflowTeamId:
      name: stackOverflowTeamId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    codaId:
      name: codaId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    asanaId:
      name: asanaId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    webIngestionId:
      name: webIngestionId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    enterpriseProviderId:
      name: enterpriseProviderId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    apiKeyId:
      name: apiKeyId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    scimKeyId:
      name: scimKeyId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    paymentInviteId:
      name: paymentInviteId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    dataSourcePresetId:
      name: dataSourcePresetId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    redirectUrl:
      name: redirectUrl
      in: query
      required: false
      schema:
        $ref: ./components/Url.yml
    overrideRedirectUrl:
      name: overrideRedirectUrl
      description: |
        Override the redirect URL for the OAuth flow to a localhost URL.
        This is used for development testing purposes only.
      in: query
      required: false
      schema:
        $ref: ./components/Url.yml
    connectingIdentity:
      name: connectingIdentity
      in: query
      required: true
      schema:
        $ref: "./components/ApiResourceId.yml"
    manifestRedirectUrl:
      name: manifestRedirectUrl
      in: query
      required: false
      schema:
        $ref: ./components/Url.yml
    dataIntegrationRedirectUrl:
      name: dataIntegrationRedirectUrl
      in: query
      required: false
      schema:
        $ref: ./components/Url.yml
    clientState:
      name: clientState
      in: query
      required: false
      description: |
        Opaque client data embedded in the state parameter in the /dashboard/login/exchange redirect.
        Client can use this to restore context after an oauth operation. Base64 encoded.
      schema:
        type: string
    provider:
      name: provider
      in: query
      description: |
        Provider to filter by.
        When not specified, all providers are returned.
      required: false
      schema:
        $ref: ./components/Provider.yml
    limit:
      name: limit
      in: query
      description: |
        Limit used to constrain results of list operations.
        When not specified a default limit is used.
        A maximum limit is applied to the results, so the server may respond with fewer results than requested;
        clients must not use this a signal that this is the final page of results.
      required: false
      schema:
        type: integer
        format: int32
    before:
      name: before
      in: query
      description: |
        Opaque cursor to be used to get previous items from the collection.
        Cursors are stateless and so they never expire.
      required: false
      schema:
        $ref: ./components/Cursor.yml
    after:
      name: after
      in: query
      description: |
        Opaque cursor to be used to get next items from the collection.
        Cursors are stateless and so they never expire.
      required: false
      schema:
        $ref: ./components/Cursor.yml
    assetId:
      name: assetId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    uploadId:
      name: uploadId
      in: path
      required: true
      schema:
        type: string
    amznCloudFrontId:
      name: amznCloudFrontId
      in: query
      description: |
        CloudFront will provide this as an id that needs to be encoded in signed url request
      required: false
      schema:
        type: string
    scmInstallationId:
      name: scmInstallationId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    tokenId:
      name: tokenId
      in: path
      description: >
        CI token ID.
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    mcpQueryId:
      name: mcpQueryId
      in: path
      required: true
      schema:
        $ref: ./components/ApiResourceId.yml
    IfModifiedSince:
      name: X-Unblocked-If-Modified-Since
      in: header
      description: |
        When specified, the server responds with the requested resource and a 200 status,
        only if the resource has been last modified since the specified date.
        If the resource has not been modified since the specified date,
        then the server responds without a body and a 304 status.
      required: false
      schema:
        type: string
    productSha:
      name: X-Unblocked-Product-Sha
      in: header
      description: |
        The client build sha1 (full string)
      required: false
      schema:
        $ref: ./components/SHA.yml
    productAgent:
      name: X-Unblocked-Product-Agent
      in: header
      description: |
        The client agent type. See: '#/components/schemas/AgentType'
      required: false
      schema:
        type: string
    feedbackTypeFilter:
      name: feedbackTypeFilter
      in: query
      required: false
      schema:
        type: array
        items:
          $ref: ./components/FeedbackTypeFilter.yml
    sessionIdentifier:
      name: sessionIdentifier
      in: query
      required: false
      schema:
        $ref: ./components/SessionId.yml
    clientPlatform:
      name: platform
      in: query
      required: true
      schema:
        $ref: ./components/ClientPlatform.yml
    clientArchitecture:
      name: arch
      in: query
      required: true
      schema:
        $ref: ./components/ClientArchitecture.yml
    ideActivityEvent:
      name: ideActivityEvent
      in: path
      required: true
      schema:
        $ref: ./components/IdeActivityEvent.yml

  securitySchemes:
    bearerAuth:
      type: http
      description: Full access token.
      scheme: bearer
      bearerFormat: JWT
    readOnlyBearerAuth:
      type: http
      description: Read-only access token.
      scheme: bearer
      bearerFormat: JWT
    exchangeAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    refreshAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
    ciBearerAuth:
      type: http
      description: |
        Access token for CI preference related endpoints only.
        Used to allow very limited access to CI preferences from the CI opt-out flow.
      scheme: bearer
      bearerFormat: JWT

x-tagGroups:
  - name: Analytics
    tags:
      - Segments
      - Single Stats
      - Time Series
  - name: Auth
    tags:
      - Auth
      - AuthInstall
      - SAML Auth
      - Single Sign On
  - name: Core
    tags:
      - Bots
      - Demo
      - Data Source Presets
      - Groups
      - Keys
      - Persons
      - Registered Domains
      - Repos
      - TeamMembers
      - Teams
  - name: Content
    tags:
      - Archived References
      - Assets
      - Insights
      - Messages
      - PullRequests
      - SourceMarks
      - Threads
      - Unreads
  - name: Integrations
    tags:
      - Integrations
      - Installations
      - CI
      - Coda
      - Confluence
      - Confluence Data Center
      - Google Drive
      - Jira
      - Jira Data Center
      - Linear
      - Notion
      - SCM Configurations
      - SCM Enterprise Integrations
      - SCM Installations
      - Slack
      - Stack Overflow for Teams
      - Web Ingestion
  - name: Management
    tags:
      - Config
      - Versions
  - name: MCP
    tags:
      - MCP
  - name: Notifications
    tags:
      - Email
      - Invite
      - Invitees
  - name: Pricing and Plan
    tags:
      - PaymentInvites
      - Plans
  - name: Real-time Update
    tags:
      - Push
  - name: Search
    tags:
      - Search
  - name: Telemetry
    tags:
      - Health
      - Logs
      - Metrics
  - name: Webhooks
    tags:
      - AtlassianForgeHooks
      - CI Hooks
      - CodaHooks
      - LinearHooks
      - ML Routers
      - NotionHooks
      - ScmHooks
      - SlackHooks
      - StripeHooks
      - TranscriptionHooks
  - name: Diagrams
    tags:
      - Diagram
