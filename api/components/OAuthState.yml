x-zally-ignore: [S005]
type: object
properties:
  nonce:
    type: string
    format: uuid
  provider:
    $ref: "./Provider.yml"
  state:
    description: |
      Opaque data embedded in the state parameter in the /dashboard/login/exchange redirect.
      Base64 encoded
    type: string
  clientState:
    description: |
      Opaque client data embedded in the state parameter in the /dashboard/login/exchange redirect.
      Dashboard can use this to redirect after successful login. Base64 encoded
    type: string
  enterpriseProviderId:
    description: |
      Enterprise provider ID, which is present only for enterprise providers.
    type: string
    format: uuid
  ssoProviderId:
    description: |
      SSO provider ID, which is present only for SSO providers.
    type: string
    format: uuid
  targetIdentityId:
    description: |
      When specified, the OAuth flow will succeed if and only if the user connects the target identity.
      Otherwise, if not specified, the user can authenticate with any identity.
    type: string
    format: uuid
  skipAccountLinking:
    description: |
      When specified, the OAuth flow will skip the account connection step.
      This means that a person will not be created, and the identity will not be linked to any person.
      This is useful when the OAuth flow is used for authentication only.
    type: boolean
